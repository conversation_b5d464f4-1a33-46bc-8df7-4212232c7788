package com.jp.med.ecs.modules.common.service.write.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.audit.AuditDetail;
import com.jp.med.common.enums.ecs.ReimTypeEnum;
import com.jp.med.common.enums.ecs.ShareTypeEnum;
import com.jp.med.common.feign.AppMessageFeignService;
import com.jp.med.ecs.modules.common.service.write.EcsAuditWriteService;
import com.jp.med.common.dto.ecs.drug.EcsDrugReimDetaiDto;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsDrugReimDetaiReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.read.EcsStoinReadMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsDrugReimDetaiWriteMapper;
import com.jp.med.ecs.modules.drugMgt.mapper.write.EcsStoinWriteMapper;
import com.jp.med.common.vo.ecs.drug.EcsDrugReimDetaiVo;
import com.jp.med.ecs.modules.reimMgt.constant.EcsConst;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimDetailDto;
import com.jp.med.ecs.modules.reimMgt.dto.EcsReimTravelApprDto;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimItemDetail;
import com.jp.med.ecs.modules.reimMgt.entity.EcsReimSubsItemDetail;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimDetailReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsReimTravelApprReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsInvoRcdWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimDetailWriteMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimTravelApprWriteMapper;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimDetailVo;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/10/9 14:29
 * @description:
 */
@Service
@Transactional
public class EcsAuditWriteServiceImpl implements EcsAuditWriteService {

    @Autowired
    private AppMessageFeignService appMessageFeignService;

    @Autowired
    private EcsReimDetailReadMapper ecsReimDetailReadMapper;

    @Autowired
    private EcsReimDetailWriteMapper ecsReimDetailWriteMapper;

    @Autowired
    private EcsReimTravelApprWriteMapper ecsReimTravelApprWriteMapper;

    @Autowired
    private EcsReimTravelApprReadMapper ecsReimTravelApprReadMapper;

    @Autowired
    private EcsInvoRcdWriteMapper ecsInvoRcdWriteMapper;

    @Autowired
    private EcsStoinReadMapper ecsStoinReadMapper;
    
    @Autowired
    private EcsDrugReimDetaiReadMapper ecsDrugReimDetaiReadMapper;

    @Autowired
    private EcsDrugReimDetaiWriteMapper ecsDrugReimDetaiWriteMapper;

    @Autowired
    private EcsStoinWriteMapper ecsStoinWriteMapper;

    @Override
    public void complete(AuditDetail dto) {
        System.out.println("====" + dto.getAuditResult());
        String bchno = dto.getAuditBchno();
        if (bchno.startsWith(AuditConst.ECS_FYBX)) {        //报销
            if (StringUtils.isNotEmpty(dto.getAuditResult()) &&
                    StringUtils.isNotEmpty(bchno)) {
                if (AuditConst.STATE_COMPLETE.equals(dto.getAuditResult())) {       //审核成功
                    // 1.查询当前批次号下所有发票记录表记录id
                    EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
                    ecsReimDetailDto.setAuditBchno(bchno);
                    List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryList(ecsReimDetailDto);
                    //更新发票状态
                    if (CollectionUtil.isNotEmpty(ecsReimDetailVos)) {
                        updateInvoStatus(bchno,ecsReimDetailVos.get(0),MedConst.TYPE_2);
                    }

                    //更新当前付款状态为已付款
                    LambdaUpdateWrapper<EcsReimDetailDto> wrapper = Wrappers.lambdaUpdate();
                    wrapper.eq(EcsReimDetailDto::getId,ecsReimDetailVos.get(0).getId());
                    //如果是零星采购或者物资采购，则只更新busstas为审核通过(此时未上传付款文件)
                    if (StringUtils.equals(ecsReimDetailVos.get(0).getType(), ReimTypeEnum.PURC_FEE.getCode()) ||
                        StringUtils.equals(ecsReimDetailVos.get(0).getType(), ReimTypeEnum.WZCG_FEE.getCode())) {
                        wrapper.set(EcsReimDetailDto::getBusstas,MedConst.TYPE_6);
                    } else {
                        wrapper.set(EcsReimDetailDto::getBusstas,MedConst.TYPE_1);
                    }
                    ecsReimDetailWriteMapper.update(null,wrapper);
                } else if (AuditConst.STATE_FAIL.equals(dto.getAuditResult())){                                                            //审核失败
                    //更新发票状态
                    // 1.查询当前批次号下所有发票记录表记录id
                    EcsReimDetailDto ecsReimDetailDto = new EcsReimDetailDto();
                    ecsReimDetailDto.setAuditBchno(bchno);
                    List<EcsReimDetailVo> ecsReimDetailVos = ecsReimDetailReadMapper.queryList(ecsReimDetailDto);

                    if (CollectionUtil.isEmpty(ecsReimDetailVos)) {
                        return;
                    }
                    EcsReimDetailVo vo = ecsReimDetailVos.get(0);
                    //更新发票状态
                    updateInvoStatus(bchno,vo,MedConst.TYPE_1);
                    //更新当前付款状态为审批驳回
                    LambdaUpdateWrapper<EcsReimDetailDto> wrapper = Wrappers.lambdaUpdate();
                    wrapper.set(EcsReimDetailDto::getBusstas, EcsConst.REIM_BUSSTAS_REJECTED)
                            .eq(EcsReimDetailDto::getId,ecsReimDetailVos.get(0).getId());
                    ecsReimDetailWriteMapper.update(null,wrapper);
                } else {
                    //如果是节点成功，不做操作
                }
            }
        } else if (bchno.startsWith(AuditConst.ECS_CLSQ)) {         //申请
            if (StringUtils.isNotEmpty(dto.getAuditResult()) &&
                    StringUtils.isNotEmpty(bchno)) {
                LambdaUpdateWrapper<EcsReimTravelApprDto> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(EcsReimTravelApprDto::getAuditBchno,bchno);
                if (AuditConst.STATE_COMPLETE.equals(dto.getAuditResult())) {       //申请审核成功
                    updateWrapper.set(EcsReimTravelApprDto::getStatus,EcsConst.APPR_STATUS_AUDIT_SUCCESS);
                    ecsReimTravelApprWriteMapper.update(null,updateWrapper);
                } else if (StringUtils.equals(AuditConst.STATE_FAIL,dto.getAuditResult())){            //申请失败                                                //申请审核失败
                    updateWrapper.set(EcsReimTravelApprDto::getStatus,EcsConst.APPR_STATUS_AUDIT_FAILD);
                    ecsReimTravelApprWriteMapper.update(null,updateWrapper);
                }
            }
        } else if (bchno.startsWith(AuditConst.ECS_DRUG_FKTZ)) {        //药品付款
            //这里的bchno 是总的审核批次号
            if (StringUtils.isNotEmpty(dto.getAuditResult()) && StringUtils.isNotEmpty(bchno)) {
                //查询所有子审批报销  包括当前审核状态
                EcsDrugReimDetaiDto param = new EcsDrugReimDetaiDto();
                param.setParentAuditBchno(bchno);
                List<EcsDrugReimDetaiVo> ecsDrugReimDetaiVos = ecsDrugReimDetaiReadMapper.queryList(param);
                if (StringUtils.equals(AuditConst.STATE_COMPLETE,dto.getAuditResult())) {
                    //审核成功,  可能全部成功，可能部分成功/部分失败
                    //获取审核成功的子审批  (可能由于微服务事务问题，这里查询不到更新状态后的drugReim)   查询审核中的drugReim即可
                    //审核中的即为应该成功的
                    List<EcsDrugReimDetaiVo> successDrugReim = ecsDrugReimDetaiVos.stream()
                            .filter(item -> StringUtils.equals(item.getAuditState(), MedConst.TYPE_3)).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(successDrugReim)) {
                        //成功的更新为通过
                        handleDrugReimResult(successDrugReim,AuditConst.STATE_SUCCESS);
                    }
                    //获取审核失败的子审批
                    List<EcsDrugReimDetaiVo> failDrugReim = ecsDrugReimDetaiVos.stream()
                            .filter(item -> StringUtils.equals(item.getAuditState(), MedConst.TYPE_2)).collect(Collectors.toList());
                    if (CollectionUtil.isNotEmpty(failDrugReim)) {
                        //失败的更新为失败
                        handleDrugReimResult(failDrugReim,AuditConst.STATE_FAIL);
                    }
                } else if (StringUtils.equals(AuditConst.STATE_FAIL,dto.getAuditResult())){
                    //审核失败,则所有子审批全部失败
                    handleDrugReimResult(ecsDrugReimDetaiVos,AuditConst.STATE_FAIL);
                }
            }

        }
    }

    /**
     * 报销审核成功/拒绝都需要更新发票状态
     */
    private void updateInvoStatus(String bchno,EcsReimDetailVo ecsReimDetailVo,String status) {
        if (!Objects.isNull(ecsReimDetailVo)) {
            EcsReimDetailDto detailDto = new EcsReimDetailDto();
            detailDto.setId(ecsReimDetailVo.getId());
            List<EcsReimItemDetail> itemDetails = ecsReimDetailReadMapper.queryItemDetail(detailDto);
            List<EcsReimSubsItemDetail> subsItemDetails = ecsReimDetailReadMapper.querySubsItemDetail(detailDto);
            List<Long> ids = new ArrayList<>();
            //报销自带发票
            if (StringUtils.isNotEmpty(ecsReimDetailVo.getInvoId())){
                ids.addAll(getInvoIds(ecsReimDetailVo.getInvoId()));
            }
            // 项目
            if (CollectionUtil.isNotEmpty(itemDetails)) {
                itemDetails.forEach(i -> {
                    if (StringUtils.isNotEmpty(i.getInvoId())) {
                        ids.addAll(getInvoIds(i.getInvoId()));
                    }
                });
            }

            // 补助项目
            if (CollectionUtil.isNotEmpty(subsItemDetails)) {
                subsItemDetails.forEach(i -> {
                    if (StringUtils.isNotEmpty(i.getInvoId())) {
                        ids.addAll(getInvoIds(i.getInvoId()));
                    }
                });
            }

            if (CollectionUtil.isNotEmpty(ids)) {
                // 更新发票记录表状态，更改为已报销(2为已报销)
                ecsInvoRcdWriteMapper.updateStateByIds(ids, status,StringUtils.equals(status,MedConst.TYPE_1)?"":getReimInvoUsedBy(ecsReimDetailVo));
            }
        }
    }

    private String getReimInvoUsedBy(EcsReimDetailVo reim) {
        if (StringUtils.equals(reim.getType(),MedConst.TYPE_4)) {
            return ShareTypeEnum.getByType(reim.getShareType()).getName();
        }
        return ReimTypeEnum.getByCode(reim.getType()).getMessage();
    }

    private List<Long> getInvoIds(String invoId){
        List<Long> ids = new ArrayList<>();
        String[] split = invoId.split(",");
        for (int i = 0; i < split.length; i++) {
            ids.add(Long.parseLong(split[i]));
        }
        return ids;
    }

    /*private void handleDrugReimResult(List<EcsDrugReimDetaiVo> drugReims,String result) {
        //获取子审批批次号
        List<String> auditBchnos = drugReims.stream().map(EcsDrugReimDetaiVo::getAuditBchno).collect(Collectors.toList());
        LambdaUpdateWrapper<EcsDrugReimDetaiDto> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(EcsDrugReimDetaiDto::getAuditBchno,auditBchnos);
        if (StringUtils.equals(AuditConst.STATE_SUCCESS,result)) {
            //更新药品报销为成功状态
            wrapper.set(EcsDrugReimDetaiDto::getStatus,EcsConst.DRUG_REIM_STATUS_SUCCESS);
        } else {
            //更新药品报销为失败状态
            wrapper.set(EcsDrugReimDetaiDto::getStatus,EcsConst.DRUG_REIM_STATUS_FAILED);
        }
        //更新reims的审核状态
        ecsDrugReimDetaiWriteMapper.update(null,wrapper);

        Set<Long> invoIds = new HashSet<>();
        List<Long> drugReimIds = drugReims.stream().map(EcsDrugReimDetaiVo::getId).collect(Collectors.toList());
        //获取入库单信息
        LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery();
        stoinWrapper.in(EcsStoinDto::getDrugReimDetailId,drugReimIds);
        List<EcsStoinDto> ecsStoinDtos = ecsStoinReadMapper.selectList(stoinWrapper);
        ecsStoinDtos.stream().forEach(item -> {
            if (StringUtils.isNotEmpty(item.getInvoId())) {
                invoIds.addAll(Arrays.stream(item.getInvoId()
                                .split(","))
                        .map(Long:: valueOf)
                        .collect(Collectors.toList()));
            }
        });
        //更新发票状态
        if (CollectionUtil.isNotEmpty(invoIds)) {
            if (StringUtils.equals(AuditConst.STATE_SUCCESS,result)) {
                //更新发票状态为已报销
                ecsInvoRcdWriteMapper.updateStateByIds(new ArrayList<>(invoIds),MedConst.TYPE_2);
            } else {
                //更新发票状态为可报销
                ecsInvoRcdWriteMapper.updateStateByIds(new ArrayList<>(invoIds),MedConst.TYPE_1);
            }
        }
    }*/


    private void handleDrugReimResult(List<EcsDrugReimDetaiVo> drugReims, String result) {
        // 获取子审批批次号
        List<String> auditBchnos = drugReims.stream()
                .map(EcsDrugReimDetaiVo::getAuditBchno)
                .collect(Collectors.toList());

        // 更新药品报销状态
        updateDrugReimStatus(auditBchnos, result);

        //获取入库单
        List<EcsStoinDto> stoins = getStoinDtoFromDrugReim(drugReims);

        //更新入库单状态
        updateStoinStatus(stoins,result);

        //删除审核失败的报销
        if (StringUtils.equals(AuditConst.STATE_FAIL,result)) {
            LambdaQueryWrapper<EcsDrugReimDetaiDto> deleteWrapper = Wrappers.lambdaQuery(EcsDrugReimDetaiDto.class);
            deleteWrapper.in(EcsDrugReimDetaiDto::getAuditBchno,auditBchnos);
            ecsDrugReimDetaiWriteMapper.delete(deleteWrapper);
        }


        // 获取发票并更新发票状态
        Set<Long> invoIds = getInvoIdsFromStoins(stoins);
        if (!invoIds.isEmpty()) {
            updateInvoiceStatus(invoIds, result);
        }
    }

    private void updateDrugReimStatus(List<String> auditBchnos, String result) {
        LambdaUpdateWrapper<EcsDrugReimDetaiDto> wrapper = Wrappers.lambdaUpdate();
        wrapper.in(EcsDrugReimDetaiDto::getAuditBchno, auditBchnos);
        wrapper.set(EcsDrugReimDetaiDto::getStatus,
                StringUtils.equals(AuditConst.STATE_SUCCESS, result)
                        ? EcsConst.DRUG_REIM_STATUS_SUCCESS
                        : EcsConst.DRUG_REIM_STATUS_FAILED)
                .set(EcsDrugReimDetaiDto::getUpdateTime, LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        ecsDrugReimDetaiWriteMapper.update(null, wrapper);
    }

    private List<EcsStoinDto> getStoinDtoFromDrugReim(List<EcsDrugReimDetaiVo> drugReims) {
        List<Long> drugReimIds = drugReims.stream()
                .map(EcsDrugReimDetaiVo::getId)
                .collect(Collectors.toList());

        LambdaQueryWrapper<EcsStoinDto> stoinWrapper = Wrappers.lambdaQuery();
        stoinWrapper.in(EcsStoinDto::getDrugReimDetailId, drugReimIds);
        return ecsStoinReadMapper.selectList(stoinWrapper);
    }

    private Set<Long> getInvoIdsFromStoins(List<EcsStoinDto> ecsStoinDtos) {
        Set<Long> invoIds = new HashSet<>();
        ecsStoinDtos.forEach(item -> {
            if (StringUtils.isNotEmpty(item.getInvoId())) {
                Arrays.stream(item.getInvoId().split(","))
                        .map(Long::valueOf)
                        .forEach(invoIds::add);
            }
        });
        return invoIds;
    }

    private void updateStoinStatus(List<EcsStoinDto> stoins,String result) {
        //如果非审核成功，则更新入库单为可报销状态
        if (!StringUtils.equals(AuditConst.STATE_SUCCESS, result)) {
            List<Integer> stoinIds = stoins.stream().map(EcsStoinDto::getId).collect(Collectors.toList());
            LambdaUpdateWrapper<EcsStoinDto> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.set(EcsStoinDto::getReimFlag,MedConst.TYPE_0)
                    .set(EcsStoinDto::getDrugReimDetailId,null)
                    .set(EcsStoinDto::getAtt,null)
                    .set(EcsStoinDto::getAttName,null)
                    .set(EcsStoinDto::getInvoId,null)
                    .in(EcsStoinDto::getId,stoinIds);
            ecsStoinWriteMapper.update(null,updateWrapper);
        }
    }

    private void updateInvoiceStatus(Set<Long> invoIds, String result) {
        String newState = StringUtils.equals(AuditConst.STATE_SUCCESS, result)
                ? MedConst.TYPE_2
                : MedConst.TYPE_1;
        ecsInvoRcdWriteMapper.updateStateByIds(new ArrayList<>(invoIds), newState,StringUtils.equals(newState,MedConst.TYPE_1)?"": "药品报销");
    }

}

package com.jp.med.ecs.modules.drugMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 卫材入库单
 * <AUTHOR>
 * @email -
 * @date 2024-07-26 15:06:37
 */
@Data
public class EcsSatmatVo {

	/** id */
	private Integer id;

	/** 单号 */
	private Integer stoinNum;

	/** 入库时间 */
	private String stoinDate;

	/** 卫材编码 */
	private String satmatCode;

	/** 卫材名称 */
	private String satmatName;

	/** 规格 */
	private String specs;

	/** 型号 */
	private String pattern;

	/** 单位 */
	private String unit;

	/** 批号 */
	private String batchNum;

	/** 生产日期 */
	private String prodDate;

	/** 有效期 */
	private String validDate;

	/** 卫材数量 */
	private Double satmatNum;

	/** 单价 */
	private BigDecimal unitPrice;

	/** 总金额 */
	private BigDecimal sumamt;

	/** 厂家 */
	private String factory;

	/** 供应商 */
	private String spler;

	/** 货主 */
	private String consignor;

	/** 同步日期 **/
	private String syncDate;

	/** 报销id **/
	private Integer satmatReimDetailId;

	/** 医疗机构id */
	private String hospitalId;

	/** 文件 */
	private String att;

	/** 文件名称 */
	private String attName;

	/** 报销标志 **/
	private String reimFlag;

}

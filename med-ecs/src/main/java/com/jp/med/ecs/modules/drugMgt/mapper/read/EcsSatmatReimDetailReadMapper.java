package com.jp.med.ecs.modules.drugMgt.mapper.read;

import com.jp.med.ecs.modules.drugMgt.dto.EcsSatmatReimDetailDto;
import com.jp.med.ecs.modules.drugMgt.vo.EcsSatmatReimDetailVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 卫材报销详情
 * <AUTHOR>
 * @email -
 * @date 2024-08-02 09:28:49
 */
@Mapper
public interface EcsSatmatReimDetailReadMapper extends BaseMapper<EcsSatmatReimDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsSatmatReimDetailVo> queryList(EcsSatmatReimDetailDto dto);

    List<EcsSatmatReimDetailVo> queryAuditData(EcsSatmatReimDetailDto dto);

    List<EcsSatmatReimDetailVo> queryAuditCollData(EcsSatmatReimDetailDto dto);
}

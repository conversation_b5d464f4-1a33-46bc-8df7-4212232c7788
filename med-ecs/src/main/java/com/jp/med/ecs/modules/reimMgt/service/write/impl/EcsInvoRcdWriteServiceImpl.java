package com.jp.med.ecs.modules.reimMgt.service.write.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.ecs.modules.reimMgt.dto.EcsInvoRcdDto;
import com.jp.med.ecs.modules.reimMgt.mapper.read.EcsInvoRcdReadMapper;
import com.jp.med.ecs.modules.reimMgt.mapper.write.EcsInvoRcdWriteMapper;
import com.jp.med.ecs.modules.reimMgt.service.write.EcsInvoRcdWriteService;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * 发票记录
 * <AUTHOR>
 * @email -
 * @date 2024-01-01 17:01:30
 */
@Service
@Transactional(readOnly = false)
public class EcsInvoRcdWriteServiceImpl extends ServiceImpl<EcsInvoRcdWriteMapper, EcsInvoRcdDto> implements EcsInvoRcdWriteService {

    @Autowired
    private EcsInvoRcdReadMapper ecsInvoRcdReadMapper;

    @Autowired
    private EcsInvoRcdWriteMapper ecsInvoRcdWriteMapper;
    @Override
    public void manualAmendInvo(EcsInvoRcdDto dto) {
        //查询手动修正的发票是否已经存在
        int count = ecsInvoRcdReadMapper.queryAlreadyAmendRcd(dto.getIds());
        if (count > 0){
            throw new AppException("存在已经人工校正的发票，请刷新重试");
        }/*
        dtos.stream().forEach(item -> {
            item.setManualAmend(MedConst.TYPE_1);
            item.setAmendUser(item.getSysUser().getHrmUser().getEmpCode());
        });*/
        LambdaUpdateWrapper<EcsInvoRcdDto> updateWrapper = Wrappers.lambdaUpdate();
        //人工申请状态 1：申请中 2 申请成功 3.申请失败
        updateWrapper.set(EcsInvoRcdDto::getStatus,MedConst.TYPE_1)
                .set(EcsInvoRcdDto::getAmendApplyer,dto.getSysUser().getUsername())
                .in(EcsInvoRcdDto::getId,dto.getIds());
        ecsInvoRcdWriteMapper.update(null,updateWrapper);

    }

    /**
     * 发票校正审核
     * @param dto
     */
    @Override
    public void invoAudit(EcsInvoRcdDto dto) {
        dto.setManualAmend(MedConst.TYPE_1);
        dto.setAmendUser(dto.getSysUser().getHrmUser().getEmpCode());
        ecsInvoRcdWriteMapper.updateById(dto);
    }

    @Override
    public void invoDel(EcsInvoRcdDto dto) {
        //查询发票parent
        LambdaQueryWrapper<EcsInvoRcdDto> wrapper = Wrappers.lambdaQuery(EcsInvoRcdDto.class);
        wrapper.in(EcsInvoRcdDto::getId,dto.getIds());
        List<EcsInvoRcdDto> ecsInvoRcdDtos = ecsInvoRcdReadMapper.selectList(wrapper);
        //判断发票状态  未报销和识别失败的发票才能删除
        boolean invalid = false;
        List<String> errCode = Arrays.asList("1", "5", "6", "7", "8");
        for (int i = 0; i < ecsInvoRcdDtos.size(); i++) {
            if (!errCode.contains(ecsInvoRcdDtos.get(i).getState())){
                invalid = true;
            }
        }
        if (invalid) {
            throw new AppException("只有未报销和审核失败的发票才能删除");
        }
        //获取发票sub的id
        Set<Integer> subInvoIds = new HashSet<>();
        ecsInvoRcdDtos.stream().forEach(e -> {
            if (StringUtils.isNotEmpty(e.getSubInvoIds())){
                String[] split = StringUtils.split(e.getSubInvoIds(), ",");
                for (String s : split) {
                    subInvoIds.add(Integer.parseInt(s));
                }
            }
        });
        //将parentInvo和subInvo的id结合
        dto.getIds().addAll(subInvoIds);
        LambdaUpdateWrapper<EcsInvoRcdDto>  delWrapper = Wrappers.lambdaUpdate(EcsInvoRcdDto.class);
        ecsInvoRcdWriteMapper.deleteBatchIds(dto.getIds());
    }
}

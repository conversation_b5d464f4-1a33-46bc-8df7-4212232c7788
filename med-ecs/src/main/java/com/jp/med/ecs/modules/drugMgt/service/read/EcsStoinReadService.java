package com.jp.med.ecs.modules.drugMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.drug.EcsStoinDto;
import com.jp.med.common.vo.ecs.drug.EcsStoinVo;
import com.jp.med.ecs.modules.drugMgt.vo.EcsStoinDetailVo;

import java.util.List;
import java.util.Map;

/**
 * 入库单
 * <AUTHOR>
 * @email -
 * @date 2024-01-04 16:54:44
 */
public interface EcsStoinReadService extends IService<EcsStoinDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsStoinVo> queryList(EcsStoinDto dto);

    /**
     * 查询月份入库单数量
     * @param dto
     * @return
     */
    List<Map<String, Integer>> monthNum(EcsStoinDto dto);

    /**
     * 查询入库单明细
     * @param dto
     * @return
     */
    List<EcsStoinDetailVo> queryStoinDetails(EcsStoinDto dto);
}


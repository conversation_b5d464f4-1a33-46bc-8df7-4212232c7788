package com.jp.med.ecs.modules.reimMgt.vo;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 合同报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 17:13:38
 */
@Data
public class EcsReimContractTaskVo {

	/** id */
	private Integer id;

	/** 申请人编号 */
	private String appyer;

	/** 申请科室 */
	private String appyerDept;

	/** 合同编码 */
	private String ctCode;

	/** 合同分类编码 */
	private String typeCode;

	/** 合同附件 */
	private String contractAtt;

	/** 合同附件名 */
	private String contractAttName;

	/** 合同总额 */
	private BigDecimal totalAmt;

	/** 当前阶段 */
	private String stage;

	/** 合同id */
	private Integer contractId;

	/** 所占比例 */
	private String proportion;

	/** 计划付款时间 */
	private String paymentTime;

	/** 报销标志 0:未报销 1:已报销 */
	private String reimFlag;

	/** 报销id **/
	private Integer reimId;

	/** 付款期数id **/
	private Integer paymentId;

	/** 申请人姓名 **/
	private String appyerName;

	/** 申请科室名称 **/
	private String appyerDeptName;

	/**
	 * 开户行
	 */
	private String bank;
	/**
	 * 户名
	 */
	private String acctname;

	/**
	 * 银行账号
	 */
	private String bankcode;

	/**
	 * 合同相对方
	 */
	private String oppositeName;

	/**
	 * 合同名称
	 */
	private String ctName;

	/**
	 * 全院统一合同编码
	 */
	private String ctUnifiedCode;

	/**
	 * 待报销金额
	 */
	private BigDecimal needReimAmt;

	private String paymentType;
}

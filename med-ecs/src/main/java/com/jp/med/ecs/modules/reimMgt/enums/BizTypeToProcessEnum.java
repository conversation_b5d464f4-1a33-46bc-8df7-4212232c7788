package com.jp.med.ecs.modules.reimMgt.enums;

import io.seata.common.util.StringUtils;

/**
 *  报销审核流程
 */
public enum BizTypeToProcessEnum {
    //bizType 1:差旅 2：培训 3：其他费用 4：分摊 5：工资 6：合同 7：折旧 8：零星采购
    //差旅普通
    TRAVEL_NORMAL_PROCESS("1","Normal","ECS_EXPENSE_REIM_OTHER"),

    //差旅临床医技
    TRAVEL_CLINIC_PROCESS("1","Clinic","ECS_EXPENSE_REIM_CLINC"),
    //培训普通
    TRAINING_NORMAL_PROCESS("2","Normal","ECS_EXPENSE_REIM_OTHER"),

    //培训临床医技
    TRAINING_CLINIC_PROCESS("2","Clinic","ECS_EXPENSE_REIM_CLINC"),

    //其他费用-普通
    REIM_NORMAL_PROCESS("3","Normal","ECS_EXPENSE_REIM_OTHER"),
    //其他费用报销-采管科
    REIM_PCM_PROCESS("3","PCM","ECS_EXPENSE_REIM_PCM"),

    //其他费用-普通
    LABOR_REIM_NORMAL_PROCESS("11","Normal","ECS_EXPENSE_REIM_OTHER"),
    //其他费用报销-采管科
    LABOR_REIM_PCM_PROCESS("11","PCM","ECS_EXPENSE_REIM_PCM"),

    //借款流程
    LOAN_REIM_NORMAL_PROCESS("13","Normal","ECS_LOAN_REIM_OTHER"),

    //分摊报销
    REIM_SHARE_NORMAL_PROCESS("4","Normal","ECS_EXPENSE_REIM_OTHER"),

    //合同报销费用-普通
    REIM_CONTRACT_NORMAL_PROCESS("6","Normal","ECS_EXPENSE_REIM_OTHER"),

    //零星采购报销费用-普通
    REIM_PURM_NORMAL_PROCESS("8","Normal","ECS_EXPENSE_REIM_PURM"),

    //物资采购报销费用-普通
    REIM_WX_NORMAL_PROCESS("10","Normal","ECS_EXPENSE_REIM_PURM"),

    //工资报销费用-普通
    REIM_SALARY_NORMAL_PROCESS("5","Normal","ECS_EXPENSE_REIM_OTHER"),

    REIM_TRADE_TRANSACTIONS("12","Normal","REIM_TRADE_TRANSACTIONS"),
    REIM_HRM_RESEARCHER_FUNDING("14","Normal","REIM_HRM_RESEARCHER_FUNDING"),

    REIM_HRM_PAPER_PAGE_CHARGE("15","Normal","REIM_HRM_PAPER_PAGE_CHARGE"),
    ;

    private String bizType;

    private String bizSubType;

    private String processName;

    public String getBizType() {
        return bizType;
    }

    public String getBizSubType() {
        return bizSubType;
    }

    public String getProcessName() {
        return processName;
    }

    BizTypeToProcessEnum(String bizType, String bizSubType, String processName) {
        this.bizType = bizType;
        this.bizSubType = bizSubType;
        this.processName = processName;
    }

    public static BizTypeToProcessEnum getSpecialProcess(String bizType,String bizSubType) {
        for (BizTypeToProcessEnum process : BizTypeToProcessEnum.values()) {
            if (StringUtils.equals(process.getBizType(),bizType) && StringUtils.equals(process.getBizSubType(),bizSubType)) {
                return process;
            }
        }
        return null;
    }
}

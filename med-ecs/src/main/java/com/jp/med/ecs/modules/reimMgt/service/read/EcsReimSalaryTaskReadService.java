package com.jp.med.ecs.modules.reimMgt.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.ecs.EcsReimSalaryTask;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskDetailVo;
import com.jp.med.ecs.modules.reimMgt.vo.EcsReimSalaryTaskVo;

import java.util.List;
import java.util.Map;

/**
 * 应发工资报销任务
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 14:45:03
 */
public interface EcsReimSalaryTaskReadService extends IService<EcsReimSalaryTask> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsReimSalaryTaskVo> queryList(EcsReimSalaryTask dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<EcsReimSalaryTaskVo> queryPageList(EcsReimSalaryTask dto);

    /**
     * 查询工资任务明细
     * @param dto
     * @return
     */
    List<EcsReimSalaryTaskDetailVo> querySalaryTaskDetail(EcsReimSalaryTask dto);

    Map<String,String> getSalarySummaryExcel(EcsReimSalaryTask dto);
    /**
     * 分页查询列表
     * @param dto
     * @return
     */
    List<EcsReimSalaryTaskVo> queryPageListNew(EcsReimSalaryTask dto);
}


package com.jp.med.ecs.modules.reimMgt.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 发票校验记录详细
 * <AUTHOR>
 * @email -
 * @date 2024-05-28 14:34:49
 */
@Data
@TableName("ecs_invo_chk_rcd_detail")
public class EcsInvoChkRcdDetailEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 税额 */
	@TableField("all_tax")
	private String allTax;

	/** 规格型号 */
	@TableField("standard")
	private String standard;

	/** 行号 */
	@TableField("row_no")
	private String rowNo;

	/** 税收分类编码 **/
	@TableField("tax_classify_code")
	private String taxClassifyCode;

	/** 不含税单价 */
	@TableField("net_value")
	private String netValue;

	/** 数量 */
	@TableField("num")
	private String num;

	/** 通行日期起 **/
	@TableField("traffic_date_start")
	private String trafficDateStart;

	/** 类型 **/
	@TableField("type")
	private String type;

	/** 含税金额 **/
	@TableField("tax_detail_amount")
	private String taxDetailAmount;

	/** 税率 */
	@TableField("tax_rate")
	private String taxRate;

	/** 含税单价 **/
	@TableField("tax_unit_price")
	private String taxUnitPrice;

	/** 计量单位 */
	@TableField("unit")
	private String unit;

	/** 车牌号 **/
	@TableField("plate_no")
	private String plateNo;

	/** 明细编号 **/
	@TableField("detail_no")
	private String detailNo;

	/** 金额 */
	@TableField("detail_amount")
	private String detailAmount;

	/** 项目名称 */
	@TableField("goods_name")
	private String goodsName;

	/** 通行日期止 **/
	@TableField("traffic_date_end")
	private String trafficDateEnd;

	/** 费用项目 **/
	@TableField("expense_item")
	private String expenseItem;

	/** 发票记录id */
	@TableField("invo_rcd_id")
	private Integer invoRcdId;

}

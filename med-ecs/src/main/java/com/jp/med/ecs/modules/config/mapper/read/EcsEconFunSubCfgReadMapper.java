package com.jp.med.ecs.modules.config.mapper.read;

import com.jp.med.ecs.modules.config.dto.EcsEconFunSubCfgDto;
import com.jp.med.ecs.modules.config.vo.EcsEconFunSubCfgVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 经济和功能科目配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-23 10:53:01
 */
@Mapper
public interface EcsEconFunSubCfgReadMapper extends BaseMapper<EcsEconFunSubCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EcsEconFunSubCfgVo> queryList(EcsEconFunSubCfgDto dto);
}

package com.jp.med.ecs.modules.config.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.ChineseCharToEnUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.ecs.modules.config.dto.EcsActigAsstCashCfgDto;
import com.jp.med.ecs.modules.config.service.read.EcsActigAsstCashCfgReadService;
import com.jp.med.ecs.modules.config.service.write.EcsActigAsstCashCfgWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 现金流量配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-08 14:03:00
 */
@Api(value = "现金流量配置", tags = "现金流量配置")
@RestController
@RequestMapping("ecsActigAsstCashCfg")
public class EcsActigAsstCashCfgController {

    @Autowired
    private EcsActigAsstCashCfgReadService ecsActigAsstCashCfgReadService;

    @Autowired
    private EcsActigAsstCashCfgWriteService ecsActigAsstCashCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询现金流量配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody EcsActigAsstCashCfgDto dto){
        return CommonResult.success(ecsActigAsstCashCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增现金流量配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody EcsActigAsstCashCfgDto dto){
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        if (StringUtils.isEmpty(dto.getPinyin())) {
            dto.setPinyin(ChineseCharToEnUtil.getAllFirstLetter(dto.getSubName()).toUpperCase());
        }
        ecsActigAsstCashCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改现金流量配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody EcsActigAsstCashCfgDto dto){
        dto.setModiTime(DateUtil.getCurrentTime(null));
        ecsActigAsstCashCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除现金流量配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody EcsActigAsstCashCfgDto dto){
        ecsActigAsstCashCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 同步
     */
    @ApiOperation("查询经济和功能科目配置")
    @PostMapping("/sync")
    public CommonResult<?> sync(@RequestBody EcsActigAsstCashCfgDto dto){
        ecsActigAsstCashCfgWriteService.sync(dto);
        return CommonResult.success();
    }

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ecs.modules.reimMgt.mapper.write.EcsReimPurcTaskWriteMapper">
    <insert id="insertPurcTaskDetail">
        INSERT INTO ecs_reim_purc_task_detail(task_id, item_no,
                                              org_id, reim_type,unt, cnt, price, reim_amt, reim_desc, att, att_name,purc_detail_id)
        VALUES (#{taskId,jdbcType=INTEGER},
                #{itemNo,jdbcType=VARCHAR},
                #{orgId,jdbcType=VARCHAR},
                #{reimType,jdbcType=VARCHAR},
                #{unt,jdbcType=VARCHAR},
                #{cnt,jdbcType=DOUBLE},
                #{price,jdbcType=DOUBLE},
                #{reimAmt,jdbcType=DOUBLE},
                #{reimDesc,jdbcType=VARCHAR},
                #{att,jdbcType=VARCHAR},
                #{attName,jdbcType=VARCHAR},
                #{purcDetailId,jdbcType=INTEGER})
    </insert>

    <update id="updatePurcTaskDetails">
        UPDATE ecs_reim_purc_task_detail
        SET reim_id   = #{reimId},
            reim_flag = #{reimFlag} WHERE id IN
        <foreach collection="ids" item="item" separator=","
                 open="(" close=")">
            #{item}
        </foreach>
    </update>

    <update id="updatePurcTaskDetailsByReimId">
        UPDATE ecs_reim_purc_task_detail
        SET reim_id   = null,
            reim_flag = '0'
        WHERE reim_id = #{reimId}
    </update>
</mapper>

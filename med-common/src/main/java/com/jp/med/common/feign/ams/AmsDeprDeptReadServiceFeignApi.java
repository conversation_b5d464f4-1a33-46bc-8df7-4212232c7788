package com.jp.med.common.feign.ams;


import com.jp.med.common.dto.erp.ErpPropertyDeprDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.vo.ErpAmsPropertyDepr2Vo;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@RefreshScope
@FeignClient(name = "AmsDeprDeptReadServiceFeignApi", url = "${custom.gateway.med-ams-service-uri}")
public interface AmsDeprDeptReadServiceFeignApi {


    @PostMapping("/amsDepr/saveSalaryTask")
    CommonFeignResult summarizeAssets();

    @PostMapping("/amsPropertyDepr/queryDeprSummary2")
    CommonResult<List<ErpAmsPropertyDepr2Vo>> erpQueryDeprSummary2(@RequestBody ErpPropertyDeprDto dto);
}

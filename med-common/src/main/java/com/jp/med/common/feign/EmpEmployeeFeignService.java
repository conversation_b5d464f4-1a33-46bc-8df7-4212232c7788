package com.jp.med.common.feign;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jp.med.common.dto.emp.EmpEmployeeInfoDto;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.vo.EmpEmployeeInfoVo;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

@RefreshScope
@FeignClient(name = "EmpEmployeeFeignService", url = "${custom.gateway.med-hrm-service-uri}")
public interface EmpEmployeeFeignService {

    @PostMapping("/emp/employeeInfo/list")
    CommonResult<Page<EmpEmployeeInfoVo>> queryList(@RequestBody EmpEmployeeInfoDto dto);
}

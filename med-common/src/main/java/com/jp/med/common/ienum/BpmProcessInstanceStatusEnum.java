package com.jp.med.common.ienum;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;

/**
 * 流程实例 ProcessInstance 的状态
 */
@Getter
@AllArgsConstructor
public enum BpmProcessInstanceStatusEnum {

    CREATE(0, "创建"),
    RUNNING(1, "审批中"),
    APPROVE(2, "审批通过"),
    REJECT(3, "审批不通过"),
    CANCEL(4, "已取消");


    /**
     * 状态
     */
    private final Integer status;
    /**
     * 描述
     */
    private final String desc;


    public static BpmProcessInstanceStatusEnum fromStatus(Integer status) {
        for (BpmProcessInstanceStatusEnum taskStatus : values()) {
            if (taskStatus.getStatus().equals(status)) {
                return taskStatus;
            }
        }
        return null; // or throw an exception
    }


}

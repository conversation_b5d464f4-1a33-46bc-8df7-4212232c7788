package com.jp.med.ams.modules.config.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.config.dto.AmsStorageCfgDto;
import com.jp.med.ams.modules.config.service.read.AmsStorageCfgReadService;
import com.jp.med.ams.modules.config.service.write.AmsStorageCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 存放地点配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-12 10:23:40
 */
@Api(value = "存放地点配置", tags = "存放地点配置")
@RestController
@RequestMapping("amsStorageCfg")
public class AmsStorageCfgController {

    @Autowired
    private AmsStorageCfgReadService amsStorageCfgReadService;

    @Autowired
    private AmsStorageCfgWriteService amsStorageCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询存放地点配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsStorageCfgDto dto){
        return CommonResult.success(amsStorageCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增存放地点配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsStorageCfgDto dto){
        amsStorageCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改存放地点配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsStorageCfgDto dto){
        amsStorageCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除存放地点配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsStorageCfgDto dto){
        amsStorageCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}

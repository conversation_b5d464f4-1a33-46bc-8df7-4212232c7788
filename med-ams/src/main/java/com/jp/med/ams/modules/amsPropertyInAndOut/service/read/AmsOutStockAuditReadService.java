package com.jp.med.ams.modules.amsPropertyInAndOut.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.amsPropertyInAndOut.dto.AmsOutStockAuditDto;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @email -
 * @date 2024-05-27 20:15:50
 */
public interface AmsOutStockAuditReadService extends IService<AmsOutStockAuditDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsOutStockAuditDto> queryList(AmsOutStockAuditDto dto);

    /**
     * 查询待审核数量
     * @param dto
     * @return
     */
    Integer queryAuditCount(AmsOutStockAuditDto dto);

    /**
     * 查询待出库数量
     * @param dto
     * @return
     */
    Integer queryOutCount(AmsOutStockAuditDto dto);
}


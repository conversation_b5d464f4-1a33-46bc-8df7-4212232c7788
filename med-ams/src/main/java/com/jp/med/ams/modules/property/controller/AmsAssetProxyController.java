package com.jp.med.ams.modules.property.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.ams.modules.property.dto.AmsAssetProxyDto;
import com.jp.med.ams.modules.property.service.read.AmsAssetProxyReadService;
import com.jp.med.ams.modules.property.service.write.AmsAssetProxyWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 资产人员科室分类代理管理表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-11-05 11:43:44
 */
@Api(value = "资产人员科室分类代理管理表", tags = "资产人员科室分类代理管理表")
@RestController
@RequestMapping("amsAssetProxy")
public class AmsAssetProxyController {

    @Autowired
    private AmsAssetProxyReadService amsAssetProxyReadService;

    @Autowired
    private AmsAssetProxyWriteService amsAssetProxyWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询资产人员科室分类代理管理表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody AmsAssetProxyDto dto) {
        return CommonResult.paging(amsAssetProxyReadService.queryPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询资产人员科室分类代理管理表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AmsAssetProxyDto dto) {
        return CommonResult.success(amsAssetProxyReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增资产人员科室分类代理管理表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AmsAssetProxyDto dto) {
        amsAssetProxyWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改资产人员科室分类代理管理表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AmsAssetProxyDto dto) {
        amsAssetProxyWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除资产人员科室分类代理管理表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AmsAssetProxyDto dto) {
        amsAssetProxyWriteService.removeById(dto);
        return CommonResult.success();
    }

}

package com.jp.med.ams.modules.property.dto;

import java.util.Date;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 资产报废申请表
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-09-08 16:24:55
 */
@Data
@TableName("ams_scrap_apply")
public class AmsScrapApplyDto extends CommonQueryDto {

    /** ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 申请单号 */
    @TableField("apply_docno")
    private String applyDocno;

    /** 申请科室 */
    @TableField("dept")
    private String dept;

    /** 申请人 */
    @TableField("applyer")
    private String applyer;

    /** 申请时间 */
    @TableField("appy_date")
    private String appyDate;

    /**
     * 申请原因
     */
    @TableField("applying_reason")
    private String applyingReason;

    /** 医疗机构ID */
    @TableField("hospital_id")
    private String hospitalId;

    @TableField("apply_time")
    private Date applyTime;

    @TableField("flag")
    private String flag;

    /**
     * 审核批次号
     */
    @TableField("bchno")
    private String bchno;

    /**
     * 是否接收
     */
    @TableField("receive")
    private String receive;

    /**
     * 业务状态
     */
    @TableField("prosstas")
    private String prosstas;

    @TableField(exist = false)
    private String annual;

    @TableField(exist = false)
    private String export;

    /**
     * scrapAssetType
     * 报废资产类型
     */
    @TableField(exist = false)
    private String scrapAssetType;

    /**
     * 操作人员编码
     */
    @TableField(exist = false)
    private String opter;

    /**
     * 是否已鉴定
     */
    @TableField(exist = false)
    private Boolean identified;

    /**
     * 查询asset
     */
    @TableField(exist = false)
    private String asset;
}

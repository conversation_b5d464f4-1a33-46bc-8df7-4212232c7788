package com.jp.med.ams.modules.config.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.config.mapper.write.AmsTypeCfgWriteMapper;
import com.jp.med.ams.modules.config.dto.AmsTypeCfgDto;
import com.jp.med.ams.modules.config.service.write.AmsTypeCfgWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资产类型配表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 14:10:05
 */
@Service
@Transactional(readOnly = false)
public class AmsTypeCfgWriteServiceImpl extends ServiceImpl<AmsTypeCfgWriteMapper, AmsTypeCfgDto> implements AmsTypeCfgWriteService {
}

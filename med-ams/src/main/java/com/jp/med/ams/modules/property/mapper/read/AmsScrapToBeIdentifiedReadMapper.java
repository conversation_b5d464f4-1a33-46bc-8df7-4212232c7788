package com.jp.med.ams.modules.property.mapper.read;

import com.jp.med.ams.modules.property.dto.AmsScrapToBeIdentifiedDto;
import com.jp.med.ams.modules.property.vo.AmsScrapToBeIdentifiedVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 资产报废待鉴定人员映射
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-02 15:21:24
 */
@Mapper
public interface AmsScrapToBeIdentifiedReadMapper extends BaseMapper<AmsScrapToBeIdentifiedDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsScrapToBeIdentifiedVo> queryList(AmsScrapToBeIdentifiedDto dto);
}

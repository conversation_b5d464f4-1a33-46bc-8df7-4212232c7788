package com.jp.med.ams.modules.property.service.write.impl;

import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.property.mapper.write.AmsSourceAmountSplitWriteMapper;
import com.jp.med.ams.modules.property.dto.AmsSourceAmountSplitDto;
import com.jp.med.ams.modules.property.service.write.AmsSourceAmountSplitWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 资产金额拆分
 *
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 21:04:01
 */
@Service
@Transactional(readOnly = false)
public class AmsSourceAmountSplitWriteServiceImpl extends ServiceImpl<AmsSourceAmountSplitWriteMapper, AmsSourceAmountSplitDto> implements AmsSourceAmountSplitWriteService {
}

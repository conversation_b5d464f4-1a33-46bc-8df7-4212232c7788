package com.jp.med.ams.modules.property.mapper.read;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.github.pagehelper.PageHelper;
import com.jp.med.ams.modules.property.dto.AmsAssetProxyDto;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.vo.AmsAssetProxyVo;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import com.jp.med.ams.modules.property.vo.AmsRecordsVo;
import com.jp.med.common.vo.SelectOptionVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 资产详情
 *
 * <AUTHOR>
 * @email -
 * @date 2023-08-29 17:33:47
 */
@Mapper
public interface AmsPropertyReadMapper extends BaseMapper<AmsPropertyDto> {

    /**
     * 查询代管审批科室
     */
    @Select("select distinct unnest(string_to_array(depts, ',')) from " +
            "sys_behalf_approver WHERE approver = #{approver} ")
    List<String> queryBehalfApprovalDepts(@Param("approver") String approver);

    /**
     * 获取资产代理数据
     */
    default AmsAssetProxyVo getAssetProxyData(AmsPropertyDto dto) {
        // 检查用户是否存在
        if (dto.getSysUser() == null || dto.getSysUser().getHrmUser() == null
                || dto.getSysUser().getHrmUser().getEmpCode() == null) {
            return null;
        }
        // 获取代理数据
        AmsAssetProxyReadMapper amsAssetProxyReadMapper = SpringUtil.getBean(AmsAssetProxyReadMapper.class);
        AmsAssetProxyDto queryDto = new AmsAssetProxyDto();
        queryDto.setStatus(1);// 启用
        queryDto.setProxyUserCode(dto.getSysUser().getHrmUser().getEmpCode());
        queryDto.setProxyDeptCode(dto.getSysUser().getHrmUser().getHrmOrgId());
        PageHelper.clearPage();
        List<AmsAssetProxyVo> amsAssetProxyVos = amsAssetProxyReadMapper.queryList(queryDto);

        // 初始化资产类型集合
        Set<String> assetTypeSet = new HashSet<>();
        Set<String> assetTypeNewSet = new HashSet<>();

        // 处理代理类型
        // * 代理类型(3:指定科室代理分类 4:指定人员代理分类)

        for (AmsAssetProxyVo proxyVo : amsAssetProxyVos) {
            if ("4".equals(proxyVo.getProxyType())) {
                // 处理资产类型
                if (StrUtil.isNotBlank(proxyVo.getAssetType())) {
                    assetTypeSet.addAll(List.of(proxyVo.getAssetType().split(StrUtil.COMMA)));
                }
                // 处理新资产类型
                if (StrUtil.isNotBlank(proxyVo.getAssetTypeNew())) {
                    assetTypeNewSet.addAll(List.of(proxyVo.getAssetTypeNew().split(StrUtil.COMMA)));
                }
            }
            if ("3".equals(proxyVo.getProxyType())) {
                // 处理资产类型
                if (StrUtil.isNotBlank(proxyVo.getAssetType())) {
                    assetTypeSet.addAll(List.of(proxyVo.getAssetType().split(StrUtil.COMMA)));
                }
                // 处理新资产类型
                if (StrUtil.isNotBlank(proxyVo.getAssetTypeNew())) {
                    assetTypeNewSet.addAll(List.of(proxyVo.getAssetTypeNew().split(StrUtil.COMMA)));
                }
            }
        }

        // 返回结果
        if (assetTypeSet.isEmpty() && assetTypeNewSet.isEmpty()) {
            return null;
        }

        AmsAssetProxyVo result = new AmsAssetProxyVo();
        result.setAssetTypeSet(assetTypeSet);
        result.setAssetTypeNewSet(assetTypeNewSet);
        return result;
    }

    /**
     * 获取代管审批科室
     */
    default List<String> getBehalfApprovalDepts(AmsPropertyDto dto) {
        // 参数校验
        if (dto == null || dto.getSysUser() == null ||
                dto.getSysUser().getHrmUser() == null ||
                StrUtil.isBlank(dto.getCurSysOrgId())) {
            return null;
        }

        // 获取审批人代码
        String approver = dto.getSysUser().getHrmUser().getEmpCode();

        PageHelper.clearPage();
        // 查询代管科室
        List<String> behalfApprovalDepts = queryBehalfApprovalDepts(approver);
        behalfApprovalDepts.add(dto.getCurSysOrgId());

        return behalfApprovalDepts;
    }

    /**
     * 查询主要字段列表
     */
    default List<AmsPropertyVo> queryMainList(AmsPropertyDto dto) {
        // 设置代管审批部门
        setQueryParams(dto);
        return queryList(dto);
    }

    /**
     * 查询列表
     */
    default List<AmsPropertyVo> queryList(AmsPropertyDto dto) {
        // 设置代管审批部门
        setQueryParams(dto);
        return queryList0(dto);
    }

    /**
     * 查询汇总
     */
    default List<AmsPropertyVo> queryListSum(AmsPropertyDto dto) {
        dto.setPageNum(null);
        dto.setPageSize(null);
        PageHelper.clearPage();
        dto.setOnlyQueryId(true);
        var amsPropertyVos = queryList(dto);

        List<Integer> ids = amsPropertyVos.stream().map(AmsPropertyVo::getId).collect(Collectors.toList());
        if (ids.isEmpty()) {
            return new ArrayList<>();
        }
        dto.setIds(ids);
        dto.setExtendForm(null);
        return queryListSum0(dto);
    }

    default void setQueryParams(AmsPropertyDto dto) {
        // 设置代管审批部门
        List<String> behalfApprovalDepts = getBehalfApprovalDepts(dto);
        dto.setBehalfApprovalDepts(behalfApprovalDepts);

        // 设置资产代理数据
        AmsAssetProxyVo assetProxyData = getAssetProxyData(dto);
        if (assetProxyData != null) {
            dto.setProxyAssetType(assetProxyData.getAssetTypeSet());
            dto.setProxyAssetTypeN(assetProxyData.getAssetTypeNewSet());
        }
        if (dto.getPageNum() != null && dto.getPageSize() != null) {
            PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        }
    }

    /**
     * 查询列表
     */
    List<AmsPropertyVo> queryList0(AmsPropertyDto dto);

    /**
     * 查询主要字段列表 废弃
     */
//    List<AmsPropertyVo> queryMainList0(AmsPropertyDto dto);

    /**
     * 查询资产档案
     */
    List<AmsRecordsVo> queryRecords(AmsPropertyDto dto);

    /**
     * 查询最大的固定资产码
     */
    Long queryMaxId(AmsPropertyDto dto);

    /**
     * 查询最大资产编码
     */
    Long queryMaxAssetCode(AmsPropertyDto dto);

    /**
     * 查询在用资产的科室
     */
    List<SelectOptionVo> queryUseDept(AmsPropertyDto dto);

    List<AmsPropertyVo> queryListSum0(AmsPropertyDto dto);

    List<AmsPropertyVo> querySon(AmsPropertyDto dto);

    Long queryMaxSonFaCode(AmsPropertyDto dto);

    AmsPropertyDto queryParent(AmsPropertyDto dto);

    List<Map<String, String>> countByCategory(List<Integer> idList);

    List<Map<String, String>> sumByCategory(List<Integer> idList);

    List<Map<String, String>> topCountByDept(List<Integer> idList);

    /**
     * 查询资产使用科室
     *
     * @param assetFaCodeList 资产卡片代码列表
     * @return 资产使用科室列表 去重List
     */
    List<String> queryAssetUseDept(@Param("assetFaCodeList") List<String> assetFaCodeList);
}

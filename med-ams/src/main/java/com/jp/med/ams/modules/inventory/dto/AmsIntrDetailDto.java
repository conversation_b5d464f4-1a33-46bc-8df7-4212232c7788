package com.jp.med.ams.modules.inventory.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.util.List;

/**
 * 盘点数据明细表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Data
@TableName("ams_intr_detail" )
public class AmsIntrDetailDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 盘点ID */
    @TableField("intr_id")
    private Integer intrId;

    /** 资产二维码 */
    @TableField("uid")
    private String uid;

    /** 任务ID */
    @TableField("task_id")
    private Integer taskId;

    /** 是否手工盘点 0 or others不是 1 是*/
    @TableField("is_manual")
    private Integer isManual;


    /** 需要手工盘点的资产 */
    @TableField(exist = false)
    private List<String> uids;

    @TableField(exist = false)
    private Integer key;

}

package com.jp.med.ams.modules.property.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.ams.modules.property.dto.AmsSourceAmountSplitDto;
import com.jp.med.ams.modules.property.vo.AmsSourceAmountSplitVo;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;

/**
 * 资产金额拆分
 *
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 21:04:01
 */
public interface AmsSourceAmountSplitReadService extends IService<AmsSourceAmountSplitDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<AmsSourceAmountSplitVo> queryList(AmsSourceAmountSplitDto dto);

    /**
     * 分页查询列表
     *
     * @param dto
     * @return
     */
    List<AmsSourceAmountSplitVo> queryPageList(AmsSourceAmountSplitDto dto);

    List<HashMap<String, BigDecimal>> querySplitAmount(AmsSourceAmountSplitDto dto);
}


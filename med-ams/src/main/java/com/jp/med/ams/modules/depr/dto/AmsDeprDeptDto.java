package com.jp.med.ams.modules.depr.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 存储部门信息，包括折旧额和其他相关数据
 * <AUTHOR>
 * @email -
 * @date 2024-07-19 16:16:26
 */
@Data
@TableName("ams_depr_dept" )
public class AmsDeprDeptDto extends CommonQueryDto {

    /**
     * 自增主键
     */
    @TableId("id")
    private Integer id;

    /**
     * 科室编码
     */
    @TableField("deptusecode")
    private String deptusecode;

    /**
     * 科室名称
     */
    @TableField("deptusename")
    private String deptusename;

    /**
     * 折旧额
     */
    @TableField("depramt")
    private BigDecimal depramt;

    /** 期号 */
    @TableField("ym")
    private String ym;

    /**
     * 创建时间
     */
    @TableField("creattime")
    private Date creattime;

    /** 是否已生成凭证 */
    @TableField("vg")
    private String vg;

    /**
     * 有效标志
     */
    @TableField("activeflag")
    private String activeflag;

}

package com.jp.med.ams.modules.inventory.vo;

import lombok.Data;

/**
 * 盘点数据明细表
 * <AUTHOR>
 * @email -
 * @date 2023-09-22 19:36:52
 */
@Data
public class AmsIntrDetailVo {

	/** ID */
	private Integer id;
	/** 唯一键 */
	private String key;
	/** 盘点ID */
	private Integer intrId;
	/** 资产二维码 */
	private String uid;
	/** 设备编码 */
	private Integer assetId;
	/** 设备编码 */
	private String assetCode;
	/** 设备名称 */
	private String assetName;
	/** 是否盘点到 */
	private String isIntr;
	/** 盘点时间 */
	private String synctime;
	/** 盘点任务名称 */
	private String intrTask;
	/** 固定资产码 */
	private String faCode;
	/** 使用科室 */
	private String deptUse;
	/** 存放位置 */
	private String storageArea;
	/**  使用科室名称 */
	private String orgName;
	/** 是否盘点 */
	private String isIntv;

	/**
	 * 是否手工盘点 0 or others不是 1 是
	 */
	private String isManual;

	private String assetNav;

	/** 资产类型(新分类) */
	private String assetTypeNName;
	/**盘点任务名称*/
	private String taskName;
    /*备注*/
    private String remark;

}

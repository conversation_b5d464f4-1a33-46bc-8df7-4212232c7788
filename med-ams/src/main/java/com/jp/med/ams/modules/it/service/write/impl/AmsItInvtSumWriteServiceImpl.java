package com.jp.med.ams.modules.it.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.it.mapper.write.AmsItInvtSumWriteMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtSumDto;
import com.jp.med.ams.modules.it.service.write.AmsItInvtSumWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 耗材库存汇总
 * <AUTHOR>
 * @email -
 * @date 2023-11-07 15:02:30
 */
@Service
@Transactional(readOnly = false)
public class AmsItInvtSumWriteServiceImpl extends ServiceImpl<AmsItInvtSumWriteMapper, AmsItInvtSumDto> implements AmsItInvtSumWriteService {
}

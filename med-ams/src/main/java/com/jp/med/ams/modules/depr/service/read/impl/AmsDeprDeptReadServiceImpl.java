package com.jp.med.ams.modules.depr.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.ams.modules.depr.dto.AmsDeprDeptDto;
import com.jp.med.ams.modules.depr.mapper.read.AmsDeprDeptReadMapper;
import com.jp.med.ams.modules.depr.mapper.read.AmsPropertyDeprReadMapper;
import com.jp.med.ams.modules.depr.service.read.AmsDeprDeptReadService;
import com.jp.med.ams.modules.depr.vo.AmsDeprDeptVo;
import com.jp.med.ams.modules.property.dto.AmsPropertyDto;
import com.jp.med.ams.modules.property.mapper.read.AmsPropertyReadMapper;
import com.jp.med.ams.modules.property.vo.AmsPropertyVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Calendar;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Transactional
@Service
public class AmsDeprDeptReadServiceImpl extends ServiceImpl<AmsDeprDeptReadMapper, AmsDeprDeptDto>
        implements AmsDeprDeptReadService {

    @Autowired
    private AmsPropertyDeprReadMapper amsPropertyDeprReadMapper;

    @Autowired
    private AmsPropertyReadMapper amsPropertyReadMapper;

    /**
     * 资产折旧汇总方法
     *
     * @return Map<String, Object> 包含借方和贷方汇总数据
     */
    public Map<String, Object> summarizeAssets() {
        AmsPropertyDto amsPropertyDto = new AmsPropertyDto();
        amsPropertyDto.setType("1");
        amsPropertyDto.setIsCanc("0");
        amsPropertyDto.setIsChk("1");
        // 查询所有资产
        List<AmsPropertyVo> assets = amsPropertyReadMapper.queryList(amsPropertyDto);

        // 初始化借方汇总数据结构
        Map<String, Map<String, Map<String, Map<String, Map<String, Map<String, Object>>>>>> debitSummary = new HashMap<>();

        // 初始化贷方汇总数据结构
        Map<String, Map<String, Map<String, Map<String, Object>>>> creditSummary = new HashMap<>();

        // 资金来源映射
        Map<String, String> sourceMapping = new HashMap<>();
        sourceMapping.put("1", "自有资金");
        sourceMapping.put("2", "财政补助资金");
        sourceMapping.put("3", "财政补助资金/自有资金");
        sourceMapping.put("4", "捐赠资金");
        sourceMapping.put("5", "其他资金");

        // 科室分类映射
        Map<String, Map<String, String>> deptMapping = new HashMap<>();
        deptMapping.put("1", createDeptInfo("门、急诊临床科室", "临床科室", "临床科室", "0100101"));
        deptMapping.put("2", createDeptInfo("住院临床科室", "临床科室", "临床科室", "0100102"));
        deptMapping.put("3", createDeptInfo("医技科室", "医技科室", "临床科室", "0100103"));
        deptMapping.put("4", createDeptInfo("医辅科室", "职能科室", "管理科室", "04001"));
        deptMapping.put("5", createDeptInfo("行政后勤", "职能科室", "管理科室", "04010"));

        // 资产类型映射
        Map<String, String> assetTypeMapping = new HashMap<>();
        assetTypeMapping.put("1", "房屋及建筑物");
        assetTypeMapping.put("2", "专用设备");
        assetTypeMapping.put("4", "通用设备");
        assetTypeMapping.put("3", "家具、用具、装具及动植物");
        assetTypeMapping.put("5", "图书档案");
        assetTypeMapping.put("6", "文物和陈列物");
        assetTypeMapping.put("701", "土地使用权");
        assetTypeMapping.put("7", "其他无形资产");
        assetTypeMapping.put("702", "其他无形资产");

        // 处理每个资产
        for (AmsPropertyVo asset : assets) {
            try {
                processAsset(asset, debitSummary, creditSummary, sourceMapping, deptMapping, assetTypeMapping);
            } catch (Exception e) {
                // 记录处理失败的资产
                log.error("处理资产失败: " + asset.getAssetCode() + ", 错误: " + e.getMessage());
            }
        }

        // 返回汇总结果
        Map<String, Object> result = new HashMap<>();
        result.put("debitSummary", debitSummary);
        result.put("creditSummary", creditSummary);
        return result;
    }

    private Map<String, String> createDeptInfo(String name, String type, String mergeType, String code) {
        Map<String, String> info = new HashMap<>();
        info.put("name", name);
        info.put("type", type);
        info.put("mergeType", mergeType);
        info.put("code", code);
        return info;
    }

    private void processAsset(AmsPropertyVo asset,
                              Map<String, Map<String, Map<String, Map<String, Map<String, Map<String, Object>>>>>> debitSummary,
                              Map<String, Map<String, Map<String, Map<String, Object>>>> creditSummary,
                              Map<String, String> sourceMapping,
                              Map<String, Map<String, String>> deptMapping,
                              Map<String, String> assetTypeMapping) {

        // 获取资产基本信息
        String deptType = getDeptType(asset.getDept(), deptMapping);
        String assetCategory = getAssetCategory(asset.getAssetType());
        String sourceType = sourceMapping.getOrDefault(asset.getSource(), "自有资金");
        String useYear = isCurrentYear(asset.getInptDate()) ? "当期年度" : "以前年度";
        String deptName = asset.getDeptUseName();
        BigDecimal amount = asset.getDep() != null ? asset.getDep() : BigDecimal.ZERO;

        // 更新借方汇总
        updateDebitSummary(debitSummary, deptType, assetCategory, sourceType, useYear, deptName, amount);

        // 更新贷方汇总
        updateCreditSummary(creditSummary, assetCategory,
                getAssetSubcategory(asset.getAssetType(), assetTypeMapping), sourceType, amount);
    }

    private String getDeptType(String deptCode, Map<String, Map<String, String>> deptMapping) {
        if (deptCode != null && !deptCode.isEmpty()) {
            Map<String, String> deptInfo = deptMapping.get(deptCode.substring(0, 1));
            return deptInfo != null ? deptInfo.get("type") : "其他";
        }
        return "其他";
    }

    private String getAssetCategory(String assetType) {
        if (assetType != null && (assetType.startsWith("7"))) {
            return "无形资产";
        }
        return "固定资产";
    }

    private String getAssetSubcategory(String assetType, Map<String, String> assetTypeMapping) {
        if (assetType != null) {
            if (assetType.startsWith("7")) {
                return assetTypeMapping.getOrDefault(assetType, "其他");
            }
            return assetTypeMapping.getOrDefault(assetType.substring(0, 1), "其他");
        }
        return "其他";
    }

    private boolean isCurrentYear(String inptDate) {
        if (inptDate == null)
            return false;
        Calendar cal = Calendar.getInstance();
        int currentYear = cal.get(Calendar.YEAR);
        return inptDate.startsWith(String.valueOf(currentYear));
    }

    private void updateDebitSummary(
            Map<String, Map<String, Map<String, Map<String, Map<String, Map<String, Object>>>>>> debitSummary,
            String deptType, String assetCategory, String sourceType, String useYear, String deptName,
            BigDecimal amount) {
        debitSummary
                .computeIfAbsent(deptType, k -> new HashMap<>())
                .computeIfAbsent(assetCategory, k -> new HashMap<>())
                .computeIfAbsent(sourceType, k -> new HashMap<>())
                .computeIfAbsent(useYear, k -> new HashMap<>())
                .computeIfAbsent(deptName, k -> new HashMap<>())
                .merge("count", 1, (oldValue, newValue) -> ((Integer) oldValue) + 1);

        debitSummary.get(deptType)
                .get(assetCategory)
                .get(sourceType)
                .get(useYear)
                .get(deptName)
                .merge("total_amount", amount,
                        (oldValue, newValue) -> ((BigDecimal) oldValue).add((BigDecimal) newValue));
    }

    private void updateCreditSummary(Map<String, Map<String, Map<String, Map<String, Object>>>> creditSummary,
                                     String assetCategory, String assetSubcategory, String sourceType, BigDecimal amount) {
        creditSummary
                .computeIfAbsent(assetCategory, k -> new HashMap<>())
                .computeIfAbsent(assetSubcategory, k -> new HashMap<>())
                .computeIfAbsent(sourceType, k -> new HashMap<>())
                .merge("count", 1, (oldValue, newValue) -> ((Integer) oldValue) + 1);

        creditSummary.get(assetCategory)
                .get(assetSubcategory)
                .get(sourceType)
                .merge("total_amount", amount,
                        (oldValue, newValue) -> ((BigDecimal) oldValue).add((BigDecimal) newValue));
    }

    @Override
    public List<AmsDeprDeptVo> queryList(AmsDeprDeptDto dto) {
        return null;
    }

    @Override
    public List<AmsDeprDeptVo> queryPageList(AmsDeprDeptDto dto) {
        return List.of();
    }

}

package com.jp.med.ams.modules.it.service.write.impl;
import cn.hutool.core.collection.CollectionUtil;
import com.jp.med.ams.modules.it.dto.AmsItInvtSumDto;
import com.jp.med.ams.modules.it.dto.ModifyFileDto;
import com.jp.med.ams.modules.it.mapper.read.AmsItInvtSumReadMapper;
import com.jp.med.ams.modules.it.mapper.read.AmsItInvtSupplementlistReadMapper;
import com.jp.med.ams.modules.it.service.write.AmsItInvtSumWriteService;
import com.jp.med.ams.modules.it.vo.AmsItInvtSumVo;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.EasyPoiUtil;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.common.util.ULIDUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.ams.modules.it.mapper.write.AmsItInvtSupplementlistWriteMapper;
import com.jp.med.ams.modules.it.dto.AmsItInvtSupplementlistDto;
import com.jp.med.ams.modules.it.service.write.AmsItInvtSupplementlistWriteService;
import org.springframework.transaction.annotation.Transactional;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @email -
 * @date 2023-12-14 17:42:02
 */
@Service
@Transactional(readOnly = false)
public class AmsItInvtSupplementlistWriteServiceImpl extends ServiceImpl<AmsItInvtSupplementlistWriteMapper, AmsItInvtSupplementlistDto> implements AmsItInvtSupplementlistWriteService {

    @Autowired
    private AmsItInvtSumReadMapper amsItInvtSumReadMapper;

    @Autowired
    private AmsItInvtSupplementlistReadMapper amsItInvtSLReadMapper;

    @Autowired
    private AmsItInvtSupplementlistWriteMapper amsItInvtSWriteMapper;

    @Autowired
    private AmsItInvtSupplementlistWriteService amsItInvtSWService;

    /** 保存要货清单(根据id集合 ids) */
    @Override
    public void saveSpullement(AmsItInvtSupplementlistDto dto) {
        AmsItInvtSumDto amsItInvtSumDto = new AmsItInvtSumDto();
        // 获取id集合
        amsItInvtSumDto.setIds(dto.getIds());
        List<AmsItInvtSumVo> amsItInvtSumVos = amsItInvtSumReadMapper.queryCheckedList(amsItInvtSumDto);
        AmsItInvtSupplementlistDto amsSLdto = new AmsItInvtSupplementlistDto();
        // 一样的uuid代表同一批次
        String UUID = ULIDUtil.generate();
        // 员工工号
        String empCode = dto.getSysUser().getHrmUser().getEmpCode();
        // 当前时间
        String format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        for (AmsItInvtSumVo vo:amsItInvtSumVos) {
            //按最大id
           Integer maxId = amsItInvtSLReadMapper.queryMaxId();
           if (Objects.isNull(maxId)){
               amsSLdto.setId(0);
           }else {
               amsSLdto.setId(maxId + 1);
           }
           amsSLdto.setName2(vo.getName2());
           amsSLdto.setModel2(vo.getModel2());
           amsSLdto.setNumber(vo.getNumber());
           amsSLdto.setCter(empCode);
           amsSLdto.setCreateTime(format);
           amsSLdto.setUpder(empCode);
           amsSLdto.setUpdateTime(format);
           amsSLdto.setActiveFlag(0);
           amsSLdto.setHospitalId("zjxrmyy");
           //保存一个标志位（批次号）,相同表示同一批次
            amsSLdto.setBatchNumber(UUID);
            amsItInvtSWService.save(amsSLdto);
        }
    }

    @Override
    public void saveSpullements(List<AmsItInvtSupplementlistDto> dtos) {
        // 一样的uuid代表同一批次
        String UUID = ULIDUtil.generate();

        // 当前时间
        String format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date());
        for (AmsItInvtSupplementlistDto dto: dtos) {
            // 员工工号
//            String empCode = dto.getSysUser().getHrmUser().getEmpCode();
            //按最大id
            Integer maxId = amsItInvtSLReadMapper.queryMaxId();
            if (Objects.isNull(maxId)){
                dto.setId(0);
            }else {
                dto.setId(maxId + 1);
            }

            dto.setName2(dto.getName2());
            dto.setModel2(dto.getModel2());
            dto.setNumber(dto.getNumber());
//            dto.setCter(dto.getSysUser().getHrmUser().getEmpCode());
            dto.setCreateTime(format);
//            dto.setUpder(dto.getSysUser().getHrmUser().getEmpCode());
            dto.setUpdateTime(format);
            dto.setActiveFlag(0);
            dto.setHospitalId("zjxrmyy");
            //保存一个标志位（批次号）,相同表示同一批次
            dto.setBatchNumber(UUID);
            amsItInvtSWService.save(dto);
        }


    }

    @Override
    public void updateAssessFiles(AmsItInvtSupplementlistDto dto) {
        List<ModifyFileDto> modifyFileDetails = dto.getModifyFileDetails();
        List<ModifyFileDto> modifyLogInfo = new ArrayList<>();
        if (!modifyFileDetails.isEmpty()) {
            modifyFileDetails.forEach(item -> {
                uploadFileToOss(item);
            });
            amsItInvtSWriteMapper.batchUpdateFile(modifyFileDetails);
        }
    }
    private void uploadFileToOss(ModifyFileDto mf) {
        StringBuilder filePaths = new StringBuilder();
        StringBuilder fileNames = new StringBuilder();
        if (CollectionUtil.isNotEmpty(mf.getFiles())) {
            mf.getFiles().forEach(file -> {
                String filePath = OSSUtil.uploadFile(OSSConst.BUCKET_HRM, mf.getTitle() + "/", file);
                filePaths.append(filePath);
                filePaths.append(",");
                fileNames.append(file.getOriginalFilename());
                fileNames.append(",");
            });
        }
        mf.setFilePath(filePaths.toString());
        mf.setFileName(fileNames.toString());
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.read.AmsSourceAmountSplitReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ams.modules.property.vo.AmsSourceAmountSplitVo" id="sourceAmountSplitMap">
        <result property="id" column="id"/>
        <result property="faCode" column="fa_code"/>
        <result property="createdAt" column="created_at"/>
        <result property="updatedAt" column="updated_at"/>
        <result property="sourceType" column="source_type"/>
        <result property="splitAmount1" column="split_amount_1"/>
        <result property="splitAmount2" column="split_amount_2"/>
        <result property="splitAmount3" column="split_amount_3"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ams.modules.property.vo.AmsSourceAmountSplitVo">
        select
            s.id as id,
            s.fa_code as faCode,
            s.created_at as createdAt, 
            s.updated_at as updatedAt,
            s.source_type as sourceType,
            s.split_amount_1 as splitAmount1,
            s.split_type_1 as splitType1,
            s.split_amount_2 as splitAmount2,
            s.split_type_2 as splitType2,
            s.split_amount_3 as splitAmount3,
            s.split_type_3 as splitType3,
            p.asset_name as assetName,


            p.asset_nav as assetNav

        from ams_source_amount_split s
        left join ams_property p on s.fa_code = p.fa_code
        <where>
            <!-- 资产代码 -->
            <if test="faCode != null and faCode != ''">
                s.fa_code = #{faCode}
            </if>
            <!-- 资金来源 -->
            <if test="sourceType != null and sourceType != ''">
                and s.source_type = #{sourceType}
            </if>
        </where>
    </select>

</mapper>

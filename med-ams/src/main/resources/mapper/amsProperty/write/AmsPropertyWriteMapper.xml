<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.write.AmsPropertyWriteMapper">
    <insert id="insertAmsProperty"> </insert>
    <insert id="insertAmsPropertyType"> </insert>

    <update id="updateByFaCode"> update ams_property set asset_status =
        #{assetStatus,jdbcType=VARCHAR} where fa_code = #{faCode,jdbcType=VARCHAR} </update>
    <update id="confirmInfoChange"> update ams_property set ${chgCode} =
        #{chgAfter,jdbcType=VARCHAR} where fa_code = #{faCode,jdbcType=VARCHAR} </update>
    <update id="confirmInfoChange2"> update ams_property set ${chgCode} = case when (SELECT
        pg_type.typname AS dataType FROM pg_class AS t1, pg_attribute AS t2 INNER JOIN pg_type ON
        pg_type.OID = t2.atttypid LEFT JOIN pg_constraint t3 ON t2.attnum = t3.conkey[1] AND
        t2.attrelid = t3.conrelid WHERE t1.relname = 'ams_property' AND t2.attrelid = t1.OID AND
        t2.attnum > 0 AND t2.attname = #{chgCode,jdbcType=VARCHAR}) = 'varchar' then
        #{chgAfter,jdbcType=VARCHAR} else CAST(#{chgAfter,jdbcType=VARCHAR} AS numeric) end where
        fa_code = #{faCode,jdbcType=VARCHAR} </update>
    <update id="confirmInfoChangeVarchar"> update ams_property set ${chgCode} =
        #{chgAfter,jdbcType=VARCHAR} where fa_code = #{faCode,jdbcType=VARCHAR} </update>
    <update id="confirmInfoChangeNumeric"> update ams_property set ${chgCode} =
        #{chgAfter,jdbcType=VARCHAR}::numeric where fa_code = #{faCode,jdbcType=VARCHAR} </update>
    <update id="confirmInfoChangeRollBackVarchar"> update ams_property set ${chgCode} =
        #{chgBefore,jdbcType=VARCHAR} where fa_code = #{faCode,jdbcType=VARCHAR} </update>
    <update id="confirmInfoChangeRollBackNumeric"> update ams_property set ${chgCode} =
        #{chgBefore,jdbcType=VARCHAR}::numeric where fa_code = #{faCode,jdbcType=VARCHAR} </update>

    <delete id="deleteByCode"> delete from ams_property where id = #{id,jdbcType=INTEGER} and is_chk
        != '1' </delete>

    <select id="queryAmsPropertyType" resultType="java.lang.String"> SELECT pg_type.typname AS
        dataType FROM pg_class AS t1, pg_attribute AS t2 INNER JOIN pg_type ON pg_type.OID =
        t2.atttypid LEFT JOIN pg_constraint t3 ON t2.attnum = t3.conkey[1] AND t2.attrelid =
        t3.conrelid WHERE t1.relname = 'ams_property' AND t2.attrelid = t1.OID AND t2.attnum > 0 AND
        t2.attname = #{columnname,jdbcType=VARCHAR} </select>

    <!--    更新资产信息-->
    <update id="updateAssetInfo"> </update>
    <update id="scrap"> update ams_property set <!--         is_canc = '1',--> asset_status = '3' where fa_code in <foreach
            collection="facodeList" item="faCode" separator="," open="(" close=")">
        #{faCode,jdbcType=VARCHAR} </foreach>
    </update>

    <update id="updateAssetStatus"> UPDATE ams_property SET asset_status = #{status} WHERE fa_code =
        #{faCode} </update>

    <update id="updateDeptUseByFaCode"> UPDATE ams_property SET dept_use = #{deptUse} WHERE fa_code
        = #{faCode} </update>

    <!--        UPDATE ams_property a-->
    <!--        SET dept_use = b.traf_in_dept-->
    <!--        FROM ams_out_stock_audit  b-->
    <!--        WHERE a.fa_code = b.fa_code-->
    <!--          AND b.bchno = #{bchno,jdbcType=VARCHAR}-->
</mapper>
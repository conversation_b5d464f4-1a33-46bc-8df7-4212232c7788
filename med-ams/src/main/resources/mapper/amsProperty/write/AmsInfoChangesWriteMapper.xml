<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.write.AmsInfoChangesWriteMapper">

    <insert id="insertBatch">
        insert into ams_info_changes(
          fa_code,
          chg_code,
          chg_name,
          chg_before,
          chg_before_name,
          chg_after,
          chg_after_name,
          chg_date,
          chger,
          chger_name,
          status,
          hospital_id
        ) VALUES (
           #{faCode,jdbcType=VARCHAR},
           #{chgCode,jdbcType=VARCHAR},
           #{chgName,jdbcType=VARCHAR},
           #{chgBefore,jdbcType=VARCHAR},
           #{chgBeforeName,jdbcType=VARCHAR},
           #{chgAfter,jdbcType=VARCHAR},
           #{chgAfterName,jdbcType=VARCHAR},
           #{chgDate,jdbcType=VARCHAR},
           #{chger,jdbcType=VARCHAR},
           #{chgerName,jdbcType=VARCHAR},
           #{status,jdbcType=VARCHAR},
           #{hospitalId,jdbcType=VARCHAR}
        )
    </insert>

    <update id="updateMsg">
        update ams_property set
        <foreach collection="list" item="item" separator=",">
            ${item.chgCode} = #{item.chgAfter}
        </foreach>
        where fa_code = #{faCode,jdbcType=VARCHAR}
    </update>
  <update id="changeInfoStatus">
    update ams_info_changes
        set status = #{status,jdbcType=VARCHAR} ,
            chker=#{chker,jdbcType=VARCHAR},
            chker_name=#{chkerName,jdbcType=VARCHAR},
            chktime=#{updteTime,jdbcType=VARCHAR}
    where id in
      <foreach collection="ids" item="id" separator="," open="(" close=")">
        #{id}
      </foreach>
  </update>
</mapper>

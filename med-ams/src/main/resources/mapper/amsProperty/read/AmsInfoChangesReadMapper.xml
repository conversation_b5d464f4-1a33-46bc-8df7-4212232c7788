<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.read.AmsInfoChangesReadMapper">
    <select id="queryList" resultType="com.jp.med.ams.modules.property.vo.AmsInfoChangesVo">
        select a.id                                as id,
               b.asset_code                        as assetCode,
               b.asset_name                        as assetName,
               a.fa_code                           as faCode,
               a.chg_code                          as chgCode,
               a.chg_name                          as chgName,
               a.chg_before                        as chgBefore,
               a.chg_before_name                   as chgBeforeName,
               a.chg_after                         as chgAfter,
               a.chg_after_name                    as chgAfterName,
               a.chg_date                          as chgDate,
               a.chger                             as chger,
               a.chger_name                        as chgerName,
               a.chker                             as chker,
               a.chker_name                        as chkerName,
               a.status                            as status,
               a.hospital_id                       as hospitalId,
               jsonb_build_object('property', b.*) as propertyJsonData
        from ams_info_changes a
                 left join ams_property b
                           on a.fa_code = b.fa_code
        <where>
            <if test="asset != '' and asset != null">
                and (a.fa_code like concat('%', concat(#{asset,jdbcType=VARCHAR}, '%')) or
                     b.asset_name like concat('%', concat(#{asset,jdbcType=VARCHAR}, '%')))
            </if>
            <if test="chgDateRange != null and chgDateRange.length > 0">
                <![CDATA[
                and (a.chg_date >= #{chgDateRange.[0],jdbcType=VARCHAR} and
                     a.chg_date <= #{chgDateRange.[1],jdbcType=VARCHAR})
                ]]>
            </if>
            <if test="chgCode != '' and chgCode != null">
                and a.chg_code = #{chgCode,jdbcType=VARCHAR}
            </if>
            <if test="faCode != '' and faCode != null">
                and a.fa_code = #{faCode,jdbcType=VARCHAR}
            </if>

            <!--    /** 变更前 */-->
            <if test="chgBefore != '' and chgBefore != null">
                and a.chg_before like concat('%', concat(#{chgBefore,jdbcType=VARCHAR}, '%'))
            </if>
            <!--    /** 变更后 */-->
            <if test="chgAfter != '' and chgAfter != null">
                and a.chg_after like concat('%', concat(#{chgAfter,jdbcType=VARCHAR}, '%'))
            </if>
        </where>
        order by a.id desc
    </select>
</mapper>

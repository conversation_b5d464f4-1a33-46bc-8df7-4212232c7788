<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.ams.modules.property.mapper.read.AmsScrapToBeIdentifiedReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.ams.modules.property.vo.AmsScrapToBeIdentifiedVo" id="scrapToBeIdentifiedMap">
        <result property="id" column="id"/>
        <result property="opter" column="opter"/>
        <result property="applyDocno" column="apply_docno"/>
        <result property="faCode" column="fa_code"/>
        <result property="activeFlag" column="active_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.ams.modules.property.vo.AmsScrapToBeIdentifiedVo">
        select id           as id,
               opter        as opter,
               apply_docno  as applyDocno,
               fa_code      as faCode,
               active_flag  as activeFlag,
               ams_scrap_id as amsScrapId
        from ams_scrap_to_be_identified
    </select>
</mapper>

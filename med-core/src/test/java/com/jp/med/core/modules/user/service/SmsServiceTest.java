package com.jp.med.core.modules.user.service;

import com.jp.med.core.modules.user.service.impl.SmsServiceImpl;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 短信服务测试类
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/01
 */
@ExtendWith(MockitoExtension.class)
public class SmsServiceTest {

    @InjectMocks
    private SmsServiceImpl smsService;

    @Test
    public void testGenerateSmsCode() {
        String code = smsService.generateSmsCode();
        assertNotNull(code);
        assertEquals(6, code.length());
        assertTrue(code.matches("\\d{6}"));
    }

    @Test
    public void testSendSmsCode() {
        String phoneNumber = "13800138000";
        String code = "123456";
        
        // 由于是模拟发送，应该返回true
        boolean result = smsService.sendSmsCode(phoneNumber, code);
        assertTrue(result);
    }
}

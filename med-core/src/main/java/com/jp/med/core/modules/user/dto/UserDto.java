package com.jp.med.core.modules.user.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.emp.EmpEmployeeInfoDto;
import com.jp.med.common.dto.emp.HrmOrgDto;
import com.jp.med.common.dto.user.CommonUserQueryDto;
import com.jp.med.common.entity.sys.SysMenu;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/15 11:02
 * @description: 用户查询接口
 */
@Getter
@Setter
@NoArgsConstructor
@TableName("sys_user")
public class UserDto extends CommonUserQueryDto {
    /** id */
    @TableId("user_id")
    private Long id;

    /** 用户名 */
    @TableField("username")
    private String username;

    /** 盐 */
    @TableField("salt")
    private String salt;

    /** 邮箱 */
    @TableField("email")
    private String email;

    /** 手机号 */
    @TableField("mobile")
    private String mobile;

    /** 状态  0：禁用   1：正常 */
    @TableField("status")
    private int status;

    /** 创建者ID */
    @TableField("create_user_id")
    private long createUserId;

    /** 创建时间 */
    @TableField("create_time")
    private Date createTime;

    /** 医疗机构编号 */
    @TableField("hospital_id")
    private String hospitalId;

    /**组织架构id  */
    @TableField("organization_id")
    private String organizationId;

    /**组织架构id  */
    @TableField(exist = false)
    private String[] orgIds;

    /** 查询，用户名  */
    @TableField(exist = false)
    private String uname;

    /** 角色id  */
    @TableField(exist = false)
    private int[] roleIds;

    /** 数据角色id  */
    @TableField(exist = false)
    private int[] roleDataIds;

    /** 归集角色  */
    @TableField(exist = false)
    private List<Integer> collRoleIds;

    /** 归集角色字符串 */
    @TableField("sys_coll_role_id_str")
    private String collRoleIdsStr;

    /** 签名密码 */
    @TableField("sign_password")
    private String signPassword;

    /** 角色数量  */
    @TableField(exist = false)
    private int roleNum;

    /** 是否跳过菜单是按钮的判断 */
    private boolean skip;

    /**
     * 设置用户名
     * @param username 用户名
     */
    public UserDto(String username){
        setUsername(username);
    }

    /** 旧密码 */
    public String oldPassword;

    /** id 集合 */
    @TableField(exist = false)
    private String[] ids;

    /** 系统菜单查询条件 */
    @TableField(exist = false)
    private SysMenu sysMenu;

    /** 用户id集合 */
    private List<Long> userIds;

    /** 员工id集合 */
    private List<Long> empIds;

    /** 数据角色id */
    private Long roleDataId;

    /** 是否退出所有用户 */
    private Boolean exitAllUser;

    /** 系统ID */
    private Long systemId;

    /** hrm组织架构 */
    private List<HrmOrgDto> hrmOrgDtos;

    /** hrm员工信息集合 */
    private List<EmpEmployeeInfoDto> hrmEmps;

    /** 科室 */
    private String dept;

    /** 是否锁住 */
    private String isLock;

    /** 白名单 */
    private String whitelist;

    /** 密码错误次数 */
    private Integer passwordErrorNum;

    /** 是否批量修改 */
    private Boolean batchModify;

    /** 用户名 */
    private List<String> usernameList;

    /** 短信验证码 */
    @TableField(exist = false)
    private String smsCode;
}

package com.jp.med.core.modules.user.service;

/**
 * 短信服务接口
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/01
 * @description: 短信验证码发送服务
 */
public interface SmsService {

    /**
     * 发送短信验证码
     * @param phoneNumber 手机号
     * @param code 验证码
     * @return 发送结果
     */
    boolean sendSmsCode(String phoneNumber, String code);

    /**
     * 生成6位数字验证码
     * @return 验证码
     */
    String generateSmsCode();

    /**
     * 验证短信验证码
     * @param phoneNumber 手机号
     * @param code 验证码
     * @return 验证结果
     */
    boolean verifySmsCode(String phoneNumber, String code);

    /**
     * 存储验证码到Redis
     * @param phoneNumber 手机号
     * @param code 验证码
     * @param expireMinutes 过期时间（分钟）
     */
    void storeSmsCode(String phoneNumber, String code, int expireMinutes);
}

package com.jp.med.core.modules.user.feign.sms;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.core.modules.user.dto.VerificationCodeDto;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(name = "sms", url = "${url,mid.sms}")
public interface SmsFeignService {

    @PostMapping("/sendCode")
    CommonResult<Boolean> sendSmsCode(@RequestBody VerificationCodeDto dto);
}

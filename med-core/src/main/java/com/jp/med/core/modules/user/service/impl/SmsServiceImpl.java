package com.jp.med.core.modules.user.service.impl;

import com.jp.med.common.util.RedisUtil;
import com.jp.med.core.modules.user.service.SmsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import java.util.Random;

/**
 * 短信服务实现类
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/01
 * @description: 短信验证码发送服务实现
 */
@Slf4j
@Service
public class SmsServiceImpl implements SmsService {

    private static final String SMS_CODE_PREFIX = "SMS_CODE:";
    private static final String CHARACTERS = "0123456789";
    private static final int CODE_LENGTH = 6;

    @Override
    public boolean sendSmsCode(String phoneNumber, String code) {
        try {
            // TODO: 这里预留远程调用第三方短信API的接口
            // 示例：调用阿里云短信、腾讯云短信等第三方服务
            log.info("发送短信验证码到手机号: {}, 验证码: {}", phoneNumber, code);
            
            // 模拟发送成功
            // 实际实现时，这里应该调用具体的短信服务提供商API
            // 例如：
            // SmsResponse response = smsClient.sendSms(phoneNumber, code);
            // return response.isSuccess();
            
            return true;
        } catch (Exception e) {
            log.error("发送短信验证码失败，手机号: {}, 错误信息: {}", phoneNumber, e.getMessage());
            return false;
        }
    }

    @Override
    public String generateSmsCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        Random random = new Random();
        
        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = random.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(index));
        }
        
        return code.toString();
    }

    @Override
    public boolean verifySmsCode(String phoneNumber, String code) {
        if (StringUtils.isEmpty(phoneNumber) || StringUtils.isEmpty(code)) {
            return false;
        }
        
        String redisKey = SMS_CODE_PREFIX + phoneNumber;
        String storedCode = (String) RedisUtil.get(redisKey);
        
        if (StringUtils.isEmpty(storedCode)) {
            log.warn("验证码已过期或不存在，手机号: {}", phoneNumber);
            return false;
        }
        
        boolean isValid = code.equals(storedCode);
        if (isValid) {
            // 验证成功后删除验证码
            RedisUtil.del(redisKey);
            log.info("短信验证码验证成功，手机号: {}", phoneNumber);
        } else {
            log.warn("短信验证码验证失败，手机号: {}, 输入验证码: {}, 存储验证码: {}", phoneNumber, code, storedCode);
        }
        
        return isValid;
    }

    @Override
    public void storeSmsCode(String phoneNumber, String code, int expireMinutes) {
        String redisKey = SMS_CODE_PREFIX + phoneNumber;
        RedisUtil.set(redisKey, code, expireMinutes);
        log.info("短信验证码已存储到Redis，手机号: {}, 过期时间: {}分钟", phoneNumber, expireMinutes);
    }
}

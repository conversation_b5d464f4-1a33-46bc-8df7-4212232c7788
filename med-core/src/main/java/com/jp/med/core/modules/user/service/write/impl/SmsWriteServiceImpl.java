package com.jp.med.core.modules.user.service.write.impl;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.core.modules.user.dto.VerificationCodeDto;
import com.jp.med.core.modules.user.feign.sms.SmsFeignService;
import com.jp.med.core.modules.user.service.write.SmsWriteService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class SmsWriteServiceImpl implements SmsWriteService {
    @Resource
    private SmsFeignService smsFeignService;

    @Override
    public boolean sendSmsCode(String phoneNumber, String code) {
        CommonResult<Boolean> booleanCommonResult = smsFeignService.sendSmsCode(VerificationCodeDto.createLoginCode(phoneNumber));
        if (booleanCommonResult.getCode()==200) {
            return true;
        }
        return false;
    }

    @Override
    public String generateSmsCode() {
        return "";
    }

    @Override
    public boolean verifySmsCode(String phoneNumber, String code) {
        return false;
    }

    @Override
    public void storeSmsCode(String phoneNumber, String code, int expireMinutes) {

    }
}

package com.jp.med.core.modules.user.service.write.impl;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.RedisUtil;
import com.jp.med.core.modules.user.dto.SmsDto;
import com.jp.med.core.modules.user.feign.sms.SmsFeignService;
import com.jp.med.core.modules.user.service.write.SmsWriteService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Random;

/**
 * 短信写入服务实现类
 * <AUTHOR>
 * @version 1.0
 * @date 2024/01/01
 * @description: 短信验证码发送和管理服务实现
 */
@Slf4j
@Service
public class SmsWriteServiceImpl implements SmsWriteService {

    @Resource
    private SmsFeignService smsFeignService;

    private static final String SMS_CODE_PREFIX = "SMS_CODE:";
    private static final String CHARACTERS = "0123456789";
    private static final int CODE_LENGTH = 6;
    private static final int DEFAULT_EXPIRE_MINUTES = 5;

    @Override
    public boolean sendSmsCode(String phoneNumber, String code) {
        if (StringUtils.isEmpty(phoneNumber) || StringUtils.isEmpty(code)) {
            log.warn("发送短信验证码失败：手机号或验证码为空");
            return false;
        }

        try {
            // 创建重置密码验证码DTO
            SmsDto dto = SmsDto.createResetPasswordCode(phoneNumber);
            dto.setSmsCode(code);

            // 远程调用短信服务
            CommonResult<Boolean> result = smsFeignService.sendSms(dto);

            if (result != null && result.getCode() == 200 && Boolean.TRUE.equals(result.getData())) {
                log.info("短信验证码发送成功，手机号: {}", phoneNumber);
                return true;
            } else {
                log.warn("短信验证码发送失败，手机号: {}, 响应: {}", phoneNumber, result);
                return false;
            }
        } catch (Exception e) {
            log.error("短信验证码发送异常，手机号: {}, 错误信息: {}", phoneNumber, e.getMessage(), e);
            return false;
        }
    }

    @Override
    public String generateSmsCode() {
        StringBuilder code = new StringBuilder(CODE_LENGTH);
        Random random = new Random();

        for (int i = 0; i < CODE_LENGTH; i++) {
            int index = random.nextInt(CHARACTERS.length());
            code.append(CHARACTERS.charAt(index));
        }

        String generatedCode = code.toString();
        log.debug("生成短信验证码: {}", generatedCode);
        return generatedCode;
    }

    @Override
    public boolean verifySmsCode(String phoneNumber, String code) {
        if (StringUtils.isEmpty(phoneNumber) || StringUtils.isEmpty(code)) {
            log.warn("验证短信验证码失败：手机号或验证码为空");
            return false;
        }

        String redisKey = SMS_CODE_PREFIX + phoneNumber;
        String storedCode = (String) RedisUtil.get(redisKey);

        if (StringUtils.isEmpty(storedCode)) {
            log.warn("验证码已过期或不存在，手机号: {}", phoneNumber);
            return false;
        }

        boolean isValid = code.equals(storedCode);
        if (isValid) {
            // 验证成功后删除验证码
            RedisUtil.del(redisKey);
            log.info("短信验证码验证成功，手机号: {}", phoneNumber);
        } else {
            log.warn("短信验证码验证失败，手机号: {}, 输入验证码: {}, 存储验证码: {}", phoneNumber, code, storedCode);
        }

        return isValid;
    }

    @Override
    public void storeSmsCode(String phoneNumber, String code, int expireMinutes) {
        if (StringUtils.isEmpty(phoneNumber) || StringUtils.isEmpty(code)) {
            log.warn("存储短信验证码失败：手机号或验证码为空");
            return;
        }

        String redisKey = SMS_CODE_PREFIX + phoneNumber;
        RedisUtil.set(redisKey, code, expireMinutes);
        log.info("短信验证码已存储到Redis，手机号: {}, 过期时间: {}分钟", phoneNumber, expireMinutes);
    }

    @Override
    public boolean sendResetPasswordCode(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            log.warn("发送重置密码验证码失败：手机号为空");
            return false;
        }

        // 生成验证码
        String code = generateSmsCode();

        // 存储验证码到Redis
        storeSmsCode(phoneNumber, code, DEFAULT_EXPIRE_MINUTES);

        // 发送验证码
        return sendSmsCode(phoneNumber, code);
    }

    @Override
    public boolean sendLoginCode(String phoneNumber) {
        if (StringUtils.isEmpty(phoneNumber)) {
            log.warn("发送登录验证码失败：手机号为空");
            return false;
        }

        try {
            // 生成验证码
            String code = generateSmsCode();

            // 存储验证码到Redis
            storeSmsCode(phoneNumber, code, DEFAULT_EXPIRE_MINUTES);

            // 创建登录验证码DTO
            SmsDto dto = SmsDto.createLoginCode(phoneNumber);
            dto.setSmsCode(code);

            // 远程调用短信服务
            CommonResult<Boolean> result = smsFeignService.sendSms(dto);

            if (result != null && result.getCode() == 200 && Boolean.TRUE.equals(result.getData())) {
                log.info("登录验证码发送成功，手机号: {}", phoneNumber);
                return true;
            } else {
                log.warn("登录验证码发送失败，手机号: {}, 响应: {}", phoneNumber, result);
                return false;
            }
        } catch (Exception e) {
            log.error("登录验证码发送异常，手机号: {}, 错误信息: {}", phoneNumber, e.getMessage(), e);
            return false;
        }
    }
}
package com.jp.med.core.modules.user.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 验证码短信DTO
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Data
@Accessors(chain = true)
public class VerificationCodeDto {

    /**
     * 手机号
     */
    private String phone;

    /**
     * 验证码
     */
    private String code;

    /**
     * 验证码长度（4-8位）
     */
    private Integer codeLength = 6;

    /**
     * 验证码类型：1-数字，2-字母，3-数字+字母
     */
    private Integer codeType = 1;

    /**
     * 有效期（分钟）
     */
    private Integer expireMinutes = 5;

    /**
     * 业务类型：login-登录，register-注册，reset-重置密码，bind-绑定手机等
     */
    private String businessType;

    /**
     * 用户ID（可选）
     */
    private String userId;

    /**
     * IP地址
     */
    private String ipAddress;

    /**
     * 用户代理
     */
    private String userAgent;

    /**
     * 是否需要图形验证码验证
     */
    private Boolean needCaptcha = false;

    /**
     * 图形验证码
     */
    private String captcha;

    /**
     * 图形验证码Key
     */
    private String captchaKey;

    /**
     * 扩展参数
     */
    private String extData;

    /**
     * 创建登录验证码
     */
    public static VerificationCodeDto createLoginCode(String phone) {
        return new VerificationCodeDto()
                .setPhone(phone)
                .setBusinessType("login")
                .setCodeLength(6)
                .setCodeType(1)
                .setExpireMinutes(5);
    }

    /**
     * 创建注册验证码
     */
    public static VerificationCodeDto createRegisterCode(String phone) {
        return new VerificationCodeDto()
                .setPhone(phone)
                .setBusinessType("register")
                .setCodeLength(6)
                .setCodeType(1)
                .setExpireMinutes(10);
    }

    /**
     * 创建重置密码验证码
     */
    public static VerificationCodeDto createResetPasswordCode(String phone) {
        return new VerificationCodeDto()
                .setPhone(phone)
                .setBusinessType("reset")
                .setCodeLength(6)
                .setCodeType(1)
                .setExpireMinutes(10);
    }

    /**
     * 创建绑定手机验证码
     */
    public static VerificationCodeDto createBindPhoneCode(String phone, String userId) {
        return new VerificationCodeDto()
                .setPhone(phone)
                .setUserId(userId)
                .setBusinessType("bind")
                .setCodeLength(6)
                .setCodeType(1)
                .setExpireMinutes(5);
    }
}

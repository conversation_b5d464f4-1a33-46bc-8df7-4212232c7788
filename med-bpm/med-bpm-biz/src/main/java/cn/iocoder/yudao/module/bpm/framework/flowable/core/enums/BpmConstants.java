package cn.iocoder.yudao.module.bpm.framework.flowable.core.enums;

import org.flowable.engine.runtime.ProcessInstance;

/**
 * BPM 通用常量
 *

 */
public class BpmConstants {

    /**
     * 流程实例的变量 - 状态
     *
     * @see ProcessInstance#getProcessVariables()
     */
    public static final String PROCESS_INSTANCE_VARIABLE_STATUS = "PROCESS_STATUS";
    /**
     * 流程实例的变量 - 发起用户选择的审批人 Map
     *
     * @see ProcessInstance#getProcessVariables()
     */
    public static final String PROCESS_INSTANCE_VARIABLE_START_USER_SELECT_ASSIGNEES = "PROCESS_START_USER_SELECT_ASSIGNEES";

    /**
     * 任务的变量 - 状态
     *
     * @see org.flowable.task.api.Task#getTaskLocalVariables()
     */
    public static final String TASK_VARIABLE_STATUS = "TASK_STATUS";
    /**
     * 任务的变量 - 理由
     * <p>
     * 例如说：审批通过、不通过的理由
     *
     * @see org.flowable.task.api.Task#getTaskLocalVariables()
     */
    public static final String TASK_VARIABLE_REASON = "TASK_REASON";

    /**
     * 审批事由
     */
    public static final String TASK_VARIABLE_CREATE_REASON = "createReason";

    /**
     * 签名图片
     */
    public static final String TASK_VARIABLE_SIGN_URL = "TASK_SIGN_URL";

    /**
     * 附件列表
     */
    public static final String TASK_VARIABLE_ATTACHMENT_LIST = "TASK_ATTACHMENT_LIST";

    /**
     * 下一审批人
     */
    public static final String NEXT_APPROVER = "NEXT_APPROVER";
}

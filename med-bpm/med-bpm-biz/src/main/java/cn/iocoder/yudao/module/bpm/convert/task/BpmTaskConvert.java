package cn.iocoder.yudao.module.bpm.convert.task;

import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.module.bpm.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.BpmTaskApproveReqVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmFormDO;
import cn.iocoder.yudao.module.bpm.dal.dataobject.task.BpmTaskTimeEditDO;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.enums.BpmConstants;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.BpmnModelUtils;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import cn.iocoder.yudao.module.bpm.mock.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.bpm.mock.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.bpm.service.message.dto.BpmMessageSendWhenTaskCreatedReqDTO;
import cn.iocoder.yudao.module.bpm.utils.BeanUtils;
import cn.iocoder.yudao.module.bpm.utils.json.JsonUtils;
import com.jp.med.common.util.OSSUtil;
import org.flowable.bpmn.model.BpmnModel;
import org.flowable.bpmn.model.ExtensionElement;
import org.flowable.bpmn.model.UserTask;
import org.flowable.engine.HistoryService;
import org.flowable.engine.RepositoryService;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.flowable.task.service.impl.persistence.entity.TaskEntityImpl;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.module.bpm.utils.collection.CollectionUtils.*;
import static cn.iocoder.yudao.module.bpm.utils.collection.MapUtils.findAndThen;


/**
 * Bpm 任务 Convert
 */
@Mapper
public interface BpmTaskConvert {

    BpmTaskConvert INSTANCE = Mappers.getMapper(BpmTaskConvert.class);


    default PageResult<BpmTaskRespVO> buildTodoTaskPage(PageResult<Task> pageResult,
                                                        Map<String, ProcessInstance> processInstanceMap,
                                                        Map<String, AdminUserRespDTO> userMap) {
        Set<String> proIns = processInstanceMap.keySet();
        List<Task> tasks = pageResult.getList().stream()
                .filter(task -> proIns.contains(task.getProcessInstanceId()))
                .collect(Collectors.toList());
        List<BpmTaskRespVO> taskVOList =  convertList(tasks, task -> {
           BpmTaskRespVO taskVO = BeanUtils.toBean(task,BpmTaskRespVO.class);
           taskVO.setCreateReason((String) task.getProcessVariables().get(BpmConstants.TASK_VARIABLE_CREATE_REASON));
           ProcessInstance processInstance = processInstanceMap.get(taskVO.getProcessInstanceId());
           if (processInstance != null) {
               taskVO.setProcessInstance(BeanUtils.toBean(processInstance, BpmTaskRespVO.ProcessInstance.class));
           }
           AdminUserRespDTO startUser = userMap.get((processInstance.getStartUserId()));
           taskVO.getProcessInstance().setStartUser(BeanUtils.toBean(startUser, BpmProcessInstanceRespVO.User.class));
            return taskVO;
        });
       return new PageResult<>(taskVOList, Long.valueOf(taskVOList.size()));
        /*return BeanUtils.toBean(pageResult, BpmTaskRespVO.class, taskVO -> {
            ProcessInstance processInstance = processInstanceMap.get(taskVO.getProcessInstanceId());
            if (processInstance == null) {
                return;
            }
            taskVO.setProcessInstance(BeanUtils.toBean(processInstance, BpmTaskRespVO.ProcessInstance.class));
            AdminUserRespDTO startUser = userMap.get((processInstance.getStartUserId()));
            taskVO.getProcessInstance().setStartUser(BeanUtils.toBean(startUser, BpmProcessInstanceRespVO.User.class));
        });*/
    }

    default PageResult<BpmTaskRespVO> buildTaskPage(PageResult<HistoricTaskInstance> pageResult,
                                                    Map<String, HistoricProcessInstance> processInstanceMap,
                                                    Map<String, AdminUserRespDTO> userMap,
                                                    Map<String, DeptRespDTO> deptMap) {
        List<BpmTaskRespVO> taskVOList = convertList(pageResult.getList(), task -> {
            BpmTaskRespVO taskVO = BeanUtils.toBean(task, BpmTaskRespVO.class);
            taskVO.setStatus(FlowableUtils.getTaskStatus(task))
                    .setReason(FlowableUtils.getTaskReason(task))
                    .setCreateReason((String) task.getProcessVariables().get(BpmConstants.TASK_VARIABLE_CREATE_REASON));
            // 用户信息
            AdminUserRespDTO assignUser = userMap.get((task.getAssignee()));
            if (assignUser != null) {
                taskVO.setAssigneeUser(BeanUtils.toBean(assignUser, BpmProcessInstanceRespVO.User.class));
                findAndThen(deptMap, assignUser.getDeptCode(), dept -> taskVO.getAssigneeUser().setDeptName(dept.getName()));
            }
            // 流程实例
            HistoricProcessInstance processInstance = processInstanceMap.get(taskVO.getProcessInstanceId());
            if (processInstance != null) {
                AdminUserRespDTO startUser = userMap.get((processInstance.getStartUserId()));
                taskVO.setProcessInstance(BeanUtils.toBean(processInstance, BpmTaskRespVO.ProcessInstance.class));
                taskVO.getProcessInstance().setStartUser(BeanUtils.toBean(startUser, BpmProcessInstanceRespVO.User.class));
            }
            return taskVO;
        });
        return new PageResult<>(taskVOList, pageResult.getTotal());
    }

    default List<BpmTaskRespVO> buildTaskListByProcessInstanceId(List<HistoricTaskInstance> taskList,
                                                                 HistoricProcessInstance processInstance,
                                                                 Map<Long, BpmFormDO> formMap,
                                                                 Map<String, AdminUserRespDTO> userMap,
                                                                 Map<String, DeptRespDTO> deptMap,
                                                                 Map<String, UserTask> userTaskMap,
                                                                 Map<String, BpmTaskTimeEditDO> timeEditMap) {
        List<BpmTaskRespVO> taskVOList = convertList(taskList, task -> {
            BpmTaskRespVO taskVO = BeanUtils.toBean(task, BpmTaskRespVO.class);
            taskVO.setStatus(FlowableUtils.getTaskStatus(task))
                    .setReason(FlowableUtils.getTaskReason(task));
            // 流程实例
            AdminUserRespDTO startUser = userMap.get((processInstance.getStartUserId()));
            taskVO.setProcessInstance(BeanUtils.toBean(processInstance, BpmTaskRespVO.ProcessInstance.class));
            taskVO.getProcessInstance().setStartUser(BeanUtils.toBean(startUser, BpmProcessInstanceRespVO.User.class));

            //如果有自定义审批时间，则重新设置createTime和endTime
            if (!Objects.isNull(timeEditMap.get(taskVO.getId()))) {
                BpmTaskTimeEditDO timeEditDO = timeEditMap.get(taskVO.getId());
                taskVO.setAuditTime(timeEditDO.getEndTime());
            }
            // 表单信息
            BpmFormDO form = null;
            if (task.getFormKey() != null) {
                form = MapUtil.get(formMap, Long.valueOf(task.getFormKey()), BpmFormDO.class);
                if (form != null) {
                    taskVO.setFormId(form.getId()).setFormName(form.getName()).setFormConf(form.getConf())
                            .setFormFields(form.getFields()).setFormVariables(FlowableUtils.getTaskFormVariable(task));
                }
            }

            // 用户信息
            AdminUserRespDTO assignUser = userMap.get((task.getAssignee()));
            if (assignUser != null) {
                taskVO.setAssigneeUser(BeanUtils.toBean(assignUser, BpmProcessInstanceRespVO.User.class));
                findAndThen(deptMap, assignUser.getDeptCode(), dept -> taskVO.getAssigneeUser().setDeptName(dept.getName()));
            }
            AdminUserRespDTO ownerUser = userMap.get((task.getOwner()));
            if (ownerUser != null) {
                taskVO.setOwnerUser(BeanUtils.toBean(ownerUser, BpmProcessInstanceRespVO.User.class));
                findAndThen(deptMap, ownerUser.getDeptCode(), dept -> taskVO.getOwnerUser().setDeptName(dept.getName()));
            }
            //签名 附件
            if (userTaskMap.containsKey(task.getTaskDefinitionKey())) {
                UserTask userTask = userTaskMap.get(task.getTaskDefinitionKey());
                Boolean needAttachment = BpmnModelUtils.parseNeedAttachment(userTask);
                Boolean needSign = BpmnModelUtils.parseNeedSign(userTask);
                taskVO.setNeedAttachment(needAttachment);
                taskVO.setNeedSign(needSign);
                if (needAttachment) {
                    String attJsonStr = (String) task.getTaskLocalVariables().get(BpmConstants.TASK_VARIABLE_ATTACHMENT_LIST);
                    if (StrUtil.isNotBlank(attJsonStr) && !attJsonStr.startsWith("{")) {
                        taskVO.setAttachmentList(JsonUtils.parseArray(attJsonStr, BpmTaskApproveReqVO.Attachment.class));
                        taskVO.getAttachmentList().forEach(att -> {
                            // 去除url前的/
                            String fileUrl = att.getFileUrl().startsWith("/") ? att.getFileUrl().substring(1) : att.getFileUrl();
                            String objectUrl = OSSUtil.getPresignedObjectUrl("bpm", fileUrl);
                            att.setFileUrl(objectUrl);
                            att.setValue(att.getFileUrl());
                        });
                    }
                }
                if (needSign) {
                    String url = (String) task.getTaskLocalVariables().get(BpmConstants.TASK_VARIABLE_SIGN_URL);
                    if (StrUtil.isNotBlank(url)) {
                        // 去除url前的/
                        url = url.startsWith("/") ? url.substring(1) : url;
                        String objectUrl = OSSUtil.getPresignedObjectUrl("bpm", url);
                        taskVO.setSignUrl(objectUrl);
                    }
                }
            }
            //拓展属性
            if (taskVO !=null) {
                // 获取流程实例
                /*RuntimeService runtimeService = SpringUtil.getBean(RuntimeService.class);
                ProcessInstance pi = runtimeService.createProcessInstanceQuery()
                        .processInstanceId(taskVO.getProcessInstanceId())
                        .singleResult();*/
                HistoryService historyService = SpringUtil.getBean(HistoryService.class);
                HistoricTaskInstance pi = historyService.createHistoricTaskInstanceQuery()
                        .processInstanceId(taskVO.getProcessInstanceId())
                        .taskId(taskVO.getId())
                        .singleResult();
                if (!Objects.isNull(pi)) {
                    // 获取流程定义
                    RepositoryService repositoryService = SpringUtil.getBean(RepositoryService.class);
                    ProcessDefinition processDefinition = repositoryService.createProcessDefinitionQuery()
                            .processDefinitionId(pi.getProcessDefinitionId())
                            .singleResult();

                    // 获取 BPMN 模型实例
                    BpmnModel modelInstance = repositoryService.getBpmnModel(processDefinition.getId());

                    // 获取当前任务的扩展元素
                    String taskElementId = task.getTaskDefinitionKey(); // 获取任务定义的 ID
                    Map<String, List<org.flowable.bpmn.model.ExtensionElement>> extensionElements = modelInstance.getFlowElement(taskElementId).getExtensionElements();

                    // 提取 flowable:property
                    Map<String, Object> properties = new HashMap<>();
                    if (extensionElements.containsKey("properties")) {
                        org.flowable.bpmn.model.ExtensionElement propertyElement = extensionElements.get("properties").get(0);
                        List<ExtensionElement> property = propertyElement.getChildElements().get("property");
                        if (!Objects.isNull(property)) {
                            for (ExtensionElement extensionElement : property) {
                                String name = extensionElement.getAttributes().get("name").get(0).getValue();
                                String value = extensionElement.getAttributes().get("value").get(0).getValue();
                                properties.put(name,value);
                            }
                            taskVO.setPropertyMap(properties);
                        }
                    }
                }
            }


            return taskVO;
        });

        // 拼接父子关系
        Map<String, List<BpmTaskRespVO>> childrenTaskMap = convertMultiMap(
                filterList(taskVOList, r -> StrUtil.isNotEmpty(r.getParentTaskId())),
                BpmTaskRespVO::getParentTaskId);
        for (BpmTaskRespVO taskVO : taskVOList) {
            taskVO.setChildren(childrenTaskMap.get(taskVO.getId()));
        }
        return filterList(taskVOList, r -> StrUtil.isEmpty(r.getParentTaskId()));
    }

    default List<BpmTaskRespVO> buildTaskListByParentTaskId(List<Task> taskList,
                                                            Map<String, AdminUserRespDTO> userMap,
                                                            Map<String, DeptRespDTO> deptMap) {
        return convertList(taskList, task -> BeanUtils.toBean(task, BpmTaskRespVO.class, taskVO -> {
            AdminUserRespDTO assignUser = userMap.get((task.getAssignee()));
            if (assignUser != null) {
                taskVO.setAssigneeUser(BeanUtils.toBean(assignUser, BpmProcessInstanceRespVO.User.class));
                DeptRespDTO dept = deptMap.get(assignUser.getDeptCode());
                if (dept != null) {
                    taskVO.getAssigneeUser().setDeptName(dept.getName());
                }
            }
            AdminUserRespDTO ownerUser = userMap.get((task.getOwner()));
            if (ownerUser != null) {
                taskVO.setOwnerUser(BeanUtils.toBean(ownerUser, BpmProcessInstanceRespVO.User.class));
                findAndThen(deptMap, ownerUser.getDeptCode(), dept -> taskVO.getOwnerUser().setDeptName(dept.getName()));
            }
        }));
    }

    default BpmMessageSendWhenTaskCreatedReqDTO convert(ProcessInstance processInstance, AdminUserRespDTO startUser,
                                                        Task task) {
        BpmMessageSendWhenTaskCreatedReqDTO reqDTO = new BpmMessageSendWhenTaskCreatedReqDTO();
        reqDTO.setProcessInstanceId(processInstance.getProcessInstanceId())
                .setProcessInstanceName(processInstance.getName()).setStartUserId(startUser.getEmpCode())
                .setStartUserNickname(startUser.getEmpName()).setTaskId(task.getId()).setTaskName(task.getName())
                .setAssigneeUserId((task.getAssignee()));
        return reqDTO;
    }

    /**
     * 将父任务的属性，拷贝到子任务（加签任务）
     * <p>
     * 为什么不使用 mapstruct 映射？因为 TaskEntityImpl 还有很多其他属性，这里我们只设置我们需要的。
     * 使用 mapstruct 会将里面嵌套的各个属性值都设置进去，会出现意想不到的问题。
     *
     * @param parentTask 父任务
     * @param childTask  加签任务
     */
    default void copyTo(TaskEntityImpl parentTask, TaskEntityImpl childTask) {
        childTask.setName(parentTask.getName());
        childTask.setDescription(parentTask.getDescription());
        childTask.setCategory(parentTask.getCategory());
        childTask.setParentTaskId(parentTask.getId());
        childTask.setProcessDefinitionId(parentTask.getProcessDefinitionId());
        childTask.setProcessInstanceId(parentTask.getProcessInstanceId());
//        childTask.setExecutionId(parentTask.getExecutionId()); // TODO 芋艿：新加的，不太确定；尴尬，不加时，子任务不通过会失败（报错）；加了，子任务审批通过会失败（报错）
        childTask.setTaskDefinitionKey(parentTask.getTaskDefinitionKey());
        childTask.setTaskDefinitionId(parentTask.getTaskDefinitionId());
        childTask.setPriority(parentTask.getPriority());
        childTask.setCreateTime(new Date());
        childTask.setTenantId(parentTask.getTenantId());
    }

}

package cn.iocoder.yudao.module.bpm.convert.task;

import cn.iocoder.yudao.module.bpm.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.controller.admin.definition.vo.process.BpmProcessDefinitionRespVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmCategoryDO;
import cn.iocoder.yudao.module.bpm.dal.dataobject.definition.BpmProcessDefinitionInfoDO;
import cn.iocoder.yudao.module.bpm.dal.dataobject.task.BpmTaskTimeEditDO;
import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEvent;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import cn.iocoder.yudao.module.bpm.mock.dept.dto.DeptRespDTO;
import cn.iocoder.yudao.module.bpm.mock.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.bpm.service.message.dto.BpmMessageSendWhenProcessInstanceApproveReqDTO;
import cn.iocoder.yudao.module.bpm.service.message.dto.BpmMessageSendWhenProcessInstanceRejectReqDTO;
import cn.iocoder.yudao.module.bpm.utils.BeanUtils;
import cn.iocoder.yudao.module.bpm.utils.collection.MapUtils;
import cn.iocoder.yudao.module.bpm.utils.number.NumberUtils;
import com.jp.med.common.entity.user.SysUser;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.module.bpm.framework.flowable.core.util.CurrentUserInfo.getCurrentUserInfo;

/**
 * 流程实例 Convert
 *

 */
@Mapper
public interface BpmProcessInstanceConvert {

    BpmProcessInstanceConvert INSTANCE = Mappers.getMapper(BpmProcessInstanceConvert.class);

    default PageResult<BpmProcessInstanceRespVO> buildProcessInstancePage(PageResult<HistoricProcessInstance> pageResult,
                                                                          Map<String, ProcessDefinition> processDefinitionMap,
                                                                          Map<String, BpmCategoryDO> categoryMap,
                                                                          Map<String, List<Task>> taskMap,
                                                                          Map<String, AdminUserRespDTO> userMap,
                                                                          Map<String, DeptRespDTO> deptMap) {
        PageResult<BpmProcessInstanceRespVO> vpPageResult = BeanUtils.toBean(pageResult, BpmProcessInstanceRespVO.class);
        for (int i = 0; i < pageResult.getList().size(); i++) {
            BpmProcessInstanceRespVO respVO = vpPageResult.getList().get(i);
            respVO.setStatus(FlowableUtils.getProcessInstanceStatus(pageResult.getList().get(i)));
            MapUtils.findAndThen(processDefinitionMap, respVO.getProcessDefinitionId(),
                    processDefinition -> respVO.setCategory(processDefinition.getCategory())
                            .setProcessDefinition(BeanUtils.toBean(processDefinition, BpmProcessDefinitionRespVO.class)));
            MapUtils.findAndThen(categoryMap, respVO.getCategory(), category -> respVO.setCategoryName(category.getName()));
            respVO.setTasks(BeanUtils.toBean(taskMap.get(respVO.getId()), BpmProcessInstanceRespVO.Task.class));
            // user
            if (userMap != null) {
                String startUserId = pageResult.getList().get(i).getStartUserId();
                AdminUserRespDTO startUser = userMap.get(NumberUtils.parseLong(startUserId));
                if (startUser != null) {
                    respVO.setStartUser(BeanUtils.toBean(startUser, BpmProcessInstanceRespVO.User.class));
                    MapUtils.findAndThen(deptMap, startUser.getDeptCode(), dept -> respVO.getStartUser().setDeptName(dept.getName()));
                }
            }
        }
        return vpPageResult;
    }

    default BpmProcessInstanceRespVO buildProcessInstance(HistoricProcessInstance processInstance,
                                                          ProcessDefinition processDefinition,
                                                          BpmProcessDefinitionInfoDO processDefinitionExt,
                                                          String bpmnXml,
                                                          AdminUserRespDTO startUser,
                                                          DeptRespDTO dept,
                                                          List<BpmTaskTimeEditDO> bpmTaskTimeEditDOS) {
        BpmProcessInstanceRespVO respVO = BeanUtils.toBean(processInstance, BpmProcessInstanceRespVO.class);
        respVO.setStatus(FlowableUtils.getProcessInstanceStatus(processInstance));
        respVO.setFormVariables(FlowableUtils.getProcessInstanceFormVariable(processInstance));
        // definition
        respVO.setProcessDefinition(BeanUtils.toBean(processDefinition, BpmProcessDefinitionRespVO.class));
        copyTo(processDefinitionExt, respVO.getProcessDefinition());
        respVO.getProcessDefinition().setBpmnXml(bpmnXml);
        // user
        if (startUser != null) {
            respVO.setStartUser(BeanUtils.toBean(startUser, BpmProcessInstanceRespVO.User.class));
            if (dept != null) {
                respVO.getStartUser().setDeptName(dept.getName());
            }
        }
        respVO.setFormVariables(FlowableUtils.getProcessInstanceFormVariable(processInstance));
        //重新设置发起时间,列表排升序
        if (!bpmTaskTimeEditDOS.isEmpty()) {
            //如果审核时间小于创建时间 (手动修改导致)
            if (bpmTaskTimeEditDOS.get(0).getStartTime().isBefore(respVO.getStartTime())) {
                respVO.setStartTime(bpmTaskTimeEditDOS.get(0).getStartTime());
            }

            //设置结束时间
            if (null != respVO.getEndTime()) {
                respVO.setEndTime(bpmTaskTimeEditDOS.get(bpmTaskTimeEditDOS.size() - 1).getEndTime());
            }
        }
        return respVO;
    }

    @Mapping(source = "from.id", target = "to.id", ignore = true)
    void copyTo(BpmProcessDefinitionInfoDO from, @MappingTarget BpmProcessDefinitionRespVO to);

    default BpmProcessInstanceStatusEvent buildProcessInstanceStatusEvent(Object source, HistoricProcessInstance instance, Integer status) {

        Map<String, Object> processVariables = instance.getProcessVariables();
        Map<String, String> stringVariables = processVariables.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() != null ? entry.getValue().toString() : ""
                ));
        return new BpmProcessInstanceStatusEvent(source).
                setId(instance.getId())
                .setVariables(stringVariables)
                .setStatus(status)
                .setProcessInstanceName(instance.getProcessDefinitionName())
                .setProcessDefinitionKey(instance.getProcessDefinitionKey())
                .setBusinessKey(instance.getBusinessKey())
                .setProcessInstanceId(instance.getId())
                .setProcessInstanceName(instance.getName());

    }

    default BpmProcessInstanceStatusEvent buildProcessInstanceStatusEvent(Object source, ProcessInstance instance, Integer status) {
        Map<String, Object> processVariables = instance.getProcessVariables();

        Map<String, String> stringVariables = processVariables.entrySet()
                .stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue() != null ? entry.getValue().toString() : ""
                ));
        return new BpmProcessInstanceStatusEvent(source)
                .setId(instance.getId())
                .setProcessInstanceName(instance.getProcessDefinitionName())
                .setVariables(stringVariables)
                .setStatus(status)
                .setProcessDefinitionKey(instance.getProcessDefinitionKey())
                .setBusinessKey(instance.getBusinessKey())
                .setProcessInstanceId(instance.getId())
                .setProcessInstanceName(instance.getName());
    }

    default BpmMessageSendWhenProcessInstanceApproveReqDTO buildProcessInstanceApproveMessage(ProcessInstance instance) {
        return new BpmMessageSendWhenProcessInstanceApproveReqDTO()
                .setStartUserId((instance.getStartUserId()))
                .setProcessInstanceId(instance.getId())
                .setProcessInstanceName(instance.getName());
    }

    default BpmMessageSendWhenProcessInstanceRejectReqDTO buildProcessInstanceRejectMessage(ProcessInstance instance, String reason) {

        // 获取当前节点 ID 和名称
//        String rejectNodeId = instance.getCurrentActivityId(); // 假设这是当前活动的 ID
//        String rejectNodeName = instance.getCurrentActivityName(); // 假设这是当前活动的名称

        SysUser currentUserInfo = getCurrentUserInfo();
        // 获取操作用户 ID 和名称
        String operatorUserId = currentUserInfo.getUsername(); // 假设有一个方法可以获取当前操作用户的 ID
        String operatorUserName = currentUserInfo.getNickname(); // 假设有一个方法可以获取当前操作用户的名称

        return new BpmMessageSendWhenProcessInstanceRejectReqDTO()
                .setProcessInstanceName(instance.getName())
                .setProcessInstanceId(instance.getId())
//                .setRejectNodeId(rejectNodeId)
//                .setRejectNodeName(rejectNodeName)
                .setOperatorUserId(operatorUserId)
                .setOperatorUserName(operatorUserName)
                .setReason(reason)
                .setStartUserId((instance.getStartUserId()));
    }


}

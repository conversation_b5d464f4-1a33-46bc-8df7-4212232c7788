package cn.iocoder.yudao.module.bpm.framework.flowable.core.util;

import com.jp.med.common.entity.user.SysUser;

public class CurrentUserInfo {

    private final static ThreadLocal<SysUser> currentUserInfo = new ThreadLocal<SysUser>();
    private final static ThreadLocal<String> currentSysName = new ThreadLocal<String>();

    /**
     * empCode
     *
     * @return
     */
    public static String getLoginUserId() {

        SysUser sysUser = currentUserInfo.get();

        if (sysUser != null) {
            String username = sysUser.getUsername();
            FlowableUtils.setAuthenticatedUserId(username);
            return username;
        } else {
            return null;
        }
    }

    public static SysUser getCurrentUserInfo() {
        return currentUserInfo.get();
    }

    public static String getCurrentSysName() {
        return currentSysName.get();
    }

    public static void setCurrentSysName(String sysName) {
        currentSysName.set(sysName);
    }

    public static void setCurrentUserInfo(SysUser sysUser) {
        currentUserInfo.set(sysUser);
    }
}

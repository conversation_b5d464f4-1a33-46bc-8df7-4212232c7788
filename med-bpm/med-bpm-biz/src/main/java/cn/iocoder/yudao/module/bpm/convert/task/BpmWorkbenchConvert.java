package cn.iocoder.yudao.module.bpm.convert.task;

import cn.iocoder.yudao.module.bpm.common.pojo.PageResult;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.cc.BpmProcessInstanceCopyRespVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.task.BpmTaskRespVO;
import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.workbench.*;
import cn.iocoder.yudao.module.bpm.dal.dataobject.task.BpmProcessInstanceCopyDO;
import org.flowable.engine.history.HistoricProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 工作台 Convert
 */
@Mapper
public interface BpmWorkbenchConvert {

    BpmWorkbenchConvert INSTANCE = Mappers.getMapper(BpmWorkbenchConvert.class);

    /**
     * 将流程实例 {@link BpmProcessInstanceRespVO} 转换为工作台我的流程VO
     */
    List<BpmWorkbenchMyProcessRespVO> convertMyProcessList(List<BpmProcessInstanceRespVO> list);

    /**
     * 将流程实例分页 {@link PageResult<BpmProcessInstanceRespVO>} 转换为工作台我的流程VO分页
     */
    PageResult<BpmWorkbenchMyProcessRespVO> convertMyProcessPage(PageResult<BpmProcessInstanceRespVO> page);

    /**
     * 将任务 {@link BpmTaskRespVO} 转换为工作台待办任务VO
     */
    List<BpmWorkbenchTodoTaskRespVO> convertTodoTaskList(List<BpmTaskRespVO> list);

    /**
     * 将任务分页 {@link PageResult<BpmTaskRespVO>} 转换为工作台待办任务VO分页
     */
    PageResult<BpmWorkbenchTodoTaskRespVO> convertTodoTaskPage(PageResult<BpmTaskRespVO> page);

    /**
     * 将任务 {@link BpmTaskRespVO} 转换为工作台已办任务VO
     */
    List<BpmWorkbenchDoneTaskRespVO> convertDoneTaskList(List<BpmTaskRespVO> list);

    /**
     * 将任务分页 {@link PageResult<BpmTaskRespVO>} 转换为工作台已办任务VO分页
     */
    PageResult<BpmWorkbenchDoneTaskRespVO> convertDoneTaskPage(PageResult<BpmTaskRespVO> page);

    /**
     * 将抄送 {@link BpmProcessInstanceCopyRespVO} 转换为工作台抄送我的VO
     */
    List<BpmWorkbenchCopyRespVO> convertCopyList(List<BpmProcessInstanceCopyRespVO> list);

    /**
     * 将抄送分页 {@link PageResult<BpmProcessInstanceCopyRespVO>} 转换为工作台抄送我的VO分页
     */
    PageResult<BpmWorkbenchCopyRespVO> convertCopyPage(PageResult<BpmProcessInstanceCopyRespVO> page);
}
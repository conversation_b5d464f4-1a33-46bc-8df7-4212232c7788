package com.jp.med.gateway.filter;

import com.alibaba.fastjson.JSONObject;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.entity.user.SysUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.RedisUtil;
import com.jp.med.gateway.config.security.SecurityUserDetails;
import com.jp.med.gateway.util.JwtTokenUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.annotation.Order;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/17 14:37
 * @description: 全局过滤
 */
@Order(0)
@Component
public class CustomGlobalFilter implements GlobalFilter {

    @Autowired
    private JwtTokenUtil jwtTokenUtil;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        String token = request.getHeaders().getFirst(MedConst.TOKEN_HEADER);
        if (!StringUtils.isEmpty(token)) {
            String realToken = token.replace(MedConst.BEARER, "");
            SecurityUserDetails userDetails = JSONObject.parseObject(JSONObject.toJSONString(RedisUtil.get(realToken)), SecurityUserDetails.class);
            ServerHttpRequest.Builder builder = request.mutate();
            //将jwt token中的用户信息传给服务
            builder.header(MedConst.HEADER_USER_INFO_KEY, URLEncoder.encode(JSONObject.toJSONString(userDetails.getSysUser()), StandardCharsets.UTF_8));
            builder.header(MedConst.HEADER_TOKEN_EXPIRE_TIME, new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(jwtTokenUtil.getExpiredDateFromToken(realToken)));
            return chain.filter(exchange.mutate().request(builder.build()).build());
        }
        return chain.filter(exchange);

    }
}

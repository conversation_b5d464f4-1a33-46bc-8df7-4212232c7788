package com.jp.med.gateway.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.common.CommonFeignDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/password")
public class EncryController {

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 加密
     */
    @RequestMapping(value = "/encry")
    CommonFeignResult encryPwd(@RequestParam("password") String password) {
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, passwordEncoder.encode(password));
    }

    /**
     * 验证密码
     */
    @RequestMapping(value = "/match")
    CommonFeignResult match(@RequestBody CommonFeignDto dto) {
        String rawPwd = (String) dto.get(MedConst.PASSWORD_R_K);
        String encryPwd = (String) dto.get(MedConst.PASSWORD_E_K);
        return CommonFeignResult.build().put(CommonFeignResult.DATA_KEY, passwordEncoder.matches(rawPwd, encryPwd));
    }
}

package com.jp.med.gateway.feign.user;

import com.jp.med.common.dto.common.CommonFeignDto;
import com.jp.med.common.entity.common.CommonFeignResult;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/2/18 14:59
 * @description: 用户远程调用接口
 */
@RefreshScope
@FeignClient(name = "med-core", url = "${custom.gateway.med-core-service-uri}")
public interface UserFeignService {

    /**
     * 通过用户名查询用户信息
     * @param username
     * @return
     */
    @PostMapping("/user/queryUserByUsername")
    CommonFeignResult queryUserByUsername(@RequestParam("username") String username);

    /**
     * 更改用户密码错误次数
     * @param username
     * @return
     */
    @PostMapping("/user/updatePasswordErrorNum")
    CommonFeignResult updatePasswordErrorNum(@RequestParam("username") String username);

    /**
     * 通过用户名查询用户所拥有的的菜单权限
     * @param dto
     * @return
     */
    @PostMapping("/user/queryUserAuthByUsername")
    CommonFeignResult queryUserAuthByUsername(@RequestBody CommonFeignDto dto);

    /**
     * 保存用户token
     * @param dto
     * @return
     */
    @PostMapping("/user/saveUserToken")
    CommonFeignResult saveUserToken(@RequestBody CommonFeignDto dto);

    /**
     * 删除用户token
     * @param dto
     * @return
     */
    @PostMapping("/user/deleteUserToken")
    public CommonFeignResult deleteUserToken(@RequestBody CommonFeignDto dto);

}

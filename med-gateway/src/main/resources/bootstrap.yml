spring:
  application:
    name: med-gateway
  cloud:
    nacos:
      config:
        server-addr: tserver:8848
        namespace: dev2
        shared-configs:
          - data-id: shared-others.yml
            group: shared
            refresh: true

          - data-id: shared-redis.yml
            group: shared
            refresh: true

          - data-id: shared-feign.yml
            group: shared
            refresh: true

          - data-id: shared-nacos.yml
            group: shared
            refresh: true

          - data-id: shared-sleuth.yml
            group: shared
            refresh: true

          - data-id: shared-logback.yml
            group: shared
            refresh: true

          - data-id: shared-seata.yml
            group: shared
            refresh: true

  profiles:
    active: dev

server:
  port: 9528

logging:
  file:
    path: logs/gateway

package com.example.sfm_back.controller;

import com.example.sfm_back.dto.AdjustStockDTO;
import com.example.sfm_back.service.MmisMaterialSumWriteService;
import com.example.sfm_back.vo.ResultVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.Map;

@RestController
@RequestMapping("/api/mmis/material/sum")
public class MmisMaterialSumController {

    private static final Logger logger = LoggerFactory.getLogger(MmisMaterialSumController.class);

    @Autowired
    private MmisMaterialSumWriteService mmisMaterialSumWriteService;

    @PostMapping("/adjust")
    public ResultVO<Void> adjustStock(@RequestBody Map<String, Object> requestMap) {
        logger.info("接收到前端调整请求，完整参数: {}", requestMap);
        
        try {
            AdjustStockDTO dto = new AdjustStockDTO();
            dto.setMatUniqueCode((String) requestMap.get("matUniqueCode"));
            
            // 处理金额 - 确保以字符串形式解析，保留完整精度
            Object amountObj = requestMap.get("adjustAmount");
            if (amountObj != null) {
                String amountStr = amountObj.toString();
                logger.info("原始金额字符串: {}", amountStr);
                
                // 检查值是否为0或非常小
                try {
                    BigDecimal amount = new BigDecimal(amountStr);
                    if (amount.abs().compareTo(new BigDecimal("0.000001")) <= 0) {
                        logger.warn("调整金额过小: {} <= 0.000001，可能不会触发矫正", amount);
                    }
                } catch (Exception e) {
                    logger.error("金额格式检查失败: {}", e.getMessage());
                }
                
                dto.setAdjustAmountStr(amountStr);
                
                // 打印转换后的值，确保精度正确
                logger.info("转换后的金额值: {}", dto.getAdjustAmount());
            } else {
                logger.warn("请求中不包含adjustAmount参数");
            }
            
            // 处理数量
            Object quantityObj = requestMap.get("adjustQuantity");
            if (quantityObj != null) {
                try {
                    if (quantityObj instanceof Number) {
                        dto.setAdjustQuantity(new BigDecimal(quantityObj.toString()));
                    } else {
                        dto.setAdjustQuantity(new BigDecimal(quantityObj.toString()));
                    }
                    logger.info("解析后的调整数量: {}", dto.getAdjustQuantity());
                } catch (Exception e) {
                    logger.error("解析调整数量失败: {}", e.getMessage());
                    dto.setAdjustQuantity(BigDecimal.ZERO);
                }
            }
            
            // 处理备注
            dto.setAdjustRemark((String) requestMap.get("adjustRemark"));
            
            logger.info("处理后的调整请求: matUniqueCode={}, adjustAmount={}, adjustQuantity={}, adjustRemark={}",
                    dto.getMatUniqueCode(), dto.getAdjustAmount(), dto.getAdjustQuantity(), dto.getAdjustRemark());
            
            mmisMaterialSumWriteService.adjustStock(dto);
            
            // 验证是否成功
            try {
                Thread.sleep(800); // 等待事务完成
                
                // 这里添加验证代码
                logger.info("接口调用完成，返回成功");
                
                return ResultVO.success();
            } catch (Exception e) {
                logger.error("验证调整结果失败", e);
                return ResultVO.success(); // 仍然返回成功，避免前端报错
            }
        } catch (Exception e) {
            logger.error("处理调整请求失败: {}", e.getMessage(), e);
            return ResultVO.error("处理调整请求失败: " + e.getMessage());
        }
    }
} 
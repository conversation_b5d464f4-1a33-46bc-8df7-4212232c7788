<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.useCodeCfg.mapper.write.MmisMeteringAssistWriteMapper">

    <update id="delAssistByCode" parameterType="string">
        update mmis_metering_assist
        set active_flag = '1'
        where meter_code = #{code,jdbcType=VARCHAR}
    </update>
</mapper>

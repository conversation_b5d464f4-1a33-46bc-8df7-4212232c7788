<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.useCodeCfg.mapper.read.MmisMeteringModeCfgReadMapper">

    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMeteringModeCfgVo" id="meteringModeCfgMap">
        <result property="id" column="id"/>
        <result property="meterCode" column="meter_code"/>
        <result property="meterName" column="meter_name"/>
        <result property="baseUnitName" column="base_unit_name"/>
        <result property="baseUnitCoefficient" column="base_unit_coefficient"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="cter" column="cter"/>
        <result property="createTime" column="create_time"/>
        <result property="updtr" column="updtr"/>
        <result property="updateTime" column="update_time"/>
        <result property="delter" column="delter"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMeteringModeCfgVo">
        SELECT a.id                                                    as id,
               a.meter_code                                            as meterCode,
               a.meter_name                                            as meterName,
               a.base_unit_name                                        as baseUnitName,
               a.base_unit_coefficient                                 as baseUnitCoefficient,
               a.active_flag                                           as activeFlag,
               a.cter                                                  as cter,
               a.create_time                                           as createTime,
               a.updtr                                                 as updtr,
               a.update_time                                           as updateTime,
               a.delter                                                as delter,
               a.delete_time                                           as deleteTime,
               a.hospital_id                                           as hospitalId,
               (SELECT b.meter_name
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 1
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitOneName,
               (SELECT b.meter_ratio
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 1
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitOneCoefficient,
               (SELECT b.meter_name
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 2
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitTwoName,
               (SELECT b.meter_ratio
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 2
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitTwoCoefficient,

               (SELECT b.meter_name
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 3
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitThrName,
               (SELECT b.meter_ratio
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 3
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitThrCoefficient,
               (SELECT b.meter_name
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 4
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitFouName,
               (SELECT b.meter_ratio
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 4
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitFouCoefficient,

               (SELECT b.meter_name
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 5
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitFivName,
               (SELECT b.meter_ratio
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 5
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitFivCoefficient,
               (SELECT b.meter_name
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 6
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitSixName,
               (SELECT b.meter_ratio
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 6
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitSixCoefficient,

               (SELECT b.meter_name
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 7
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitSevName,
               (SELECT b.meter_ratio
                FROM mmis_metering_assist b
                WHERE b.meter_code = a.meter_code
                  AND b.order_num = 7
                  AND (b.active_flag IS NULL OR b.active_flag != '1')) as unitSevCoefficient
        FROM mmis_metering_mode_cfg a
        where (a.active_flag IS NULL OR a.active_flag != '1')
    </select>

    <select id="queryMaxCode" resultType="string">
        select meter_code as meterCode
        from mmis_metering_mode_cfg
        where (active_flag IS NULL OR active_flag != '1')
        order by meter_code desc limit 1
    </select>
    <select id="queryMeterModeCfg" resultType="com.jp.med.mmis.modules.useCodeCfg.vo.MmisMeteringModeCfgVo">
        SELECT
        meter_name as meterName,
        meter_code as meterCode,
        hospital_id as hospitalId
        FROM
        mmis_metering_mode_cfg
        <where>
            (active_flag IS NULL OR active_flag != '1')
            <if test="meterName != null and meterName != ''">
                and meter_name =#{meterName,jdbcType=VARCHAR}
            </if>
        </where>
    </select>
    <!--  查找code  -->
    <select id="queryCodeById" parameterType="integer" resultType="string">
        select meter_code as meterCode
        from mmis_metering_mode_cfg
        <where>
            (active_flag IS NULL OR active_flag != '1')
            <if test="id != null and id != ''">
                AND id =#{id,jdbcType=INTEGER}
            </if>
        </where>

    </select>

</mapper>

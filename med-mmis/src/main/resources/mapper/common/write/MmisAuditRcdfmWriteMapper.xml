<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.mmis.modules.common.mapper.write.MmisAuditRcdfmWriteMapper">
    <!-- 添加审核结果 -->
    <insert id="saveAuditRes">
        INSERT INTO mmis_audit_res(bchno, audit_res, crter, create_time, dto)
        VALUES (#{bchno,jdbcType=VARCHAR},
                #{auditRes,jdbcType=VARCHAR},
                #{crter,jdbcType=VARCHAR},
                #{createTime,jdbcType=VARCHAR},
                #{dto,jdbcType=VARCHAR})
    </insert>
    <!-- 更新消息id -->
    <update id="updateMessageId">
        UPDATE mmis_audit_rcdfm
        SET message_id = #{messageId,jdbcType=INTEGER}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>
    <!-- 更新审核结果 -->
    <update id="updateAuditRes">
        UPDATE mmis_audit_res
        SET audit_res = #{auditRes,jdbcType=VARCHAR}
        WHERE bchno = #{bchno,jdbcType=VARCHAR}
    </update>

    <update id="updateAuditDetail">
        UPDATE mmis_audit_rcdfm
        SET chk_time      = #{chkTime,jdbcType=VARCHAR},
            chk_state     = #{chkState,jdbcType=VARCHAR},
            chk_remarks   = #{chkRemarks,jdbcType=VARCHAR},
            chk_sign_path = #{chkSignPath,jdbcType=VARCHAR},
            chk_att_path  = #{chkAttPath,jdbcType=VARCHAR},
            act_chker     = #{actChker,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER}
    </update>

    <!--新增审核详情-->
    <insert id="saveAuditDetail" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO mmis_audit_rcdfm(bchno,
                                     chker,
                                     chk_dept,
                                     chk_time,
                                     chk_remarks,
                                     chk_sign,
                                     chk_att,
                                     chk_seq,
                                     chk_state,
                                     dscr,
                                     message_sup,
                                     message_payload)
        VALUES (#{bchno,jdbcType=VARCHAR},
                #{chker,jdbcType=VARCHAR},
                #{chkDept,jdbcType=VARCHAR},
                #{chkTime,jdbcType=VARCHAR},
                #{chkRemarks,jdbcType=VARCHAR},
                #{chkSign,jdbcType=VARCHAR},
                #{chkAtt,jdbcType=VARCHAR},
                #{chkSeq,jdbcType=INTEGER},
                #{chkState,jdbcType=VARCHAR},
                #{dscr,jdbcType=VARCHAR},
                #{messageSup,jdbcType=VARCHAR},
                #{messagePayload,jdbcType=VARCHAR})
    </insert>

</mapper>

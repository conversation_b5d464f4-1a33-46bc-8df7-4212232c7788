# M1004 物资出库撤销接口文档

## 1. 接口概述

**接口 ID**：M1004

**接口名称**：物资出库撤销接口

**接口说明**：用于撤销已完成的物资出库记录，将恢复库存并变更出库单据状态。

**业务流程**：

1. 外部系统通过此接口发送撤销请求
2. 系统验证工单 ID 是否存在
3. 系统恢复已出库物资的库存
4. 系统更新出库记录状态为已撤销
5. 系统返回撤销处理结果

## 2. 接口规范

### 2.1 请求信息

**接口地址**：https://mmis/api/consumable/outbound/revoke

> 注意：实际请求需通过统一网关入口访问：`https://hrp.zjxrmyy.cn:18090/back/mmis/externalApi/gateway`

**请求方式**：POST

**请求头**：

```
Content-Type: application/json
```

### 2.2 请求参数

| 参数名     | 类型   | 必填 | 说明                                 | 示例值                                             |
| ---------- | ------ | ---- | ------------------------------------ | -------------------------------------------------- |
| hospitalId | String | 是   | 医院 ID                              | "zjxrmyy"                                          |
| encryptKey | String | 是   | 加密认证密钥，需与系统约定的密钥一致 | "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E" |
| infno      | String | 是   | 接口编号，固定值："M1004"            | "M1004"                                            |
| input      | Object | 是   | 包含撤销数据，详见下表               | {...}                                              |

**input 对象参数说明**：

| 参数名       | 类型   | 必填 | 说明                              | 示例值          |
| ------------ | ------ | ---- | --------------------------------- | --------------- |
| workId       | String | 是   | 原出库工作单号，与 M1003 接口对应 | "WO20250421001" |
| revokeReason | String | 否   | 撤销原因                          | "数据录入错误"  |
| operator     | String | 否   | 操作人                            | "李四"          |

### 2.3 请求示例

```json
{
  "hospitalId": "zjxrmyy",
  "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
  "infno": "M1004",
  "input": {
    "workId": "WO20250421001",
    "revokeReason": "数据录入错误",
    "operator": "李四"
  }
}
```

### 2.4 响应参数

| 参数名  | 类型   | 说明                      | 示例值 |
| ------- | ------ | ------------------------- | ------ |
| code    | Number | 状态码：0-成功，非 0-失败 | 0      |
| message | String | 状态描述                  | "成功" |
| data    | Object | 返回数据对象              | {...}  |

**data 对象参数说明**：

| 参数名  | 类型   | 说明                            | 示例值             |
| ------- | ------ | ------------------------------- | ------------------ |
| status  | String | 状态：SUCCESS-成功，FAILED-失败 | "SUCCESS"          |
| message | String | 处理结果说明                    | "出库记录撤销成功" |

### 2.5 响应示例

**成功示例**：

```json
{
  "code": 0,
  "message": "成功",
  "data": {
    "status": "SUCCESS",
    "message": "出库记录撤销成功"
  }
}
```

**失败示例**：

```json
{
  "code": 404,
  "message": "未找到对应的出库记录：WO20250421001",
  "data": {
    "status": "FAILED",
    "message": "找不到工单的活动备份记录"
  }
}
```

## 3. 错误码说明

| 错误码 | 说明                     | 示例错误信息                          |
| ------ | ------------------------ | ------------------------------------- |
| 400    | 请求参数错误             | "参数错误：缺少必填参数 workId"       |
| 401    | 医院 ID 或加密密钥不正确 | "认证失败：医院 ID 或密钥不正确"      |
| 404    | 出库记录不存在           | "未找到对应的出库记录：WO20250421001" |
| 409    | 出库记录状态不允许撤销   | "当前出库记录状态不允许撤销"          |
| 500    | 服务器内部错误           | "系统内部错误，请联系管理员"          |
| 503    | 服务暂时不可用           | "服务暂时不可用，请稍后重试"          |

## 4. 业务处理逻辑

1. 系统会根据提供的 workId 查找对应的出库记录和备份信息，并检查该记录的状态是否允许撤销
2. 系统会自动恢复出库物资的库存，将出库数量重新加回 mmis_material_sum 表中
3. 撤销操作会将出库记录状态更新为已撤销(4)
4. 系统会记录撤销原因、操作人和撤销时间，便于后续审计和追踪
5. 一旦出库记录被撤销，原出库单据将不再有效，但系统仍会保留记录以便查询历史

## 5. 实现细节

### 5.1 数据表关系

撤销操作主要涉及以下数据表：

- **mmis_outbound_backup**: 出库数据备份表，存储完整的出库物资信息和状态
- **mmis_outbound_apply**: 出库申请主表，通过更新状态标记撤销
- **mmis_material_sum**: 物资库存表，恢复物资库存量

### 5.2 状态流转

出库记录撤销后的状态变化：

- mmis_outbound_backup 表的 status 字段由"ACTIVE"变更为"REVOKED"
- mmis_outbound_apply 表的 out_status 字段更新为"4"(已撤销)

### 5.3 撤销限制

以下情况不允许进行撤销操作：

1. 找不到对应的工单备份记录
2. 工单已经被撤销(status="REVOKED")
3. 系统内部异常情况(如数据不一致)

## 6. 注意事项

1. **幂等性保证**：同一 workId 的多次撤销请求只会成功一次，后续请求会返回已撤销的提示
2. **库存恢复**：撤销操作会恢复物资库存，请确保与实际物资流转一致
3. **日志记录**：系统会记录所有撤销操作，包括操作人、撤销原因和时间
4. **撤销时效**：建议在出库操作后的合理时间内进行撤销，以避免业务冲突
5. **异常处理**：撤销过程中可能出现的异常会被记录，并向调用方返回明确的错误信息

## 7. 调试与测试

### 7.1 接口调试

可使用以下 Curl 命令进行接口调试：

```bash
curl -X POST \
  https://hrp.zjxrmyy.cn:18090/back/mmis/externalApi/gateway \
  -H 'Content-Type: application/json' \
  -d '{
    "hospitalId": "zjxrmyy",
    "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
    "infno": "M1004",
    "input": {
      "workId": "WO20250421001",
      "revokeReason": "测试撤销",
      "operator": "测试人员"
    }
  }'
```

### 7.2 测试用例

| 测试场景         | 输入                              | 预期输出                         |
| ---------------- | --------------------------------- | -------------------------------- |
| 正常撤销         | 有效的 workId                     | 撤销成功，库存恢复               |
| 撤销不存在的工单 | 无效的 workId                     | 错误提示：找不到对应出库记录     |
| 重复撤销同一工单 | 已撤销的 workId                   | 错误提示：当前记录状态不允许撤销 |
| 参数验证         | 缺少 workId                       | 错误提示：缺少必填参数 workId    |
| 认证失败         | 不正确的 hospitalId 或 encryptKey | 错误提示：认证失败               |

```

```

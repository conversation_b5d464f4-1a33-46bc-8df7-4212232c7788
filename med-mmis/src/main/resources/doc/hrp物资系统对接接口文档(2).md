# HRP 物资系统对接接口文档

## 1. 系统概述

本文档详细描述 HRP 物资系统与医院物资管理系统的接口对接方案，包括接口定义、请求参数、响应数据格式等。

## 2. 系统交互流程

```
+---------------+                    +----------------------+                     +-----------------+
|               |                    |                      |                     |                 |
|  HRP物资系统   |<------------------>|  API网关/适配服务     |<------------------->|  物资管理系统    |
|               |                    |                      |                     |                 |
+---------------+                    +----------------------+                     +-----------------+
```

HRP 物资系统与物资管理系统的交互流程如下：

1. **物资分类同步**：HRP 系统通过接口获取物资分类数据
2. **物资项目同步**：HRP 系统通过接口获取物资项目数据
3. **物资出库处理**：HRP 系统推送出库数据至物资管理系统

## 3. 接口汇总

| 接口编号 | 接口名称     | 接口说明         | 请求方式 |
| -------- | ------------ | ---------------- | -------- |
| M1001    | 物资分类接口 | 获取物资分类数据 | POST     |
| M1002    | 物资项目接口 | 获取物资项目数据 | POST     |
| M1003    | 物资出库接口 | 接收物资出库数据 | POST     |

## 4. 接口说明

### 4.1 统一网关入口

所有接口均通过统一网关入口访问，使用固定的 URL 路径：

```
POST https://hrp.zjxrmyy.cn:18090/back/mmis/externalApi/gateway
```

#### 请求头

```
Content-Type: application/json
```

#### 通用请求参数

所有接口请求必须包含以下固定参数：

| 参数名     | 类型   | 是否必须 | 描述                                                               |
| ---------- | ------ | -------- | ------------------------------------------------------------------ |
| hospitalId | String | 是       | 医院标识，固定值：zjxrmyy                                          |
| encryptKey | String | 是       | 加密密钥，固定值：57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E |
| infno      | String | 是       | 接口编号，如：M1001                                                |
| input      | Object | 否       | 请求数据，根据接口不同而变化                                       |

#### 通用响应格式

```json
{
  "code": 200,
  "message": "成功",
  "data": {}
}
```

| 参数名  | 类型    | 描述                                   |
| ------- | ------- | -------------------------------------- |
| code    | Integer | 响应状态码，200 表示成功，其他表示失败 |
| message | String  | 响应消息                               |
| data    | Object  | 响应数据，根据接口不同而变化           |

#### 响应状态码说明

| 状态码 | 说明           |
| ------ | -------------- |
| 200    | 请求成功       |
| 400    | 请求参数错误   |
| 401    | 认证失败       |
| 404    | 请求资源不存在 |
| 500    | 服务器内部错误 |

### 4.2 物资分类接口 (M1001)

#### 接口说明

获取物资分类数据，支持全量数据获取。

#### 请求参数

| 参数名     | 类型   | 是否必须 | 描述                                                               |
| ---------- | ------ | -------- | ------------------------------------------------------------------ |
| hospitalId | String | 是       | 医院标识，固定值：zjxrmyy                                          |
| encryptKey | String | 是       | 加密密钥，固定值：57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E |
| infno      | String | 是       | 接口编号，固定值：M1001                                            |
| input      | Object | 否       | 可为空，此接口不需要额外输入参数                                   |

#### 请求示例

```json
{
  "hospitalId": "zjxrmyy",
  "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
  "infno": "M1001",
  "input": {}
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "成功",
  "data": [
    {
      "tripartiteConsumableTypeId": "1001",
      "code": "01",
      "name": "办公用品",
      "parentId": "0",
      "isLeaf": false,
      "deleteFlag": false
    },
    {
      "tripartiteConsumableTypeId": "1002",
      "code": "01.01",
      "name": "纸制品",
      "parentId": "1001",
      "isLeaf": true,
      "deleteFlag": false
    }
  ]
}
```

#### 响应字段说明

| 字段名                     | 类型    | 描述                              |
| -------------------------- | ------- | --------------------------------- |
| tripartiteConsumableTypeId | String  | 物资分类 ID                       |
| code                       | String  | 物资分类编码                      |
| name                       | String  | 物资分类名称                      |
| parentId                   | String  | 父级分类 ID，顶级分类为"0"        |
| isLeaf                     | Boolean | 是否叶子节点，true 表示是叶子节点 |
| deleteFlag                 | Boolean | 是否删除标志，false 表示未删除    |

### 4.3 物资项目接口 (M1002)

#### 接口说明

获取物资项目数据，支持全量数据获取。

#### 请求参数

| 参数名     | 类型   | 是否必须 | 描述                                                               |
| ---------- | ------ | -------- | ------------------------------------------------------------------ |
| hospitalId | String | 是       | 医院标识，固定值：zjxrmyy                                          |
| encryptKey | String | 是       | 加密密钥，固定值：57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E |
| infno      | String | 是       | 接口编号，固定值：M1002                                            |
| input      | Object | 否       | 可为空，此接口不需要额外输入参数                                   |

#### 请求示例

```json
{
  "hospitalId": "zjxrmyy",
  "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
  "infno": "M1002",
  "input": {}
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "成功",
  "data": [
    {
      "tripartiteConsumableId": "2001",
      "code": "0101001",
      "name": "A4纸",
      "unit": "包",
      "cost": 25.5,
      "specification": "210*297mm",
      "isPutaway": true,
      "deleteFlag": false,
      "tripartiteConsumableTypeId": "1002"
    },
    {
      "tripartiteConsumableId": "2002",
      "code": "0101002",
      "name": "便签纸",
      "unit": "本",
      "cost": 5.0,
      "specification": "76*76mm",
      "isPutaway": true,
      "deleteFlag": false,
      "tripartiteConsumableTypeId": "1002"
    }
  ]
}
```

#### 响应字段说明

| 字段名                     | 类型    | 描述                           |
| -------------------------- | ------- | ------------------------------ |
| tripartiteConsumableId     | String  | 物资 ID                        |
| code                       | String  | 物资编码                       |
| name                       | String  | 物资名称                       |
| unit                       | String  | 单位                           |
| cost                       | Double  | 单价                           |
| specification              | String  | 规格型号                       |
| isPutaway                  | Boolean | 是否上架，true 表示已上架      |
| deleteFlag                 | Boolean | 是否删除标志，false 表示未删除 |
| tripartiteConsumableTypeId | String  | 所属物资分类 ID                |

### 4.4 物资出库接口 (M1003)

#### 接口说明

接收物资出库数据，用于处理 HRP 系统推送的出库记录。

#### 请求参数

| 参数名     | 类型   | 是否必须 | 描述                                                               |
| ---------- | ------ | -------- | ------------------------------------------------------------------ |
| hospitalId | String | 是       | 医院标识，固定值：zjxrmyy                                          |
| encryptKey | String | 是       | 加密密钥，固定值：57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E |
| infno      | String | 是       | 接口编号，固定值：M1003                                            |
| input      | Object | 是       | 出库数据对象，包含以下字段                                         |

**input 对象字段说明：**

| 参数名         | 类型   | 是否必须 | 描述                     |
| -------------- | ------ | -------- | ------------------------ |
| workId         | String | 是       | 工作单号，唯一标识       |
| workCode       | String | 是       | 出库单号                 |
| hospitalId     | String | 是       | 医院 ID，固定值：zjxrmyy |
| consumableList | Array  | 是       | 物资列表                 |

**consumableList 数组元素说明：**

| 参数名                 | 类型   | 是否必须 | 描述                                |
| ---------------------- | ------ | -------- | ----------------------------------- |
| tripartiteConsumableId | String | 是       | 物资 ID                             |
| name                   | String | 是       | 物资名称                            |
| specification          | String | 否       | 规格型号                            |
| unit                   | String | 是       | 单位                                |
| num                    | Double | 是       | 数量                                |
| totalPrices            | Double | 是       | 总金额                              |
| businessDept           | String | 否       | 业务部门                            |
| useDept                | String | 是       | 使用部门                            |
| campusId               | String | 否       | 院区 ID                             |
| createTime             | String | 否       | 创建时间，格式：yyyy-MM-dd HH:mm:ss |
| applicant              | String | 否       | 申请人                              |

#### 请求示例

```json
{
  "hospitalId": "zjxrmyy",
  "encryptKey": "57B836BBDD4B75000EB231EE5C5B52F5CC3D3B1D904C277E",
  "infno": "M1003",
  "input": {
    "workId": "OUT20240520001",
    "workCode": "CK20240520001",
    "hospitalId": "zjxrmyy",
    "consumableList": [
      {
        "tripartiteConsumableId": "2001",
        "name": "A4纸",
        "specification": "210*297mm",
        "unit": "包",
        "num": 10.0,
        "totalPrices": 255.0,
        "businessDept": "采购部",
        "useDept": "行政部",
        "campusId": "1",
        "createTime": "2024-05-20 10:30:00",
        "applicant": "张三"
      },
      {
        "tripartiteConsumableId": "2002",
        "name": "便签纸",
        "specification": "76*76mm",
        "unit": "本",
        "num": 5.0,
        "totalPrices": 25.0,
        "businessDept": "采购部",
        "useDept": "行政部",
        "campusId": "1",
        "createTime": "2024-05-20 10:30:00",
        "applicant": "张三"
      }
    ]
  }
}
```

#### 响应示例

```json
{
  "code": 200,
  "message": "成功",
  "data": {
    "workId": "OUT20240520001",
    "status": "SUCCESS",
    "message": "出库成功"
  }
}
```

#### 响应字段说明

| 字段名  | 类型   | 描述                                        |
| ------- | ------ | ------------------------------------------- |
| workId  | String | 工作单号                                    |
| status  | String | 处理状态，SUCCESS 表示成功，FAILED 表示失败 |
| message | String | 状态描述                                    |

#### 错误码和错误信息

| 错误码 | 错误信息                       | 描述                                           |
| ------ | ------------------------------ | ---------------------------------------------- |
| 400    | 参数错误                       | 请求参数不符合要求                             |
| 401    | 认证失败                       | hospitalId 或 encryptKey 不正确                |
| 4001   | 物资[xxx]不存在，当前库存:0    | 请求的物资 ID 在系统中不存在                   |
| 4001   | 物资[xxx]库存不足, 当前库存:xx | 物资库存不足，并返回当前库存数量，便于前端提示 |
| 500    | 系统内部错误                   | 服务器处理异常                                 |

## 5. 全量更新设计说明

针对物资分类接口(M1001)和物资项目接口(M1002)采用全量更新而非增量更新的原因：

1. **数据量较小**：物资分类数据量较小(约 10 条)，物资项目数据量也在可接受范围内(约 880 条)，全量传输不会造成明显的网络和性能负担。

2. **实现简单**：全量更新无需复杂的增量逻辑，不需要记录最后同步时间，降低了实现复杂度和出错风险。

3. **数据一致性**：全量更新可以确保两个系统间数据完全一致，能够修正历史数据不一致问题。

4. **低频次变更**：物资分类结构相对稳定，物资基础信息变更不频繁，适合全量同步策略。

5. **接口稳定性**：全量更新接口更简单，便于调试和维护，减少集成中的错误。

## 6. 注意事项

1. **时间格式**：所有接口中的时间字段均采用`yyyy-MM-dd HH:mm:ss`格式

2. **重复请求处理**：物资出库接口(M1003)会根据 workId 检查是否重复处理，对于重复的请求将返回成功但不会重复处理

3. **库存校验**：物资出库接口会严格检查以下内容：

   - 物资是否存在，如不存在则返回错误
   - 物资库存是否充足，如不足则返回错误并提示当前库存数量
   - 库存扣减操作在事务中进行，确保数据一致性
   - 对同一物资的并发请求会进行悲观锁定，避免超卖问题

4. **错误重试**：在网络异常情况下，建议设置合理的重试机制

5. **数据一致性**：建议定期进行数据校验，确保系统间数据一致性

6. **安全性要求**：

   - 所有接口通信必须使用 HTTPS 加密传输
   - 严格验证 hospitalId 和 encryptKey 参数
   - 建议实施 IP 白名单限制
   - 所有接口调用需记录详细日志

7. **性能优化建议**：

   - 出库接口建议控制批量处理的数据量
   - 全量接口考虑分页机制处理大量数据
   - 设置合理的接口超时时间

8. **异常处理**：
   - 网络连接异常：建议实施重试机制
   - 数据格式错误：详细记录错误信息
   - 业务处理异常：记录异常原因，便于排查
   - 库存不足异常：前端应友好展示剩余库存信息，引导用户调整数量

# HRP 物资系统接口与数据库字段映射关系

本文档详细描述 HRP 物资系统对接接口中的字段与内部物资系统数据库字段的映射关系，用于开发和维护参考。

## 1. 物资分类接口(M1001)字段映射

| 接口字段                   | 数据库字段  | 数据库表       | 描述                        |
| -------------------------- | ----------- | -------------- | --------------------------- |
| tripartiteConsumableTypeId | id          | mmis_aset_type | 物资分类 ID                 |
| code                       | code        | mmis_aset_type | 物资分类编码                |
| name                       | name        | mmis_aset_type | 物资分类名称                |
| parentId                   | parent_id   | mmis_aset_type | 父级分类 ID                 |
| isLeaf                     | is_leaf     | mmis_aset_type | 是否叶子节点(0 否/1 是)     |
| deleteFlag                 | delete_flag | mmis_aset_type | 删除标志(0 未删除/1 已删除) |

## 2. 物资项目接口(M1002)字段映射

| 接口字段                   | 数据库字段      | 数据库表       | 描述                        |
| -------------------------- | --------------- | -------------- | --------------------------- |
| tripartiteConsumableId     | mat_unique_code | mmis_aset_info | 物资唯一编码                |
| code                       | code            | mmis_aset_info | 物资编码                    |
| name                       | name            | mmis_aset_info | 物资名称                    |
| unit                       | metering_unit   | mmis_aset_info | 计量单位                    |
| cost                       | reference_price | mmis_aset_info | 参考价格/单价               |
| specification              | model_spec      | mmis_aset_info | 规格型号                    |
| isPutaway                  | use_status      | mmis_aset_info | 使用状态/是否上架           |
| deleteFlag                 | delete_flag     | mmis_aset_info | 删除标志(0 未删除/1 已删除) |
| tripartiteConsumableTypeId | asset_type_id   | mmis_aset_info | 物资分类 ID                 |

## 3. 物资出库接口(M1003)字段映射

### 3.1 出库单主表字段映射

| 接口字段   | 数据库字段  | 数据库表             | 描述                      |
| ---------- | ----------- | -------------------- | ------------------------- |
| workId     | work_id     | mmis_outbound_record | 外部工作单号              |
| workCode   | work_code   | mmis_outbound_record | 出库单号                  |
| hospitalId | hospital_id | mmis_outbound_record | 医院 ID                   |
| -          | create_time | mmis_outbound_record | 创建时间(系统自动生成)    |
| -          | status      | mmis_outbound_record | 处理状态(固定为'SUCCESS') |

### 3.2 出库单明细表字段映射

| 接口字段                   | 数据库字段      | 数据库表                   | 描述                       |
| -------------------------- | --------------- | -------------------------- | -------------------------- |
| tripartiteConsumableId     | mat_unique_code | mmis_outbound_apply_detail | 物资唯一编码               |
| code                       | aset_code       | mmis_outbound_apply_detail | 物资编码                   |
| name                       | aset_name       | mmis_outbound_apply_detail | 物资名称                   |
| specification              | model_spec      | mmis_outbound_apply_detail | 规格型号                   |
| unit                       | metering_unit   | mmis_outbound_apply_detail | 计量单位                   |
| num                        | out_apply_num   | mmis_outbound_apply_detail | 出库数量                   |
| campusId                   | campus_id       | mmis_outbound_apply_detail | 院区 ID                    |
| useDept                    | use_dept        | mmis_outbound_apply_detail | 使用部门                   |
| cost                       | price           | mmis_outbound_apply_detail | 单价                       |
| -                          | use_purpose     | mmis_outbound_apply_detail | 用途(固定为'外部系统出库') |
| tripartiteConsumableTypeId | asset_type_id   | mmis_outbound_apply_detail | 物资分类 ID                |
| -                          | create_time     | mmis_outbound_apply_detail | 创建时间(系统自动生成)     |
| -                          | create_user     | mmis_outbound_apply_detail | 创建人(固定为'SYSTEM')     |
| -                          | update_time     | mmis_outbound_apply_detail | 更新时间(系统自动生成)     |
| -                          | update_user     | mmis_outbound_apply_detail | 更新人(固定为'SYSTEM')     |

### 3.3 物资库存更新字段映射

| 接口字段               | 数据库字段      | 数据库表          | 描述                   |
| ---------------------- | --------------- | ----------------- | ---------------------- |
| num                    | stock_num       | mmis_material_sum | 库存数量(减去出库数量) |
| tripartiteConsumableId | mat_unique_code | mmis_material_sum | 物资唯一编码           |
| -                      | update_time     | mmis_material_sum | 更新时间(系统自动生成) |
| -                      | update_user     | mmis_material_sum | 更新人(固定为'SYSTEM') |

## 4. 数据表结构说明

### 4.1 物资分类表 (mmis_aset_type)

物资分类表存储了所有物资的分类信息，包括分类的层级结构。

### 4.2 物资信息表 (mmis_aset_info)

物资信息表存储了所有物资的基本信息，如编码、名称、规格型号等。其中 mat_unique_code 字段是物资的唯一标识符，用于与外部系统对接。

### 4.3 出库记录表 (mmis_outbound_record)

出库记录表存储了外部系统传入的出库单基本信息。

### 4.4 出库明细表 (mmis_outbound_apply_detail)

出库明细表存储了出库单中每个物资的详细信息。

### 4.5 物资库存表 (mmis_material_sum)

物资库存表存储了每个物资的当前库存数量，出库后需要更新对应物资的库存数量。

## 5. 注意事项

1. **数据类型转换**：在处理数据时需注意接口与数据库字段的数据类型可能不同，如字符串和数字类型的转换。

2. **默认值处理**：数据库中某些字段（如创建人、创建时间等）会使用默认值或系统生成的值。

3. **库存处理**：出库操作会减少物资库存，需确保库存充足，系统会在更新前进行验证。

4. **数据一致性**：为保证数据一致性，出库操作应在事务中完成，任何环节失败都应回滚整个事务。

5. **字段映射更新**：如系统架构或数据库结构发生变化，需及时更新本映射文档。

6. **物资唯一标识**：tripartiteConsumableId 对应物资系统中的 mat_unique_code 字段，是物资的唯一标识符，用于跨系统数据同步。

package com.jp.med.mmis.modules.matAccounting.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 物资结账详情表
 * <AUTHOR>
 * @email -
 * @date 2024-12-25 14:45:15
 */
@Data
public class MmisMatAccountingDetailsVo {

	/** id */
	private Integer id;

	/** 结账主表的关联id */
	private Integer accountingId;

	/** 货号 */
	private String itemNum;

	/** 入出库申请对应的申请id，根据主表的入出库类型决定这个id属于谁的，写在这里并不跟主表的id关联，只是为了后面维护方便追溯 */
	private Integer applyId;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 更新人 */
	private String updtr;

	/** 更新时间 */
	private String updateTime;

	/** 组织id */
	private String hospitalId;

	/** 品名 */
	private String name;

	/** 计量方式 */
	private String meterCode;

	/** 入出库对应数量 */
	private String num;

	/** 入出库物资对应价格 */
	private String price;

	/** 入出库该物资物资对应金额 */
	private String amt;

	/** 物资对应的唯一编码（用于追溯的，这个具体的信息可能会变） */
	private String matUniqueCode;


	@TableField("modspec")
	private String modspec;
}

package com.jp.med.mmis.modules.matAccounting.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.mmis.modules.matAccounting.mapper.read.MmisMatAccountingReadMapper;
import com.jp.med.mmis.modules.matAccounting.dto.MmisMatAccountingDto;
import com.jp.med.mmis.modules.matAccounting.vo.MmisMatAccountingVo;
import com.jp.med.mmis.modules.matAccounting.service.read.MmisMatAccountingReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisMatAccountingReadServiceImpl extends ServiceImpl<MmisMatAccountingReadMapper, MmisMatAccountingDto> implements MmisMatAccountingReadService {

    @Autowired
    private MmisMatAccountingReadMapper mmisMatAccountingReadMapper;

    @Override
    public List<MmisMatAccountingVo> queryList(MmisMatAccountingDto dto) {
        return mmisMatAccountingReadMapper.queryList(dto);
    }

    @Override
    public List<MmisMatAccountingVo> queryPageList(MmisMatAccountingDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        
        return mmisMatAccountingReadMapper.queryList(dto);
    }

}

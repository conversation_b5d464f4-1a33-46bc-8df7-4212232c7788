package com.jp.med.mmis.modules.useCodeCfg.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import com.jp.med.mmis.modules.useCodeCfg.dto.MmisMaterialUsageCfgDto;
import com.jp.med.mmis.modules.useCodeCfg.mapper.read.MmisMaterialUsageCfgReadMapper;
import com.jp.med.mmis.modules.useCodeCfg.mapper.write.MmisMaterialUsageCfgWriteMapper;
import com.jp.med.mmis.modules.useCodeCfg.service.write.MmisMaterialUsageCfgWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Objects;

/**
 * 物资用途配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-24 17:26:53
 */
@Service
@Transactional(readOnly = false)
public class MmisMaterialUsageCfgWriteServiceImpl extends ServiceImpl<MmisMaterialUsageCfgWriteMapper, MmisMaterialUsageCfgDto> implements MmisMaterialUsageCfgWriteService {

    @Autowired
    private MmisMaterialUsageCfgReadMapper usageCfgReadMapper;

    @Autowired
    private MmisMaterialUsageCfgWriteMapper usageCfgWriteMapper;


    @Override
    public void saveDto(MmisMaterialUsageCfgDto dto) {
       String maxCode =  usageCfgReadMapper.queryMaxCode();
        if (!Objects.isNull(maxCode)){
            int newCode = Integer.parseInt(maxCode) + 1;
            String s = String.format("%03d", newCode);
            dto.setUsageCode(s);
        }else {
            dto.setUsageCode("001");
        }
        dto.setHospitalId(dto.getHospitalId());
        //设置提出人的信息
        if (dto.getSysUser() != null){
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setCter(null);
            }else {
                dto.setCter(dto.getSysUser().getUsername());
            }
        }
        dto.setCreateTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_0);
        usageCfgWriteMapper.insert(dto);
    }

    @Override
    public void updateDtoById(MmisMaterialUsageCfgDto dto) {
        //设置提出人的信息
        if (dto.getSysUser() != null){
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setUpdtr(null);
            }else {
                dto.setUpdtr(dto.getSysUser().getUsername());
            }
        }
        dto.setUpdateTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        usageCfgWriteMapper.updateById(dto);
    }

    @Override
    public void removeDtoById(MmisMaterialUsageCfgDto dto) {
        //设置提出人的信息
        if (dto.getSysUser() != null){
            if (MedConst.DEV_NAME.equals(dto.getSysUser().getUsername())) {
                //如果是管理员新增提出需求，则empid设为null
                dto.setDelter(null);
            }else {
                dto.setDelter(dto.getSysUser().getUsername());
            }
        }
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setDeleteTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        usageCfgWriteMapper.updateById(dto);
    }
}

package com.jp.med.mmis.modules.matApply.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDetailDto;
import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDto;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVo;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVoNewKey;

import java.util.List;

/**
 * 物资申请明细
 *
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 16:59:25
 */
public interface MmisMaterialApplyDetailReadService extends IService<MmisMaterialApplyDetailDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<MmisMaterialApplyDetailVo> queryList(MmisMaterialApplyDetailDto dto);

    List<MmisMaterialApplyDetailVo> reqDetailList(MmisMaterialApplyDto dto);

    List<MmisMaterialApplyDetailVo> autoDetailsList(MmisMaterialApplyDto dto);

    List<MmisMaterialApplyDetailVo> waitComfirmList(MmisMaterialApplyDto dto);

    List<MmisMaterialApplyDetailVoNewKey>  detailsListNewKey(MmisMaterialApplyDto dto);

    List<MmisMaterialApplyDetailVo> queryCanUseMatByDetail(MmisMaterialApplyDto dto);
}


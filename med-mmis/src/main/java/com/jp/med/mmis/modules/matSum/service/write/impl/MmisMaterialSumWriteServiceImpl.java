package com.jp.med.mmis.modules.matSum.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.mmis.modules.asetInfo.dto.MmisAsetInfoAssistDto;
import com.jp.med.mmis.modules.asetInfo.dto.MmisAsetInfoDto;
import com.jp.med.mmis.modules.asetInfo.mapper.read.MmisAsetInfoAssistReadMapper;
import com.jp.med.mmis.modules.asetInfo.mapper.read.MmisAsetInfoReadMapper;
import com.jp.med.mmis.modules.asetInfo.mapper.write.MmisAsetInfoAssistWriteMapper;
import com.jp.med.mmis.modules.asetInfo.mapper.write.MmisAsetInfoWriteMapper;
import com.jp.med.mmis.modules.matSum.dto.MmisMaterialSumDto;
import com.jp.med.mmis.modules.matSum.dto.AdjustStockDTO;
import com.jp.med.mmis.modules.matSum.dto.PrecisionConversionDto;
import com.jp.med.mmis.modules.matSum.vo.AdjustStockVO;
import com.jp.med.mmis.modules.matSum.vo.PrecisionAnalysisDetailVo;
import com.jp.med.mmis.modules.matSum.entity.MmisMaterialSumBackup;
import com.jp.med.mmis.modules.matSum.mapper.write.MmisMaterialSumWriteMapper;
import com.jp.med.mmis.modules.matSum.mapper.read.MmisMaterialSumReadMapper;
import com.jp.med.mmis.modules.matSum.service.write.MmisMaterialSumWriteService;
import com.jp.med.mmis.modules.matPrecisionAdjustment.entity.MmisPriceHistoryEntity;
import com.jp.med.mmis.modules.matPrecisionAdjustment.service.write.MmisPriceHistoryWriteService;
import com.jp.med.common.exception.BusinessException;
import com.jp.med.common.utils.DateUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.jp.med.mmis.modules.matPrecisionAdjustment.dto.MmisPriceHistoryDto;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.text.SimpleDateFormat;
import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;
import java.math.RoundingMode;

/**
 * 物资汇总表
 * 
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 17:03:16
 */
@Service
@Transactional(readOnly = false)
public class MmisMaterialSumWriteServiceImpl extends ServiceImpl<MmisMaterialSumWriteMapper, MmisMaterialSumDto>
		implements MmisMaterialSumWriteService {

	@Autowired
	private MmisMaterialSumWriteMapper mmisMaterialSumWriteMapper;

	@Autowired
	private MmisMaterialSumReadMapper mmisMaterialSumReadMapper;

	@Autowired
	private MmisPriceHistoryWriteService mmisPriceHistoryWriteService;

	@Autowired
	private MmisAsetInfoAssistWriteMapper mmisAsetInfoAssistWriteMapper;

	@Autowired
	private MmisAsetInfoWriteMapper mmisAsetInfoWriteMapper;

	@Autowired
	private MmisAsetInfoAssistReadMapper mmisAsetInfoAssistReadMapper;

	@Autowired
	private MmisAsetInfoReadMapper mmisAsetInfoReadMapper;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public AdjustStockVO adjustStock(AdjustStockDTO dto) {
		// 1. 参数验证
		if (dto == null) {
			throw new BusinessException("请求参数不能为空");
		}

		if (dto.getSysUser() == null) {
			throw new BusinessException("用户信息不能为空");
		}

		// 获取操作人编码
		String empCode = StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())
				? dto.getSysUser().getHrmUser().getEmpCode()
				: dto.getSysUser().getNickname();

		if (StringUtils.isEmpty(dto.getMatUniqueCode())) {
			throw new BusinessException("物资唯一编码不能为空");
		}

		if (dto.getAdjustQuantity() == null) {
			throw new BusinessException("调整数量不能为空");
		}

		if (dto.getAdjustAmount() == null) {
			throw new BusinessException("调整金额不能为空");
		}

		// 2. 查询当前库存数据
		MmisMaterialSumDto currentStock = mmisMaterialSumReadMapper.selectByMatUniqueCode(dto.getMatUniqueCode());
		if (currentStock == null) {
			throw new BusinessException("未找到对应的库存记录");
		}

		// 3. 创建备份记录
		MmisMaterialSumBackup backup = new MmisMaterialSumBackup();
		backup.setBackupTime(DateUtils.getCurrentTime());
		backup.setBackupType("手动备份"); // 直接设置为"手动备份"

		// 自动生成期号（当前年月）
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		String currentPeriod = sdf.format(new Date());
		backup.setSettlePeriodNum(currentPeriod);

		backup.setItemNum(currentStock.getItemNum());
		backup.setNum(currentStock.getNum());
		backup.setName(currentStock.getName());
		backup.setModspec(currentStock.getModspec());
		backup.setWrhsAddr(currentStock.getWrhsAddr());
		backup.setWrhsCode(currentStock.getWrhsCode());
		backup.setMeterCode(currentStock.getMeterCode());
		backup.setPrice(currentStock.getPrice());
		backup.setAmt(currentStock.getAmt());
		backup.setMatUniqueCode(currentStock.getMatUniqueCode());
		backup.setAdjustQuantity(dto.getAdjustQuantity());
		backup.setAdjustAmount(dto.getAdjustAmount());
		backup.setAdjustRemark(dto.getAdjustRemark());
		backup.setCrter(empCode); // 使用empCode作为创建人
		backup.setCreateTime(DateUtils.getCurrentTime());
		backup.setHospitalId(currentStock.getHospitalId());
		backup.setIsDeleted(0);

		// 4. 执行备份操作
		mmisMaterialSumWriteMapper.insertBackup(backup);

		// 5. 更新库存数据
		MmisMaterialSumDto updateStock = new MmisMaterialSumDto();
		updateStock.setId(currentStock.getId());
		updateStock.setNum(currentStock.getNum().add(dto.getAdjustQuantity())); // 使用BigDecimal的add方法
		updateStock.setAmt(currentStock.getAmt().add(dto.getAdjustAmount()));
		updateStock.setUpdateTime(DateUtils.getCurrentTime());
		updateStock.setUpdtr(empCode); // 修改为setUpdtr

		// 6. 执行更新操作
		mmisMaterialSumWriteMapper.updateById(updateStock);

		// 7. 返回结果
		AdjustStockVO vo = new AdjustStockVO();
		vo.setBackupId(String.valueOf(backup.getId()));
		vo.setAdjustTime(backup.getBackupTime()); // 使用backupTime作为调整时间
		vo.setSuccess(true);
		return vo;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean convertPrecision(PrecisionConversionDto dto) {
		// 1. 生成批次号
		String batchNo = UUID.randomUUID().toString().replace("-", "");

		// 2. 获取需要转换的物资列表
		LambdaQueryWrapper<MmisMaterialSumDto> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(MmisMaterialSumDto::getHospitalId, dto.getHospitalId());

		// 3. 联合查询，只处理use_status='1'的记录
		wrapper.exists(
				"SELECT 1 FROM mmis_aset_info_assist a WHERE a.mat_unique_code = mmis_material_sum.mat_unique_code AND a.use_status = '1'");

		List<MmisMaterialSumDto> materials = mmisMaterialSumReadMapper.selectList(wrapper);

		// 3. 遍历物资进行精度转换
		for (MmisMaterialSumDto material : materials) {
			try {
				// 3.1 记录原价格到历史表
				MmisPriceHistoryDto history = new MmisPriceHistoryDto();
				history.setAsetId(material.getId());
				history.setAsetCode(material.getItemNum());
				history.setMatUniqueCode(material.getMatUniqueCode());
				history.setOldPrice(material.getPrice());
				history.setOldStockQuantity(material.getNum());
				history.setOldStockAmount(material.getAmt());
				history.setPrecision(dto.getPrecision());
				history.setOperationType("PRECISION_CONVERT");
				history.setBatchNo(batchNo);
				history.setOperator(dto.getOperator());
				history.setOperateTime(new Date());
				history.setRemark(dto.getRemark());
				history.setHospitalId(dto.getHospitalId());
				history.setStatus("SUCCESS");

				// 3.2 计算新价格（四舍五入）
				BigDecimal newPrice = material.getPrice().setScale(dto.getPrecision(), RoundingMode.HALF_UP);
				history.setNewPrice(newPrice);

				// 3.3 计算新金额
				BigDecimal newAmount = material.getNum().multiply(newPrice);
				history.setNewStockAmount(newAmount);

				// 3.4 计算金额差异
				history.setAmountDiff(newAmount.subtract(material.getAmt()));

				// 3.5 保存历史记录
				mmisPriceHistoryWriteService.save(history);

				// 3.6 更新物资价格和金额
				material.setPrice(newPrice);
				material.setAmt(newAmount);
				material.setUpdtr(dto.getOperator());
				material.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
				mmisMaterialSumWriteMapper.updateById(material);

				// 3.7 更新物资辅助表价格
				updateAssistPrice(material.getMatUniqueCode(), newPrice, dto.getOperator());

				// 3.8 检查是否需要更新物资主表
				checkAndUpdateInfoPrice(material.getMatUniqueCode(), newPrice, dto.getOperator(), batchNo,
						dto.getHospitalId());

			} catch (Exception e) {
				// 记录失败信息
				MmisPriceHistoryDto history = new MmisPriceHistoryDto();
				history.setAsetId(material.getId());
				history.setAsetCode(material.getItemNum());
				history.setMatUniqueCode(material.getMatUniqueCode());
				history.setOperationType("PRECISION_CONVERT");
				history.setBatchNo(batchNo);
				history.setOperator(dto.getOperator());
				history.setOperateTime(new Date());
				history.setRemark(dto.getRemark());
				history.setHospitalId(dto.getHospitalId());
				history.setStatus("FAILED");
				history.setErrorMessage(e.getMessage());
				mmisPriceHistoryWriteService.save(history);

				throw new BusinessException("精度转换失败：" + e.getMessage());
			}
		}

		return true;
	}

	@Override
	@Transactional(rollbackFor = Exception.class)
	public boolean batchConvertPrecisionByAnalysis(PrecisionConversionDto dto) {
		// 1. 参数校验
		if (dto == null) {
			throw new BusinessException("请求参数不能为空");
		}

		if (dto.getPrecision() == null || dto.getPrecision() < 1 || dto.getPrecision() > 6) {
			throw new BusinessException("精度必须在1-6之间");
		}

		if (StringUtils.isEmpty(dto.getOperator())) {
			throw new BusinessException("操作人不能为空");
		}

		if (StringUtils.isEmpty(dto.getHospitalId())) {
			throw new BusinessException("医院ID不能为空");
		}

		// 2. 创建查询参数，基于传入的精度查询需要转换的物资列表
		MmisMaterialSumDto queryDto = new MmisMaterialSumDto();
		queryDto.setPrecision(dto.getPrecision());
		queryDto.setHospitalId(dto.getHospitalId());

		// 3. 通过精度分析接口获取需要转换的物资列表
		List<PrecisionAnalysisDetailVo> materialsToConvert = mmisMaterialSumReadMapper
				.queryPrecisionAnalysisDetail(queryDto);

		if (materialsToConvert == null || materialsToConvert.isEmpty()) {
			return true; // 没有需要转换的物资，直接返回成功
		}

		// 4. 生成批次号
		String batchNo = UUID.randomUUID().toString().replace("-", "");

		// 5. 遍历物资列表执行精度转换
		for (PrecisionAnalysisDetailVo material : materialsToConvert) {
			try {
				// 5.1 根据物资ID查询物资详情
				MmisMaterialSumDto materialDto = mmisMaterialSumReadMapper.selectById(material.getId());
				if (materialDto == null) {
					continue; // 跳过不存在的物资
				}

				// 5.2 记录原价格到历史表
				MmisPriceHistoryDto history = new MmisPriceHistoryDto();
				history.setAsetId(materialDto.getId());
				history.setAsetCode(materialDto.getItemNum());
				history.setMatUniqueCode(materialDto.getMatUniqueCode());
				history.setOldPrice(materialDto.getPrice());
				history.setOldStockQuantity(materialDto.getNum());
				history.setOldStockAmount(materialDto.getAmt());
				history.setPrecision(dto.getPrecision());
				history.setOperationType("BATCH_PRECISION_CONVERT");
				history.setBatchNo(batchNo);
				history.setOperator(dto.getOperator());
				history.setOperateTime(new Date());
				history.setRemark(dto.getRemark());
				history.setHospitalId(dto.getHospitalId());
				history.setStatus("SUCCESS");

				// 5.3 计算新价格（使用分析结果中的四舍五入价格）
				BigDecimal newPrice = material.getRoundedPrice();
				history.setNewPrice(newPrice);

				// 5.4 计算新金额
				BigDecimal newAmount = materialDto.getNum().multiply(newPrice);
				history.setNewStockAmount(newAmount);

				// 5.5 计算金额差异
				history.setAmountDiff(newAmount.subtract(materialDto.getAmt()));

				// 5.6 保存历史记录
				mmisPriceHistoryWriteService.save(history);

				// 5.7 更新物资价格和金额
				materialDto.setPrice(newPrice);
				materialDto.setAmt(newAmount);
				materialDto.setUpdtr(dto.getOperator());
				materialDto.setUpdateTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
				mmisMaterialSumWriteMapper.updateById(materialDto);

				// 5.8 更新物资辅助表价格
				updateAssistPrice(materialDto.getMatUniqueCode(), newPrice, dto.getOperator());

				// 5.9 检查是否需要更新物资主表
				checkAndUpdateInfoPrice(materialDto.getMatUniqueCode(), newPrice, dto.getOperator(), batchNo,
						dto.getHospitalId());

			} catch (Exception e) {
				// 记录失败信息但继续处理下一个
				MmisPriceHistoryDto history = new MmisPriceHistoryDto();
				history.setAsetId(material.getId());
				history.setAsetCode(material.getCode());
				history.setOperationType("BATCH_PRECISION_CONVERT");
				history.setBatchNo(batchNo);
				history.setOperator(dto.getOperator());
				history.setOperateTime(new Date());
				history.setRemark(dto.getRemark());
				history.setHospitalId(dto.getHospitalId());
				history.setStatus("FAILED");
				history.setErrorMessage(e.getMessage());
				mmisPriceHistoryWriteService.save(history);

				// 继续处理下一个物资，不中断整体流程
			}
		}

		return true;
	}

	/**
	 * 更新物资辅助表价格
	 * 
	 * @param matUniqueCode 物资唯一编码
	 * @param newPrice      新价格
	 * @param operator      操作人
	 */
	private void updateAssistPrice(String matUniqueCode, BigDecimal newPrice, String operator) {
		// 查询辅助表记录
		LambdaQueryWrapper<MmisAsetInfoAssistDto> wrapper = new LambdaQueryWrapper<>();
		wrapper.eq(MmisAsetInfoAssistDto::getMatUniqueCode, matUniqueCode);
		wrapper.eq(MmisAsetInfoAssistDto::getUseStatus, "1");
		wrapper.eq(MmisAsetInfoAssistDto::getIsDeleted, 0);

		MmisAsetInfoAssistDto assistDto = mmisAsetInfoAssistReadMapper.selectOne(wrapper);
		if (assistDto != null) {
			// 更新价格
			assistDto.setRefPrice(newPrice);
			assistDto.setUpdtr(operator);
			assistDto.setUpdtrTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
			mmisAsetInfoAssistWriteMapper.updateById(assistDto);
		}
	}

	/**
	 * 检查并更新物资主表价格
	 * 
	 * @param matUniqueCode 物资唯一编码
	 * @param newPrice      新价格
	 * @param operator      操作人
	 * @param batchNo       批次号
	 * @param hospitalId    医院ID
	 */
	private void checkAndUpdateInfoPrice(String matUniqueCode, BigDecimal newPrice, String operator, String batchNo,
			String hospitalId) {
		// 1. 根据matUniqueCode获取code
		MmisAsetInfoAssistDto assistDto = mmisAsetInfoAssistReadMapper.selectOne(
				new LambdaQueryWrapper<MmisAsetInfoAssistDto>()
						.eq(MmisAsetInfoAssistDto::getMatUniqueCode, matUniqueCode)
						.select(MmisAsetInfoAssistDto::getCode));

		if (assistDto != null && assistDto.getCode() != null) {
			String code = assistDto.getCode();

			// 2. 获取code在主表中的记录数
			Long count = mmisAsetInfoReadMapper.selectCount(
					new LambdaQueryWrapper<MmisAsetInfoDto>()
							.eq(MmisAsetInfoDto::getCode, code)
							.eq(MmisAsetInfoDto::getUseStatus, "1")
							.eq(MmisAsetInfoDto::getIsDeleted, 0));

			// 3. 如果只有一条记录，则更新主表价格
			if (count != null && count == 1) {
				MmisAsetInfoDto infoDto = mmisAsetInfoReadMapper.selectOne(
						new LambdaQueryWrapper<MmisAsetInfoDto>()
								.eq(MmisAsetInfoDto::getCode, code)
								.eq(MmisAsetInfoDto::getUseStatus, "1")
								.eq(MmisAsetInfoDto::getIsDeleted, 0));

				if (infoDto != null) {
					// 记录原价格
					MmisPriceHistoryDto history = new MmisPriceHistoryDto();
					history.setAsetId(infoDto.getId());
					history.setAsetCode(infoDto.getCode());
					history.setMatUniqueCode(matUniqueCode);
					history.setOldPrice(infoDto.getRefPrice());
					history.setNewPrice(newPrice);
					history.setPrecision(null); // 主表价格转换没有指定精度
					history.setOperationType("INFO_PRICE_CONVERT");
					history.setBatchNo(batchNo);
					history.setOperator(operator);
					history.setOperateTime(new Date());
					history.setHospitalId(hospitalId);
					history.setStatus("SUCCESS");
					mmisPriceHistoryWriteService.save(history);

					// 更新主表价格
					infoDto.setRefPrice(newPrice);
					infoDto.setUpdtr(operator);
					infoDto.setUpdtrTime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
					mmisAsetInfoWriteMapper.updateById(infoDto);
				}
			}
		}
	}
}

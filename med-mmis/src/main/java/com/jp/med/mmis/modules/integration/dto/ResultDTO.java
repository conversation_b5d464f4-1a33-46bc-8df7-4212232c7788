package com.jp.med.mmis.modules.integration.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 统一返回结果DTO
 */
@Data
@ApiModel(description = "统一返回结果DTO")
public class ResultDTO {

	@ApiModelProperty(value = "状态码")
	private Integer code;

	@ApiModelProperty(value = "响应消息")
	private String message;

	@ApiModelProperty(value = "响应数据")
	private Object data;

	/**
	 * 成功结果，带数据
	 * 
	 * @param data 数据
	 * @return 结果DTO
	 */
	public static ResultDTO success(Object data) {
		ResultDTO result = new ResultDTO();
		result.setCode(200);
		result.setMessage("success");
		result.setData(data);
		return result;
	}

	/**
	 * 成功结果，不带数据
	 * 
	 * @return 结果DTO
	 */
	public static ResultDTO success() {
		return success(null);
	}

	/**
	 * 失败结果
	 * 
	 * @param code    状态码
	 * @param message 消息
	 * @return 结果DTO
	 */
	public static ResultDTO failed(Integer code, String message) {
		ResultDTO result = new ResultDTO();
		result.setCode(code);
		result.setMessage(message);
		return result;
	}
}
package com.jp.med.mmis.modules.integration.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.integration.dto.ConsumableOutboundDTO;
import com.jp.med.mmis.modules.integration.dto.MmisOutboundBackupDto;

/**
 * M1003物资出库接口数据备份表(用于撤销操作)
 * 
 * <AUTHOR>
 * @email -
 * @date 2025-04-21 11:35:22
 */
public interface MmisOutboundBackupWriteService extends IService<MmisOutboundBackupDto> {

	/**
	 * 备份出库数据
	 * 
	 * @param outboundDTO 出库DTO
	 * @param outboundId  系统生成的出库单号
	 * @param applyId     出库申请ID
	 * @return 是否成功
	 */
	boolean backupOutboundData(ConsumableOutboundDTO outboundDTO, String outboundId, Integer applyId);

	/**
	 * 更新出库数据处理状态
	 * 
	 * @param workId       工单ID
	 * @param status       处理状态
	 * @param errorMessage 错误信息
	 * @return 是否成功
	 */
	boolean updateProcessStatus(String workId, String status, String errorMessage);

	/**
	 * 记录M1007预出库尝试（无论成功或失败）
	 *
	 * @param outboundDTO   预出库DTO
	 * @param preOutboundId 预出库单号 (M1007成功时生成，失败时可能为null)
	 * @param applyId       关联的mmis_outbound_apply表的ID (M1007成功时生成，失败时可能为null)
	 * @param processResult 处理结果 ("SUCCESS" 或 "FAILED")
	 * @param errorMessage  错误信息 (失败时记录)
	 * @return 是否成功记录
	 */
	boolean logPreOutboundAttempt(ConsumableOutboundDTO outboundDTO, String preOutboundId, Integer applyId,
			String processResult, String errorMessage);

	/**
	 * 创建一个通用的出库备份记录
	 *
	 * @param backupDto 包含所有备份信息的DTO对象
	 * @return 是否成功创建
	 */
	boolean createOutboundBackup(MmisOutboundBackupDto backupDto);

	/**
	 * 保存或更新出库备份记录（处理唯一约束冲突）
	 * 如果记录已存在(通过work_id判断)，则更新记录
	 * 如果记录不存在，则创建新记录
	 *
	 * @param backupDto 包含所有备份信息的DTO对象
	 * @return 是否成功操作
	 */
	boolean saveOrUpdateOutboundBackup(MmisOutboundBackupDto backupDto);

	/**
	 * 更新备份记录的interface_code，记录操作历史
	 * 
	 * @param workId       工单ID
	 * @param newInterface 新的接口编号（如M1004）
	 * @return 是否更新成功
	 */
	boolean appendInterfaceCodeHistory(String workId, String newInterface);

	/**
	 * 安全地更新备份记录状态（避免时间戳解析问题）
	 * 通过workId更新状态、撤销原因和操作人，不查询包含时间戳的字段
	 * 
	 * @param workId         工单ID
	 * @param status         新状态
	 * @param revokeReason   撤销原因
	 * @param revokeOperator 撤销操作人
	 * @return 是否更新成功
	 */
	boolean updateBackupStatusSafely(String workId, String status, String revokeReason, String revokeOperator);
}

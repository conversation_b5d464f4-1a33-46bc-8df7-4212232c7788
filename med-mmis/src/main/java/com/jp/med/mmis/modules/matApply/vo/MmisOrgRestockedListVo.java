package com.jp.med.mmis.modules.matApply.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 科室待补货清单
 * <AUTHOR>
 * @email -
 * @date 2024-10-28 15:23:58
 */
@Data
public class MmisOrgRestockedListVo {

	/** id */
	private Integer id;

	/** 物资申请id */
	private Integer applyId;


	private String wrhsCode;


	/**
	 * 申请科室id
	 */
	private String appyOrgId;

	/** 需要后续补货完成送货数量 */
	private BigDecimal applyNum;

	/** 补货备注,后续也要查上 */
	private String remark;

	/** 创建人 */
	private String crter;
	private String crterName;

	/** 创建时间 */
	private String createTime;

	/** 组织id */
	private String hospitalId;

	/** 逻辑删除 */
	private Integer isDeleted;

	/** 计量方式 */
	private String meterCode;

	/** 申领时的参考价格，仅做参考，实际不同 */
	private BigDecimal price;

	/** 申领需要的规格 */
	private String modspec;

	/** 物资名称 */
	private String name;

	/** 物资代码（领用的物资类） */
	private String itemNum;

	/** 实际采购回来的价格可能不一致（这是后续到货物资的唯一编码，可能价格不同或规格不同） */
	private String matUniqueCode;

	/** 补货状态（0：已提交，待库管提出补货申请，1：库管已提交补货申请，2：采购执行中，3：库房已签收，4：库房已出货，5：提出科室已确认收货） */
	private String restockStatus;

	/** 关联补货采购申请的id */
	private Integer suppleId;

	/** 关联入库时的id(备用) */
	private Integer storageId;

	/** 关联出库时id（备用） */
	private Integer outboundId;


	/**
	 * 申请时该物资的实际库存，没啥用就是用来回显的
	 */
	private BigDecimal actNum;
	/**
	 * 计量单位名称
	 */
	private String meterUnitName;

	private String meterTypeName;


	private String meterUnit;


	private String code;

	private String  wrhsAddr;

	private String  wrhsAddrName;

	private String asetType;

	private String asetTypeName;

	/**
	 * 物资图片
	 */
	private String att;

	private String attName;

	private BigDecimal amt;


	private BigDecimal refPrice;

	private String mfgDate;

	private String exprinDate;

	private Integer actSendNum;

	private String phone;

	/**
	 * 申请科室名称
	 */
	private String appyOrgName;
}

package com.jp.med.mmis.modules.asetType.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.common.interceptors.BaseTree;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 物资类别
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-24 21:27:00
 */
@Data
public class MmisAsetTypeVo implements BaseTree<String, MmisAsetTypeVo> {

    /**
     * $column.comments
     */
    private Integer id;

    /**
     * 类别代码
     */
    private String code;

    /**
     * 类别名称
     */
    private String name;

    /**
     * 父代码
     */
    private String parentCode;

    /**
     * 仓库
     */
    private String wrhs;

    /**
     * 计量方式
     */
    private String mtrType;

    /**
     * 出库计价方式
     */
    private String stooutPrctpe;

    /**
     * 保质期
     */
    private String exprinDate;

    /**
     * 存货控制科目
     */
    private String stogCtrlSub;

    /**
     * 进货控制科目
     */
    private String addCtrlSub;

    /**
     * 成本控制科目
     */
    private String costCtrlSub;

    /**
     * 最低储量
     */
    private String minReserve;

    /**
     * 最高储量
     */
    private String maxReserve;

    /**
     * 最佳储量
     */
    private String bestReserve;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建时间
     */
    private String crterTime;

    /**
     * 更新人
     */
    private String updtr;

    /**
     * 更新时间
     */
    private String updtrTime;

    /**
     * 逻辑删除标志
     */
    private Integer isDeleted;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    private List<MmisAsetTypeVo> children;


    @Override
    public String getPid() {
        return parentCode;
    }

    @Override
    public void setPid(String pid) {
        this.parentCode = pid;
    }

    @Override
    public void addChild(MmisAsetTypeVo node) {
        if (Objects.isNull(this.children)) {
            this.children = new ArrayList<>();
        }
        this.children.add(node);
    }

    /**
     * 仓库名称
     */
    private String wrhsName;
}

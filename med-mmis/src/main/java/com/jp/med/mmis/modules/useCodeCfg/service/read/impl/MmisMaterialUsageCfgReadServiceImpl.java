package com.jp.med.mmis.modules.useCodeCfg.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.mmis.modules.useCodeCfg.dto.MmisMaterialUsageCfgDto;
import com.jp.med.mmis.modules.useCodeCfg.mapper.read.MmisMaterialUsageCfgReadMapper;
import com.jp.med.mmis.modules.useCodeCfg.service.read.MmisMaterialUsageCfgReadService;
import com.jp.med.mmis.modules.useCodeCfg.vo.MmisMaterialUsageCfgVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class MmisMaterialUsageCfgReadServiceImpl extends ServiceImpl<MmisMaterialUsageCfgReadMapper, MmisMaterialUsageCfgDto> implements MmisMaterialUsageCfgReadService {

    @Autowired
    private MmisMaterialUsageCfgReadMapper mmisMaterialUsageCfgReadMapper;

    @Override
    public List<MmisMaterialUsageCfgVo> queryList(MmisMaterialUsageCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return mmisMaterialUsageCfgReadMapper.queryList(dto);
    }

    @Override
    public List<MmisMaterialUsageCfgVo> queryCfgList(MmisMaterialUsageCfgDto dto) {
        return mmisMaterialUsageCfgReadMapper.queryMaterialUsageCfg(dto);
    }
}

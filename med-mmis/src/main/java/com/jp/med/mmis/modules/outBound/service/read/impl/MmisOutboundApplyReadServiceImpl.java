package com.jp.med.mmis.modules.outBound.service.read.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.config.ConfigureBuilder;
import com.deepoove.poi.policy.HackLoopTableRenderPolicy;
import com.deepoove.poi.policy.LoopColumnTableRenderPolicy;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.AuditConst;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.user.HrmUser;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.ConvertUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.common.util.PdfUtil;
import com.jp.med.mmis.modules.common.service.read.MmisAuditRcdfmReadService;
import com.jp.med.mmis.modules.common.vo.MmisAuditRcdfmVo;
import com.jp.med.mmis.modules.exportFileManage.dto.MmisExportAttachmentDto;
import com.jp.med.mmis.modules.exportFileManage.dto.MmisOutboundExportRecordDto;
import com.jp.med.mmis.modules.exportFileManage.mapper.read.MmisOutboundExportRecordReadMapper;
import com.jp.med.mmis.modules.exportFileManage.mapper.write.MmisExportAttachmentWriteMapper;
import com.jp.med.mmis.modules.exportFileManage.mapper.write.MmisOutboundExportRecordWriteMapper;
import com.jp.med.mmis.modules.exportFileManage.vo.MmisOutboundExportRecordVo;
import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDto;
import com.jp.med.mmis.modules.matApply.mapper.read.MmisMaterialApplyReadMapper;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVo;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyVo;
import com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo;
import com.jp.med.mmis.modules.outBound.entity.MmisOutApplyTable;
import com.jp.med.mmis.modules.outBound.mapper.read.MmisOutboundApplyDetailsReadMapper;
import com.jp.med.mmis.modules.outBound.mapper.write.MmisOutboundApplyWriteMapper;
import com.jp.med.mmis.modules.outBound.vo.MmisOutboundApplyDetailsVo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.bouncycastle.cms.PasswordRecipientId;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import com.jp.med.mmis.modules.outBound.mapper.read.MmisOutboundApplyReadMapper;
import com.jp.med.mmis.modules.outBound.dto.MmisOutboundApplyDto;
import com.jp.med.mmis.modules.outBound.vo.MmisOutboundApplyVo;
import com.jp.med.mmis.modules.outBound.service.read.MmisOutboundApplyReadService;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

@Transactional(readOnly = true)
@Service
public class MmisOutboundApplyReadServiceImpl extends ServiceImpl<MmisOutboundApplyReadMapper, MmisOutboundApplyDto>
        implements MmisOutboundApplyReadService {

    @Autowired
    private MmisOutboundApplyReadMapper mmisOutboundApplyReadMapper;

    @Autowired
    private MmisAuditRcdfmReadService auditRcdfmReadService;

    @Autowired
    private MmisOutboundApplyDetailsReadMapper detailsReadMapper;

    @Autowired
    private MmisMaterialApplyReadMapper materialApplyReadMapper;

    @Autowired
    private MmisOutboundExportRecordReadMapper exportRecordReadMapper;

    @Override
    public List<MmisOutboundApplyVo> queryList(MmisOutboundApplyDto dto) {
        try {
            if (dto == null) {
                return Collections.emptyList();
            }

            if (dto.getPageNum() != null && dto.getPageSize() != null && dto.getPageNum() > 0
                    && dto.getPageSize() > 0) {
                PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
            }

            if (!MedConst.DEV_NAME.equals(dto.getUsername())) {
                if (dto.getSysUser() != null) {
                    HrmUser hrmUser = dto.getSysUser().getHrmUser();
                    if (hrmUser != null) {
                        String empCode = hrmUser.getEmpCode();

                        if (StringUtils.isNotEmpty(empCode)) {
                            if (StringUtils.isNotEmpty(dto.getAudit())) {
                                dto.setChker(empCode);
                            } else {
                                if (StringUtils.isEmpty(dto.getOutStatus())) {
                                    dto.setCrter(empCode);
                                }
                            }
                        }
                    }
                }
            }

            if (AuditConst.RES_IN.equals(dto.getChkState())) {
                dto.setChkState(null);
            }

            List<MmisOutboundApplyVo> mmisOutboundApplyVos = mmisOutboundApplyReadMapper.queryList(dto);

            if (CollectionUtils.isEmpty(mmisOutboundApplyVos)) {
                return Collections.emptyList();
            }

            for (MmisOutboundApplyVo mmisOutboundApplyVo : mmisOutboundApplyVos) {
                if (mmisOutboundApplyVo == null || mmisOutboundApplyVo.getId() == null) {
                    continue;
                }

                try {
                    MmisOutboundExportRecordVo mmisOutboundExportRecordVo = exportRecordReadMapper
                            .queryListByOutBoundId(mmisOutboundApplyVo.getId());

                    mmisOutboundApplyVo.setExportStatus(!Objects.isNull(mmisOutboundExportRecordVo)
                            && StringUtils.isNotEmpty(mmisOutboundExportRecordVo.getExportStatus())
                                    ? mmisOutboundExportRecordVo.getExportStatus()
                                    : MedConst.TYPE_0);
                } catch (Exception e) {
                    mmisOutboundApplyVo.setExportStatus(MedConst.TYPE_0);
                }

                try {
                    List<MmisOutboundApplyDetailsVo> details = detailsReadMapper
                            .queryListByApplyId(mmisOutboundApplyVo.getId());

                    if (CollectionUtils.isEmpty(details)) {
                        mmisOutboundApplyVo.setOutBoundSummary("");
                        continue;
                    }

                    StringBuilder outBoundSummary = new StringBuilder();
                    for (MmisOutboundApplyDetailsVo detail : details) {
                        if (detail == null) {
                            continue;
                        }
                        String name = detail.getName() != null ? detail.getName() : "";
                        String num = detail.getNum() != null ? detail.getNum().toString() : "";
                        String remark = detail.getRemark() != null ? detail.getRemark() : "";
                        outBoundSummary.append(name).append("*").append(num).append("<").append(remark)
                                .append(">;");
                    }
                    mmisOutboundApplyVo.setOutBoundSummary(outBoundSummary.toString());
                } catch (Exception e) {
                    mmisOutboundApplyVo.setOutBoundSummary("");
                }
            }
            return mmisOutboundApplyVos;
        } catch (Exception e) {
            e.printStackTrace();
            return Collections.emptyList();
        }
    }

    @Override
    public MmisOutboundApplyVo queryDocmentNumStr(MmisAsetStorageDto dto) {
        MmisOutboundApplyVo outboundApplyVo = mmisOutboundApplyReadMapper.queryDocNum();
        StringBuilder stb = new StringBuilder("CK");
        StringBuilder append = stb.append(outboundApplyVo.getDocmentNum());
        outboundApplyVo.setDocmentNum(append.toString());
        return outboundApplyVo;
    }

    @Override
    public MmisOutboundApplyVo queryAppAuditDetail(MmisMaterialApplyDto dto) {
        if (dto == null || dto.getAuditBchno() == null) {
            return null;
        }

        dto.setSqlAutowiredHospitalCondition(true);

        List<MmisAuditRcdfmVo> auditRcdfmVos = Collections.emptyList();
        if (dto.getSysUser() != null && dto.getSysUser().getHrmUser() != null &&
                StringUtils.isNotEmpty(dto.getSysUser().getHrmUser().getEmpCode())) {
            auditRcdfmVos = auditRcdfmReadService.getAuditDetails(dto.getAuditBchno(),
                    dto.getSysUser().getHrmUser().getEmpCode());
        }

        MmisOutboundApplyVo applyVo = mmisOutboundApplyReadMapper.selectOneByBchno(dto.getAuditBchno());
        if (applyVo == null || applyVo.getId() == null) {
            return null;
        }

        List<MmisOutboundApplyDetailsVo> applyDetailVos = detailsReadMapper.queryListByApplyId(applyVo.getId());
        applyVo.setTableDetails(applyDetailVos != null ? applyDetailVos : Collections.emptyList());

        if (!CollectionUtil.isEmpty(auditRcdfmVos)) {
            auditRcdfmVos.sort(Comparator.comparing(MmisAuditRcdfmVo::getChkSeq));
            applyVo.setAuditDetails(auditRcdfmVos);
        } else {
            applyVo.setAuditDetails(Collections.emptyList());
        }

        return applyVo;
    }

    @Override
    public List<MmisOutboundApplyDetailsVo> queryOutBoundApplyDetailsBy2Code(MmisOutboundApplyDto dto) {
        if (dto == null) {
            return Collections.emptyList();
        }
        List<MmisOutboundApplyDetailsVo> results = mmisOutboundApplyReadMapper.queryOutBoundApplyDetailsBy2Code(dto);
        return results != null ? results : Collections.emptyList();
    }

    /**
     * 查询待结账的物资出库单（其实就是查没有结账期号的数据）
     * 
     * @param dto 查询参数
     * @return 出库单列表
     */
    @Override
    public List<MmisOutboundApplyVo> queryWaitAccountingOutBount(MmisOutboundApplyDto dto) {
        return null;
    }

    /**
     * 查询出库明细数据
     * 
     * @param dto 查询参数
     * @return 出库明细列表
     */
    @Override
    public List<MmisOutboundApplyDetailsVo> queryOutboundDetailList(MmisOutboundApplyDto dto) {
        if (dto == null) {
            return Collections.emptyList();
        }
        List<MmisOutboundApplyDetailsVo> results = mmisOutboundApplyReadMapper.queryOutboundDetailList(dto);
        return results;
    }
}

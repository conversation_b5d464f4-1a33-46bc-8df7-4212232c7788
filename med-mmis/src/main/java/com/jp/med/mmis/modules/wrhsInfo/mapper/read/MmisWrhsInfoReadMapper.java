package com.jp.med.mmis.modules.wrhsInfo.mapper.read;

import com.jp.med.mmis.modules.wrhsInfo.dto.MmisWrhsInfoDto;
import com.jp.med.mmis.modules.wrhsInfo.vo.MmisWrhsInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.mmis.modules.wrhsInfo.vo.MmisWrhsManagerVo;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 仓库信息
 * <AUTHOR>
 * @email -
 * @date 2024-01-24 15:43:47
 */
@Mapper
public interface MmisWrhsInfoReadMapper extends BaseMapper<MmisWrhsInfoDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisWrhsInfoVo> queryList(MmisWrhsInfoDto dto);

    /**
     * 获取新增仓库代码
     * @return
     */
    String queryWrhsCode();

    List<MmisWrhsInfoVo> queryManagerList(MmisWrhsInfoDto dto);

	MmisWrhsInfoVo querywrhsAndManagerDetail(MmisWrhsInfoDto dto);


    MmisWrhsInfoVo queryOneByID(Integer id);
}

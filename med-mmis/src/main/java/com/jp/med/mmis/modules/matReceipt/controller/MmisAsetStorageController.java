package com.jp.med.mmis.modules.matReceipt.controller;

import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDetailDto;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageDetailVo;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo;
import com.jp.med.mmis.modules.matReceipt.vo.MmisInvoRcdVo;
import com.jp.med.mmis.modules.outBound.dto.MmisOutboundApplyDto;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto;
import com.jp.med.mmis.modules.matReceipt.service.read.MmisAsetStorageReadService;
import com.jp.med.mmis.modules.matReceipt.service.write.MmisAsetStorageWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;
import java.util.Map;

/**
 * 物资入库
 *
 * <AUTHOR>
 * @email -
 * @date 2024-02-19 15:01:07
 */
@Api(value = "物资入库", tags = "物资入库")
@RestController
@RequestMapping("mmisAsetStorage")
public class MmisAsetStorageController {

    @Autowired
    private MmisAsetStorageReadService mmisAsetStorageReadService;

    @Autowired
    private MmisAsetStorageWriteService mmisAsetStorageWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询物资入库")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.paging(mmisAsetStorageReadService.queryList(dto));
    }

    @ApiOperation("查询物资入库")
    @PostMapping("/waitGoReimReceipt")
    public CommonResult<?> waitGoReimReceipt(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageReadService.waitGoReimReceipt(dto));
    }

    /**
     * 查询待结账的物资入库单
     */
    @ApiOperation("查询待结账的物资入库单")
    @PostMapping("/queryWaitAccountingReceipt")
    public CommonResult<?> queryWaitAccountingReceipt(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageReadService.queryWaitAccountingReceipt(dto));
    }

    /**
     * 单据号查询
     */
    @ApiOperation("单据号查询")
    @PostMapping("/docmentNum")
    public CommonResult<?> docmentNum(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageReadService.queryDocmentNumStr(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增物资入库")
    @PostMapping("/save")
    public CommonResult<?> save(MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.saveDto(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改物资入库")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.updateDtoById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除物资入库")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除原发票记录")
    @PutMapping("/removeInvoice")
    public CommonResult<?> removeInvoice(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.removeInvoice(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询APP审核页面数据详情")
    @PostMapping("/appAuditDetail")
    public CommonResult<?> queryAppAuditDetail(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageReadService.queryAppAuditDetail(dto));
    }

    @ApiOperation("OCR识别")
    @PostMapping("/ocrIdentify")
    public CommonResult<?> ocrIdentify(MmisAsetStorageDto dto) {
        List<MmisInvoRcdVo> list = mmisAsetStorageWriteService.ocrIdentify(dto);
        return CommonResult.success(list);
    }

    /**
     * 生成出库单模板
     */
    @ApiOperation("生成出库单模板")
    @PostMapping("/generateStorageTem")
    public CommonResult<?> exportStorageFormTem(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageWriteService.generateTemp(dto));
    }

    // 根据详情id集合获取入库记录 然后再打印入库单文件
    @ApiOperation("根据详情id集合获取入库记录 然后再打印入库单文件")
    @PostMapping("/generateStorageTempByDetailIds")
    public CommonResult<?> generateStorageTempByDetailIds(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageWriteService.generateStorageTempByDetailIds(dto));

    }

    /**
     * 物资入库
     * 需求：物资入库这边的业务是，物资先到了库房，只是物资班这里进行记录，
     * 并且这个记录会让库房那边审核对不对，（暂时没有提到库房那边可不可以修改入库申请，统一不能）
     * 物资申请的申请人可以修改申请详情的情况只有审核中才可以修改
     * 物资这边就相当于管理库房的，库房那里的操作人员只管取货
     *
     * 所以这边根据业务，就是在入库申请表加了inStatus状态：0：需要入库，1：已入库，2：审核失败，无需入库，3：已撤销，无需入库
     */
    @ApiOperation("入库状态")
    @PutMapping("/updateStorageStatus")
    public CommonResult<?> confirmStorageStatus(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.updateStorageStatus(dto);
        return CommonResult.success();
    }

    /**
     * 新增撤销申请功能，chkState = 4
     * ，但是还是要展示出来（入库申请撤销的话，得要把对应发票记录删除，发票表里的，但是申请条目里面的发票图片不能删除，）
     * 
     * @param dto
     * @return
     */
    @ApiOperation("撤销申请")
    @PutMapping("/revokeReceiptApply")
    public CommonResult<?> revokeReceiptApply(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.revokeReceiptApply(dto);
        return CommonResult.success();
    }

    /**
     * 查询已入库的物资
     * 已入库的物资特点：chkState =1 (审核通过) inStatus =1 （已入库过） isDeleted = 0 （未被删除）
     */
    @ApiOperation("查询已入库的物资")
    @PostMapping("/hasStroagedMats")
    public CommonResult<?> searchHasStroagedMats(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.paging(mmisAsetStorageReadService.hasStroagedMats(dto));
    }

    /**
     *
     * 和上面的区别是不用连表查入库时的金额
     * 
     * @param dto
     * @return
     */
    @ApiOperation("查询已入库的物资（物资申请用的）")
    @PostMapping("/hasStroagedMatsInApply")
    public CommonResult<?> searchHasStroagedMatsInApply(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.paging(mmisAsetStorageReadService.hasStroagedMatsInApply(dto));
    }

    /**
     * 查询库存物资信息（使用matUniqueCode作为key）
     * 返回结果包含：物资唯一编码、物资编码、物资名称、规格型号、参考价格等信息
     *
     * @param dto 查询参数
     * @return 物资库存列表
     */
    @ApiOperation("查询库存物资信息（使用matUniqueCode作为key）")
    @PostMapping("/hasStroagedMatsInApplyByUniqueCode")
    public CommonResult<?> searchHasStroagedMatsInApplyByUniqueCode(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.paging(mmisAsetStorageReadService.hasStroagedMatsInApplyByUniqueCode(dto));
    }

    @ApiOperation("根据物资编码（特殊编码）或者就是物资编码查询相关的入库记录")
    @PostMapping("/queryStorageRecordsByMat2Code")
    public CommonResult<?> queryStorageRecordsByMat2Code(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageReadService.queryStorageRecordsByMat2Code(dto));
    }

    /**
     * 根据选择的入库单生成报销任务
     * 为什么不选具体的物资进行报销：因为入库单一般是会和发票进行绑定的，所以选择入库单进行报销
     * 不会出现一个入库单是多个供应商，只会多个发票或单个发票对应一个供应商
     * 
     * @param dto
     * @return
     */
    @ApiOperation("查询物资入库")
    @PostMapping("/saveMmisMatPurcTask")
    public CommonResult<?> saveMmisMatPurcTask(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.saveMmisMatPurcTask(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询物资入库")
    @PostMapping("/saveMmisInMatSumPurcTask")
    public CommonResult<?> saveMmisInMatSumPurcTask(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.saveMmisInMatSumPurcTask(dto);
        return CommonResult.success();
    }

    /**
     * 反写状态
     */
    @ApiOperation("反写状态")
    @PostMapping("/updateDetailsReimIdAndStatus")
    public CommonResult<?> updatePurcTaskReimId(@RequestBody MmisAsetStorageDto dto) {
        mmisAsetStorageWriteService.updateItemReimId(dto);
        return CommonResult.success();
    }

    /**
     * 文件下载
     */
    @ApiOperation("文件下载")
    @PostMapping("/fileDownload")
    public void fileDownload(@RequestBody MmisAsetStorageDto dto, HttpServletResponse response) {
        // return
        // CommonResult.success(cmsContractOppositeReadService.fileDownload(dto));
        try {
            InputStream fileInputStream = OSSUtil.getObject(OSSConst.BUCKET_MMIS, dto.getFilePath());
            response.setHeader("Content-Disposition", "attachment;filename=" + dto.getFilePath());
            response.setContentType("application/force-download");
            response.setCharacterEncoding("UTF-8");
            IOUtils.copy(fileInputStream, response.getOutputStream());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查询入库明细数据
     */
    @ApiOperation("查询入库明细数据")
    @PostMapping("/aLLDetailList")
    public CommonResult<?> queryStorageDetailList(@RequestBody MmisAsetStorageDto dto) {
        return CommonResult.success(mmisAsetStorageReadService.queryStorageDetailList(dto));
    }

}

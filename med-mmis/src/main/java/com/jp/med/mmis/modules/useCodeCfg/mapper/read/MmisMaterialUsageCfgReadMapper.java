package com.jp.med.mmis.modules.useCodeCfg.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.mmis.modules.useCodeCfg.dto.MmisMaterialUsageCfgDto;
import com.jp.med.mmis.modules.useCodeCfg.vo.MmisMaterialUsageCfgVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 物资用途配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-24 17:26:53
 */
@Mapper
public interface MmisMaterialUsageCfgReadMapper extends BaseMapper<MmisMaterialUsageCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisMaterialUsageCfgVo> queryList(MmisMaterialUsageCfgDto dto);


    String queryMaxCode();

    List<MmisMaterialUsageCfgVo> queryMaterialUsageCfg(MmisMaterialUsageCfgDto dto);
}

package com.jp.med.mmis.modules.matBack.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matBack.dto.MmisBackApplyDto;
import com.jp.med.mmis.modules.matBack.vo.MmisBackApplyVo;

import java.util.List;

/**
 * 物资退货申请表
 * <AUTHOR>
 * @email -
 * @date 2024-10-21 21:41:31
 */
public interface MmisBackApplyReadService extends IService<MmisBackApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<MmisBackApplyVo> queryList(MmisBackApplyDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<MmisBackApplyVo> queryPageList(MmisBackApplyDto dto);
}


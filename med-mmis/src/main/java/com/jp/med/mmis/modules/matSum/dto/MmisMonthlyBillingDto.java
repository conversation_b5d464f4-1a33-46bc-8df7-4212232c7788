package com.jp.med.mmis.modules.matSum.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;

import lombok.Data;

/**
 * 每月轧账表
 * <AUTHOR>
 * @email -
 * @date 2024-10-21 21:01:53
 */
@Data
@TableName("mmis_monthly_billing" )
public class MmisMonthlyBillingDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 扎帐日期 */
    @TableField("rcd_time")
    private String rcdTime;

    /** 扎帐类别的code(就是物资类别的非叶子节点) */
    @TableField("type_code")
    private String typeCode;

    /** 入出库类型 */
    @TableField("inv_type_code")
    private String invTypeCode;

    /** 扎帐金额 */
    @TableField("account_amt")
    private BigDecimal accountAmt;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

}

package com.jp.med.mmis.modules.outBound.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import com.jp.med.mmis.modules.common.vo.MmisAuditRcdfmVo;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVo;
import lombok.Data;

import java.util.List;

/**
 * 物资出库
 * <AUTHOR>
 * @email -
 * @date 2024-03-14 17:18:10
 */
@Data
public class MmisOutboundApplyVo {

	/** id */
	private Integer id;

	/** 申请科室 */
	private String appyOrgId;

	/** $column.comments */
	private String appyer;

	/** 审核批次号 */
	private String auditBchno;

	/** 审核状态 */
	private String chkState;

	/** 开单日期 */
	private String billDate;

	/** 单据号 */
	private String docmentNum;

	/** 手工单号 */
	private String manualDocNum;

	/** 用途 */
	private String purpose;

	/** 申请备注 */
	private String applyRemark;

	/** 入出库类型 */
	private String invType;

	/** 仓库=库位代码 */
	private String wrhsCode;

	/** 出库状态 */
	private String outStatus;

	/** 出库操作人 */
	private String outEmp;

	/** 出库时间 */
	private String outTime;

	/** 出库备注 */
	private String outRemark;

	/**
	 * 出库的目标科室
	 */
	private String outTagetOrg;

	/**
	 * 出库的目标科室的具体人员
	 */
	private String outAppyer;

	/**
	 * 物资申领的编号
	 */
	private String matApplyBchno;

	/**
	 * 所属园区
	 */
	private String hosCampus;

	/**
	 * 物资申领流水号
	 */
	private String matApplySerial;


	/**
	 * 物资申领的编号
	 */
	private String outTargetOrgId;

	private String  outTagetOrgName;
	private String  outAppyerName;


	/** 组织id */
	private String hospitalId;

	/** 逻辑删除标志 */
	private Integer isDeleted;


	/**
	 * 审核标志： 1代表审核界面
	 */
	private String auditFlag;

	/**
	 * 创建人
	 */
	private String crter;

	/**
	 * 创建时间
	 */
	private String createTime;


	private String updtr;


	private String updateTime;

	/**
	 * 物资申请明细
	 */
	private List<MmisOutboundApplyDetailsVo> tableDetails;

	/**
	 * 审核流程详情
	 */
	private List<MmisAuditRcdfmVo> auditDetails;

	private String empName;

	private String orgName;

	/**
	 * 入库时的批次号
	 */
	private String storageBchno;

	/**
	 * 出库人拍的照片
	 */
	private String att;

	private String attName;

	private String invTypeName;

	/**
	 * 出库详情摘要
	 */
	private String outBoundSummary;

	/**
	 * 查询申领人的科室和和领用人
	 */
	private String matApplyOrgId;
	private String matApplyApyyer;
	private String matApplyApyyerName;
	private String matApplyOrgName;


	/**
	 * 是否打印过了
	 */
	private String exportStatus;

	/**
	 * 结账期号
	 * 同一期结账的入出库单会有相同的期号
	 * 同时还会把数据进行备份到
	 *入出库的一个备份记录单，表示这笔账就不变了，期末的数据就按照这个来
	 * 反结账就是去除这个期号，然后再写进备份记录表中，
	 * 备份记录表就是存储每次结账的记录的，相当与一个备份，就算反结账，也不会进行删除记录，用于出现账务问题恢复备份
	 */
	private String settlePeriodNum;



	

	
}

package com.jp.med.mmis.modules.useCodeCfg.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;

import lombok.Data;

/**
 * 计量方式
 *
 * <AUTHOR>
 * @email -
 * @date 2024-01-26 17:03:46
 */
@Data
public class MmisMeteringModeCfgVo {

    /**
     * id
     */
    private Integer id;

    /**
     * 代码
     */
    private String meterCode;

    /**
     * 名称
     */
    private String meterName;

    /**
     * 最小计量单位的名称（这是产品的默认计量单位。）
     */
    private String baseUnitName;

    /**
     * 最小计量单位与计量单位一之间的转换系数(通常对于最小计量单位来说，这个系数是1。)
     */
    private BigDecimal baseUnitCoefficient;

    /**
     * 逻辑状态(0未删除，1已删除)
     */
    private String activeFlag;

    /**
     * 创建人
     */
    private String cter;

    /**
     * 创建人
     */
    private String createTime;

    /**
     * 更新人
     */
    private String updtr;

    /**
     * 更新时间
     */
    private String updateTime;

    /**
     * 删除人
     */
    private String delter;

    /**
     * 删除时间
     */
    private String deleteTime;

    /**
     * 组织id
     */
    private String hospitalId;

    /**
     * 计量单位一名称
     */
    private String unitOneName;

    /**
     * 计量单位一与最小计量单位之间的转换系数
     */
    private BigDecimal unitOneCoefficient;

    /**
     * 计量单位二名称
     */
    private String unitTwoName;

    /**
     * 计量单位二与最小计量单位之间的转换系数
     */
    private BigDecimal unitTwoCoefficient;

    /**
     * 计量单位三
     */
    private String unitThrName;

    /**
     * 计量单位三与最小计量单位之间的转换系数
     */
    private BigDecimal unitThrCoefficient;

    /**
     * 计量单位四
     */
    private String unitFouName;

    /**
     * 计量单位四与最小计量单位之间的转换系数
     */
    private BigDecimal unitFouCoefficient;

    /**
     * 计量单位五
     */
    private String unitFivName;

    /**
     * 计量单位五与最小计量单位之间的转换系数
     */
    private BigDecimal unitFivCoefficient;

    /**
     * 计量单位六
     */
    private String unitSixName;

    /**
     * 计量单位六与最小计量单位之间的转换系数
     */
    private BigDecimal unitSixCoefficient;

    /**
     * 计量单位七
     */
    private String unitSevName;

    /**
     * 计量单位七与最小计量单位之间的转换系数
     */
    private BigDecimal unitSevCoefficient;

}

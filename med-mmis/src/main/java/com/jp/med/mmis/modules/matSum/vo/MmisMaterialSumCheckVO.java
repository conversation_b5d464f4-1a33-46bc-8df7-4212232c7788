package com.jp.med.mmis.modules.matSum.vo;

import lombok.Data;
import java.math.BigDecimal;

/**
 * 物资对账数据VO
 */
@Data
public class MmisMaterialSumCheckVO {
	private Long key;
	private String name;
	private BigDecimal actNum;
	private BigDecimal price;
	private BigDecimal amt;
	private BigDecimal oldAmt;
	private String easyCode;
	private String modspec;
	private String meterUnitName;
	private String meterCode;
	private String wrhsAddr;
	private String wrhsAddrName;
	private String asetType;
	private String asetTypeName;
	private String itemNum;
	private String matUniqueCode;
	private String hospitalId;

	// 理论库存数据
	private BigDecimal initialNum;
	private BigDecimal initialAmt;
	private BigDecimal storageNum;
	private BigDecimal storageAmt;
	private BigDecimal outboundNum;
	private BigDecimal outboundAmt;
	private BigDecimal targetNum;
	private BigDecimal targetAmt;

	// 差异数据
	private BigDecimal numDiff;
	private BigDecimal amtDiff;
}
package com.jp.med.mmis.modules.matApply.controller;

import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDto;
import com.jp.med.mmis.modules.matReceipt.dto.MmisAsetStorageDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDetailDto;
import com.jp.med.mmis.modules.matApply.service.read.MmisMaterialApplyDetailReadService;
import com.jp.med.mmis.modules.matApply.service.write.MmisMaterialApplyDetailWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;


/**
 * 物资申请明细
 *
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 16:59:25
 */
@Api(value = "物资申请明细", tags = "物资申请明细")
@RestController
@RequestMapping("mmisMaterialApplyDetail")
public class MmisMaterialApplyDetailController {

    @Autowired
    private MmisMaterialApplyDetailReadService mmisMaterialApplyDetailReadService;

    @Autowired
    private MmisMaterialApplyDetailWriteService mmisMaterialApplyDetailWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询物资申请明细")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody MmisMaterialApplyDetailDto dto) {
        return CommonResult.paging(mmisMaterialApplyDetailReadService.queryList(dto));
    }


    /**
     * 保存
     */
    @ApiOperation("新增物资申请明细")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody MmisMaterialApplyDetailDto dto) {
        mmisMaterialApplyDetailWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改物资申请明细")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody MmisMaterialApplyDetailDto dto) {
        mmisMaterialApplyDetailWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除物资申请明细")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody MmisMaterialApplyDetailDto dto) {
        mmisMaterialApplyDetailWriteService.removeById(dto);
        return CommonResult.success();
    }

    @ApiOperation("查询申请详情")
    @PostMapping("/detailsList")
    public CommonResult<?> reqDetailList(@RequestBody MmisMaterialApplyDto dto) {
        return CommonResult.success(mmisMaterialApplyDetailReadService.reqDetailList(dto));
    }

    /**搞了一个一样的查询，因为要一个key字段返回，已经有一个integer类型的了,这个需要另外一个返回体
     *
     * @param dto
     * @return
     */
    @ApiOperation("查询申请详情")
    @PostMapping("/detailsListNewKey")
    public CommonResult<?> detailsListNewKey(@RequestBody MmisMaterialApplyDto dto) {
        return CommonResult.success(mmisMaterialApplyDetailReadService.detailsListNewKey(dto));
    }

    /**
     * 根据单个申请详情，去查找能够符合情况能够出库的物资
     * @param dto
     * @return
     */
    @ApiOperation("查询申请详情")
    @PostMapping("/queryCanUseMatByDetail")
    public CommonResult<?> queryCanUseMatByDetail(@RequestBody MmisMaterialApplyDto dto) {
        return CommonResult.success(mmisMaterialApplyDetailReadService.queryCanUseMatByDetail(dto));
    }

    @ApiOperation("查询申请详情")
    @PostMapping("/autoDetailsList")
    public CommonResult<?> autoDetailsList(@RequestBody MmisMaterialApplyDto dto) {
        return CommonResult.success(mmisMaterialApplyDetailReadService.autoDetailsList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询待收货确认的物资详情")
    @PostMapping("/waitComfirmList")
    public CommonResult<?> waitComfirmList(@RequestBody MmisMaterialApplyDto dto) {
        return CommonResult.paging(mmisMaterialApplyDetailReadService.waitComfirmList(dto));
    }
}

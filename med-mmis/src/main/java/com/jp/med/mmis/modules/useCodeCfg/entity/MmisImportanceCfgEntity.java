package com.jp.med.mmis.modules.useCodeCfg.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 重要程度配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-24 17:26:53
 */
@Data
@TableName("mmis_importance_cfg")
public class MmisImportanceCfgEntity {

	/** id
 */
	@TableId("id")
	private Integer id;

	/** 类型代码 */
	@TableField("imp_code")
	private String impCode;

	/** 重要程度 */
	@TableField("imp_name")
	private String impName;

	/** 逻辑状态(0未删除，1已删除) */
	@TableField("active_flag")
	private String activeFlag;

	/** 创建人 */
	@TableField("cter")
	private String cter;

	/** 创建人 */
	@TableField("create_time")
	private String createTime;

	/** 更新人 */
	@TableField("updtr")
	private String updtr;

	/** 更新时间 */
	@TableField("update_time")
	private String updateTime;

	/** 删除人 */
	@TableField("delter")
	private String delter;

	/** 删除时间 */
	@TableField("delete_time")
	private String deleteTime;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

}

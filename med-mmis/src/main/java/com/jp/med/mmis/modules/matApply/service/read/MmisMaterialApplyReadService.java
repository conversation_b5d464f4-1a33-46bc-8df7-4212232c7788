package com.jp.med.mmis.modules.matApply.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.mmis.modules.matApply.dto.MmisMaterialApplyDto;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyDetailVo;
import com.jp.med.mmis.modules.matApply.vo.MmisMaterialApplyVo;
import com.jp.med.mmis.modules.matReceipt.vo.MmisAsetStorageVo;

import java.util.List;

/**
 * 物资申请
 *
 * <AUTHOR>
 * @email -
 * @date 2024-03-06 16:59:24
 */
public interface MmisMaterialApplyReadService extends IService<MmisMaterialApplyDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<MmisMaterialApplyVo> queryList(MmisMaterialApplyDto dto);

    MmisMaterialApplyVo queryAppAuditDetail(MmisMaterialApplyDto dto);

    List<MmisMaterialApplyVo> waitOutApplyList(MmisMaterialApplyDto dto);

	List<MmisMaterialApplyDetailVo> queryApplyByItemNum(MmisMaterialApplyDto dto);
}


package com.jp.med.mmis.modules.outBound.dto;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.math.BigDecimal;
import java.util.List;

import com.jp.med.common.entity.audit.AuditDetail;
import lombok.Data;

/**
 * 出库申请详情
 * <AUTHOR>
 * @email -
 * @date 2024-03-14 17:18:10
 */
@Data
@TableName("mmis_outbound_apply_details" )
public class MmisOutboundApplyDetailsDto extends CommonQueryDto {

    /** id
 */
    @TableId("id")
    private Integer id;

    /** 申请id */
    @TableField("apply_id")
    private Integer applyId;

    /** 货号(入库商品的) */
    @TableField("item_num")
    private String itemNum;

    @TableField(exist = false)
    private String key;

    /** 品名 */
    @TableField("name")
    private String name;

    /** 规格 */
    @TableField("modspec")
    private String modspec;

    /** 库位代码(选择仓库对应的仓库代码) */
    @TableField("wrhs_addr")
    private String wrhsAddr;

    /** 计量方式 */
    @TableField("meter_code")
    private String meterCode;

    /** 每件细数(一件一件的) */
    @TableField("item_count")
    private BigDecimal itemCount;

    /**
     * 数量
     */
    @TableField("num")
    private BigDecimal num;

    /**
     * his系统数量
     */
    @TableField("his_num")
    private BigDecimal hisNum;

    /** 单价 */
    @TableField("price")
    private BigDecimal price;

    /** 金额 */
    @TableField("amt")
    private BigDecimal amt;

    /** 生产日期 */
    @TableField("mfg_date")
    private String mfgDate;

    /** 到期日 */
    @TableField("exprin_date")
    private String exprinDate;

    /** 物资备注 */
    @TableField("remark")
    private String remark;

    /** 物资品牌 */
    @TableField("aset_brad")
    private String asetBrad;

    /** 供应商 */
    @TableField("supplier_name")
    private String supplierName;

    /** 创建人 */
    @TableField("crter")
    private String crter;

    /** 生产日期 */
    @TableField("create_time")
    private String createTime;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 逻辑删除状态 */
    @TableField("is_deleted")
    private Integer isDeleted;

    @TableField("storage_bchno")
    private String storageBchno;

    /**
     * 出库的实际选择物资，根据存看库存日期，价格，然后再是库存进行比较进行选择的
     */
    @TableField("mat_unique_code")
    private String matUniqueCode;
    /**
     * 物资申请明细
     */
    @TableField(exist = false)
    private List<MmisOutboundApplyDetailsDto> mmisOutboundApplyDetailsDtos;

    /**
     * 审核页面
     */
    @TableField(exist = false)
    private String audit;

    /** 审核流程详情 */
    @TableField(exist = false)
    private List<AuditDetail> auditDetails;
}

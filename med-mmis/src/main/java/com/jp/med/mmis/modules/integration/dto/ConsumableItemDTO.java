package com.jp.med.mmis.modules.integration.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 耗材项明细DTO
 */
@Data
@ApiModel(description = "耗材项明细DTO")
public class ConsumableItemDTO {

	@ApiModelProperty(value = "耗材ID")
	private String tripartiteConsumableId;

	@ApiModelProperty(value = "耗材编码")
	private String code;

	@ApiModelProperty(value = "耗材名称")
	private String name;

	@ApiModelProperty(value = "计量单位")
	private String unit;

	@ApiModelProperty(value = "单价")
	private BigDecimal cost;

	@ApiModelProperty(value = "规格")
	private String specification;

	@ApiModelProperty(value = "数量")
	private BigDecimal num;

	@ApiModelProperty(value = "总价")
	private BigDecimal totalPrices;

	@ApiModelProperty(value = "业务部门ID")
	private String business_dept;

	@ApiModelProperty(value = "使用科室ID")
	private String use_dept;

	@ApiModelProperty(value = "院区ID")
	private String campus_id;

	@ApiModelProperty(value = "创建时间")
	private String create_time;

	@ApiModelProperty(value = "申请人ID")
	private String applicant;

	@ApiModelProperty(value = "耗材分类ID")
	private String tripartiteConsumableTypeId;

	@ApiModelProperty(value = "耗材分类编码")
	private String consumableTypeCode;

	@ApiModelProperty(value = "耗材分类名称")
	private String consumableTypeName;
}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.rms.modules.projectInfo.mapper.write.RmsResearchersWriteMapper">

    <delete id="deleteByProjectId" parameterType="integer">
        delete
        from rms_researchers
        where project_id = #{projectId,jdbcType=INTEGER}
          AND EXISTS (SELECT 1 FROM rms_researchers WHERE project_id = #{projectId,jdbcType=INTEGER});
    </delete>
</mapper>

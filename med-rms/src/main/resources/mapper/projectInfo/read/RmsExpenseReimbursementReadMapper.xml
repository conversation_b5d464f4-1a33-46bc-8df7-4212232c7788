<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.rms.modules.projectInfo.mapper.read.RmsExpenseReimbursementReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.rms.modules.projectInfo.vo.RmsExpenseReimbursementVo" id="expenseReimbursementMap">
        <id property="id" column="id"/>
        <result property="projectId" column="project_id"/>
        <result property="expenseType" column="expense_type"/>
        <result property="expenseName" column="expense_name"/>
        <result property="expenseDate" column="expense_date"/>
        <result property="amount" column="amount"/>
        <result property="attachments" column="attachments" jdbcType="OTHER" typeHandler="com.jp.med.rms.typeHandler.JsonbArrayTypeHandler"/>
        <result property="remark" column="remark"/>
        <result property="reimbursementStatus" column="reimbursement_status"/>
        <result property="reimbursementTime" column="reimbursement_time"/>
        <result property="taskId" column="task_id"/>
        <result property="reimbursementId" column="reimbursement_id"/>
        <result property="deleted" column="deleted"/>
        <result property="creator" column="creator"/>
        <result property="createTime" column="create_time"/>
        <result property="updater" column="updater"/>
        <result property="updateTime" column="update_time"/>
        <result property="hospitalId" column="hospitalId"/>
    </resultMap>

    <resultMap id="EcsReimbursementDetailVo" type="com.jp.med.rms.modules.projectInfo.vo.EcsReimbursementDetailVo">
        <id property="id" column="id"/>
        <result property="applicant" column="appyer" />
        <result property="applicantName" column="appyer_name" />
        <result property="applicantDept" column="appyer_dept" />
        <result property="applicantDeptName" column="appyer_dept_name" />
        <result property="applicationTime" column="appyer_time" />
        <result property="fundingId" column="funding_id" />
        <result property="reimbursementAmount" column="sum" />
        <result property="reimbursementAmountUpperCase" column="cap_sum" />
        <result property="reimbursementStatus" column="busstas" />
        <result property="bank" column="bank" />
        <result property="accountName" column="acctname" />
        <result property="accountNumber" column="bankcode" />
        <result property="hospitalId" column="hospitalId"/>
    </resultMap>

    <select id="queryList" resultType="com.jp.med.rms.modules.projectInfo.vo.RmsExpenseReimbursementVo">
        select
            rer.id,
            rer.project_id,
            rer.expense_type,
            rer.expense_name,
            rer.expense_date,
            rer.amount,
            rer.attachments,
            rer.remark,
            case when erd.id is null then rer.reimbursement_status else erd.busstas::int2 end as reimbursement_status,
            rer.reimbursement_time,
            rer.task_id,
            rer.reimbursement_id,
            rer.deleted,
            rer.creator,
            rer.create_time,
            rer.updater,
            rer.update_time,
            rer.hospital_id as hospitalId
        from rms_expense_reimbursement rer
        left join ecs_reim_detail erd on erd.id = rer.reimbursement_id and erd.hospital_id = rer.hospital_id
        where rer.deleted = false
        <if test="projectId != null">
            and rer.project_id = #{projectId}
        </if>
        <if test="expenseType != null">
            and rer.expense_type = #{expenseType}
        </if>
        <if test="expenseName != null and expenseName != ''">
            and rer.expense_name like concat('%',#{expenseName},'%')
        </if>
        <if test="reimbursementStatus != null">
            and case when erd.id is null then rer.reimbursement_status else erd.busstas::int2 end = #{reimbursementStatus}
        </if>
        order by rer.id desc
    </select>

    <select id="queryEcsReimbursementDetailList" resultMap="EcsReimbursementDetailVo">
        select
            erd.id,
            erd.appyer,
            hei.emp_name as appyer_name,
            erd.appyer_dept,
            ho.org_name as appyer_dept_name,
            erd.appyer_time,
            erd.funding_id,
            erd.sum,
            erd.cap_sum,
            CAST(busstas AS INTEGER) AS busstas,
            erd.bank,
            erd.acctname,
            erd.bankcode,
            erd.hospital_id as hospitalId
        from ecs_reim_detail erd
        left join hrm_org ho
        on erd.appyer_dept = ho.org_id
        left join hrm_employee_info hei
        on erd.appyer = hei.emp_code
        <where>
            <if test="projectId != null">
                erd.project_id = #{projectId,jdbcType=INTEGER}
            </if>
            <if test="reimbursementStatus != null">
                erd.busstas = #{reimbursementStatus.code,jdbcType=INTEGER}
            </if>
        </where>
    </select>

</mapper>

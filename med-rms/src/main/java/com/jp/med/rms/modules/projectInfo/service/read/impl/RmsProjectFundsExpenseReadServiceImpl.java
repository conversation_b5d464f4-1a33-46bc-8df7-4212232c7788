package com.jp.med.rms.modules.projectInfo.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.rms.modules.projectInfo.mapper.read.RmsProjectFundsExpenseReadMapper;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectFundsExpenseDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsProjectFundsExpenseVo;
import com.jp.med.rms.modules.projectInfo.service.read.RmsProjectFundsExpenseReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class RmsProjectFundsExpenseReadServiceImpl extends ServiceImpl<RmsProjectFundsExpenseReadMapper, RmsProjectFundsExpenseDto> implements RmsProjectFundsExpenseReadService {

    @Autowired
    private RmsProjectFundsExpenseReadMapper rmsProjectFundsExpenseReadMapper;

    @Override
    public List<RmsProjectFundsExpenseVo> queryList(RmsProjectFundsExpenseDto dto) {
        return rmsProjectFundsExpenseReadMapper.queryList(dto);
    }

    @Override
    public List<RmsProjectFundsExpenseVo> queryPageList(RmsProjectFundsExpenseDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return rmsProjectFundsExpenseReadMapper.queryList(dto);
    }

}

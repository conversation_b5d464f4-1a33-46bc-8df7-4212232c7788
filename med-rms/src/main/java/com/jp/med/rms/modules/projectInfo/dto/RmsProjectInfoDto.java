package com.jp.med.rms.modules.projectInfo.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.rms.modules.projectApplication.entity.RmsProjectInte;
import lombok.*;

import java.math.BigDecimal;

/**
 * 项目详情信息表
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:18
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("rms_project_info" )
public class RmsProjectInfoDto extends CommonQueryDto {

    /** id */
    @TableId("id")
    private Integer id;

    /** 项目id */
    @TableField("project_id")
    private Integer projectId;

    /** 项目名称 */
    @TableField("project_name")
    private String projectName;

    /** 项目开始日期 */
    @TableField("project_start_date")
    private String projectStartDate;

    /** 项目结束日期 */
    @TableField("project_end_date")
    private String projectEndDate;

    /** 创新类型 */
    @TableField("innovation_type")
    private String innovationType;

    /** 产学研联合  */
    @TableField("industry_academia_collab")
    private String industryAcademiaCollab;

    /** 知识产权状况 */
    @TableField("ip_status")
    private String ipStatus;

    /** 成果水平 */
    @TableField("result_level")
    private String resultLevel;

    /** 成果形式 */
    @TableField(value = "result_format", typeHandler = org.apache.ibatis.type.ArrayTypeHandler.class)
    private String[] resultFormat;

    /**
     * 知识产权（发明专利项数、实用型新型专利项数、其他专利项数）
     */
    @TableField(exist = false)
    private RmsProjectInte intellectualProperty;

    /** 发明专利项数 */
    @TableField("invent_patent_cnt")
    private Integer inventPatentCnt;

    /** 实用新型专利项数 */
    @TableField("util_patent_cnt")
    private Integer utilPatentCnt;

    /** 其他专利项数 */
    @TableField("other_patent_num")
    private Integer otherPatentNum;

    /** 技术标准制定 */
    @TableField("tech_std_dev")
    private String techStdDev;

    /** 经费预算 */
    @TableField("budget")
    private BigDecimal budget;

    /** 项目概述 */
    @TableField("project_descr")
    private String projectDescr;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

    /** 更新人  */
    @TableField("updtr")
    private String updtr;

    /** 更新时间 */
    @TableField("update_time")
    private String updateTime;

}

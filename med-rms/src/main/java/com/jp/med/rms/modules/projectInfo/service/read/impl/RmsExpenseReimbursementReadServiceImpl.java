package com.jp.med.rms.modules.projectInfo.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.rms.modules.projectInfo.dto.EcsReimbursementDetailQueryDto;
import com.jp.med.rms.modules.projectInfo.dto.RmsExpenseReimbursementDto;
import com.jp.med.rms.modules.projectInfo.mapper.read.RmsExpenseReimbursementReadMapper;
import com.jp.med.rms.modules.projectInfo.service.read.RmsExpenseReimbursementReadService;
import com.jp.med.rms.modules.projectInfo.vo.EcsReimbursementDetailVo;
import com.jp.med.rms.modules.projectInfo.vo.RmsExpenseReimbursementVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class RmsExpenseReimbursementReadServiceImpl extends ServiceImpl<RmsExpenseReimbursementReadMapper, RmsExpenseReimbursementDto> implements RmsExpenseReimbursementReadService {

    @Autowired
    private RmsExpenseReimbursementReadMapper rmsExpenseReimbursementReadMapper;

    @Override
    public List<RmsExpenseReimbursementVo> queryList(RmsExpenseReimbursementDto dto) {
        return rmsExpenseReimbursementReadMapper.queryList(dto);
    }

    @Override
    public List<RmsExpenseReimbursementVo> queryPageList(RmsExpenseReimbursementDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return rmsExpenseReimbursementReadMapper.queryList(dto);
    }

    @Override
    public List<EcsReimbursementDetailVo> queryEcsReimbursementDetailPageList(EcsReimbursementDetailQueryDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return super.baseMapper.queryEcsReimbursementDetailList(dto);
    }

}

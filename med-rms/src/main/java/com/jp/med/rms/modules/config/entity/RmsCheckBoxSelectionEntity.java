package com.jp.med.rms.modules.config.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 科研系统勾选框
 * <AUTHOR>
 * @email -
 * @date 2024-09-11 11:45:42
 */
@Data
@TableName("rms_check_box_selection")
public class RmsCheckBoxSelectionEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 辅助项名称 */
	@TableField("selected_label")
	private String selectedLabel;


	/** 辅助项值 */
	@TableField("selected_value")
	private String selectedValue;

	/** 辅助项类型 */
	@TableField("dict_type")
	private String dictType;

	/** 备注 */
	@TableField("remark")
	private String remark;

	/** 创建人 */
	@TableField("crter")
	private String crter;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 逻辑删除 */
	@TableField("is_deleted")
	@TableLogic(value = "0", delval = "1")
	private Integer isDeleted;

	/** 排序 */
	@TableField("seq")
	private Integer seq;



}

package com.jp.med.rms.modules.projectApplication.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.rms.modules.projectApplication.mapper.read.RmsFileUploadRecordReadMapper;
import com.jp.med.rms.modules.projectApplication.dto.RmsFileUploadRecordDto;
import com.jp.med.rms.modules.projectApplication.vo.RmsFileUploadRecordVo;
import com.jp.med.rms.modules.projectApplication.service.read.RmsFileUploadRecordReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class RmsFileUploadRecordReadServiceImpl extends ServiceImpl<RmsFileUploadRecordReadMapper, RmsFileUploadRecordDto> implements RmsFileUploadRecordReadService {

    @Autowired
    private RmsFileUploadRecordReadMapper rmsFileUploadRecordReadMapper;

    @Override
    public List<RmsFileUploadRecordVo> queryList(RmsFileUploadRecordDto dto) {
        return rmsFileUploadRecordReadMapper.queryList(dto);
    }

    @Override
    public List<RmsFileUploadRecordVo> queryPageList(RmsFileUploadRecordDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return rmsFileUploadRecordReadMapper.queryList(dto);
    }

}

package com.jp.med.rms.modules.rabbitMQ;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.ienum.BpmTaskStatusEnum;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.rms.modules.projectApplication.dto.RmsProjectMainInfoDto;
import com.jp.med.rms.modules.projectApplication.service.write.RmsProjectMainInfoWriteService;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date: 2024/11/7
 * @time: 15:32
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RmsProjectDeclarationMessageHandler extends AbstractBpmApproveMessageHandle {
    private final RmsProjectMainInfoWriteService projectMainInfoWriteService;

    @Override
    public String[] getProcessIdentifier() {
        return new String[] {"RMS_PROJECT_DECLARATION"};
    }

    @RabbitListener(queues = {"RMS_PROJECT_DECLARATION"})
    @Override
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        super.receiveMessage0(msg);
    }

    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {
        log.debug("handleCreate ==> {}", message);
    }

    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {
        log.debug("handleApproved ==> {}", message);
    }

    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        switch (message.getTaskId()) {
//            院内拒绝
            case "rms_prj_declare_hospital":
                projectMainInfoWriteService.updateById(
                        RmsProjectMainInfoDto.builder()
                                .id(Integer.valueOf(message.getBusinessKey()))
                                .applyStatus(MedConst.TYPE_3)
                                .chkState(MedConst.TYPE_2)
                                .build()
                );
                break;
//                省内拒绝
            case "rms_prj_declare_province":
                projectMainInfoWriteService.updateById(
                        RmsProjectMainInfoDto.builder()
                                .id(Integer.valueOf(message.getBusinessKey()))
                                .applyStatus(MedConst.TYPE_5)
                                .chkState(MedConst.TYPE_1)
                                .build()
                );
                break;
        }
    }

    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {
        switch(message.getTaskId()) {
            case "rms_prj_declare_hospital":
//                院内审核通过
                if (message.getVariables().get("TASK_STATUS").equals(BpmTaskStatusEnum.APPROVE.getStatus().toString())) {
                    projectMainInfoWriteService.updateById(
                            RmsProjectMainInfoDto.builder()
                                    .id(Integer.valueOf(message.getBusinessKey()))
                                    .applyStatus(MedConst.TYPE_2)
                                    .chkState(MedConst.TYPE_1)
                                    .build()
                    );
                }
                break;
            case "rms_prj_declare_province":
//                省内审核通过
                if (message.getVariables().get("TASK_STATUS").equals(BpmTaskStatusEnum.APPROVE.getStatus().toString())) {
                    projectMainInfoWriteService.updateById(
                            RmsProjectMainInfoDto.builder()
                                    .id(Integer.valueOf(message.getBusinessKey()))
                                    .applyStatus(MedConst.TYPE_4)
                                    .chkState(MedConst.TYPE_1)
                                    .build()
                    );
                }
                break;
        }
    }

    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {
        log.debug("handleCancelled ==> {}", message);
    }
}

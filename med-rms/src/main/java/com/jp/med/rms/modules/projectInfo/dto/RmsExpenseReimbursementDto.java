package com.jp.med.rms.modules.projectInfo.dto;

import com.baomidou.mybatisplus.annotation.*;
import com.jp.med.common.dto.common.CommonQueryDto;
import com.jp.med.rms.typeHandler.JsonbArrayTypeHandler;
import lombok.*;
import org.apache.ibatis.type.JdbcType;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 科研经费报销记录表
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2024-12-06 11:23:32
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@EqualsAndHashCode(callSuper = true)
@Data
@TableName("rms_expense_reimbursement" )
public class RmsExpenseReimbursementDto extends CommonQueryDto {

    /** 报销申请记录ID */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /** 项目ID */
    @TableField("project_id")
    private Integer projectId;

    /** 费用类型 */
    @TableField("expense_type")
    private String expenseType;

    /** 费用名称 */
    @TableField("expense_name")
    private String expenseName;

    /** 费用花费日期 */
    @TableField("expense_date")
    private LocalDate expenseDate;

    /** 报销金额 */
    @TableField("amount")
    private BigDecimal amount;

    /** 附件 */
    @TableField(value = "attachments", jdbcType = JdbcType.OTHER, typeHandler = JsonbArrayTypeHandler.class)
    private List<Map<String, Object>> attachments;

    /** 报销备注 */
    @TableField("remark")
    private String remark;

    /** 报销状态 #1已付款 2未提交 3审核中 4被驳回 */
    @TableField("reimbursement_status")
    private Integer reimbursementStatus;

    /** 报销时间 */
    @TableField("reimbursement_time")
    private LocalDateTime reimbursementTime;

    /** 报销任务ID */
    @TableField("task_id")
    private Integer taskId;

    /** 报销ID */
    @TableField("reimbursement_id")
    private Integer reimbursementId;

    /** 是否删除 */
    @TableField("deleted")
    @TableLogic(value = "false", delval = "true")
    private Boolean deleted;

    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 更新人 */
    @TableField("updater")
    private String updater;

    /** 更新时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 组织id */
    @TableField("hospital_id")
    private String hospitalId;

}

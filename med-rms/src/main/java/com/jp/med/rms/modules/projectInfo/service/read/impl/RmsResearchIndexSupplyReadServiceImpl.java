package com.jp.med.rms.modules.projectInfo.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.rms.modules.projectInfo.mapper.read.RmsResearchIndexSupplyReadMapper;
import com.jp.med.rms.modules.projectInfo.dto.RmsResearchIndexSupplyDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsResearchIndexSupplyVo;
import com.jp.med.rms.modules.projectInfo.service.read.RmsResearchIndexSupplyReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class RmsResearchIndexSupplyReadServiceImpl extends ServiceImpl<RmsResearchIndexSupplyReadMapper, RmsResearchIndexSupplyDto> implements RmsResearchIndexSupplyReadService {

    @Autowired
    private RmsResearchIndexSupplyReadMapper rmsResearchIndexSupplyReadMapper;

    @Override
    public List<RmsResearchIndexSupplyVo> queryList(RmsResearchIndexSupplyDto dto) {
        return rmsResearchIndexSupplyReadMapper.queryList(dto);
    }

    @Override
    public List<RmsResearchIndexSupplyVo> queryPageList(RmsResearchIndexSupplyDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return rmsResearchIndexSupplyReadMapper.queryList(dto);
    }

}

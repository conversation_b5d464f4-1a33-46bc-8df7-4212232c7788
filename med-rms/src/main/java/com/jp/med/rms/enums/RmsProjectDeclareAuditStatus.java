package com.jp.med.rms.enums;

import lombok.Getter;

/**
 * Created by IntelliJ IDEA.
 * 审核状态枚举
 * <AUTHOR>
 * @date: 2024/11/19
 * @time: 14:19
 */
@Getter
public enum RmsProjectDeclareAuditStatus {
    // 筹备申报提交
    PRE_DECLARANT(RmsProcessTask.PRE_DECLARE_START, RmsProjectDeclareAuditStatus.PASSED),
    // 筹备申报科教科选择评审专家通过
    PRE_SES_SELECT_PASSED(RmsProcessTask.PRE_DECLARE_SES_SELECT, RmsProjectDeclareAuditStatus.PASSED),
    // 筹备申报科教科选择评审专家驳回
    PRE_SES_SELECT_REJECTED(RmsProcessTask.PRE_DECLARE_SES_SELECT, RmsProjectDeclareAuditStatus.REJECTED),
    // 筹备申报评审专家通过
    PRE_REVIEWERS_PASSED(RmsProcessTask.PRE_DECLARE_REVIEWERS, RmsProjectDeclareAuditStatus.PASSED),
    // 筹备申报评审专家驳回
    PRE_REVIEWERS_REJECTED(RmsProcessTask.PRE_DECLARE_REVIEWERS, RmsProjectDeclareAuditStatus.REJECTED),
    // 筹备申报科教科审核通过
    PRE_SES_AUDIT_PASSED(RmsProcessTask.PRE_DECLARE_SES_AUDIT, RmsProjectDeclareAuditStatus.PASSED),
    // 筹备申报科教科审核驳回
    PRE_SES_AUDIT_REJECTED(RmsProcessTask.PRE_DECLARE_SES_AUDIT, RmsProjectDeclareAuditStatus.REJECTED),
    // 立项申报提交
    PRJ_DECLARANT_PASSED(RmsProcessTask.PROJECT_DECLARE_START, RmsProjectDeclareAuditStatus.PASSED),
    // 立项申报科教科选择评审专家通过
    PRJ_SES_SELECT_PASSED(RmsProcessTask.PROJECT_DECLARE_SES_SELECT, RmsProjectDeclareAuditStatus.PASSED),
    // 立项申报科教科选择评审专家驳回
    PRJ_SES_SELECT_REJECTED(RmsProcessTask.PROJECT_DECLARE_SES_SELECT, RmsProjectDeclareAuditStatus.REJECTED),
    // 立项申报评审专家通过
    PRJ_REVIEWERS_PASSED(RmsProcessTask.PROJECT_DECLARE_REVIEWERS, RmsProjectDeclareAuditStatus.PASSED),
    // 立项申报评审专家驳回
    PRJ_REVIEWERS_REJECTED(RmsProcessTask.PROJECT_DECLARE_REVIEWERS, RmsProjectDeclareAuditStatus.REJECTED),
    // 立项申报科教科审核通过
    PRJ_SES_AUDIT_PASSED(RmsProcessTask.PROJECT_DECLARE_SES_AUDIT, RmsProjectDeclareAuditStatus.PASSED),
    // 立项申报科教科审核驳回
    PRJ_SES_AUDIT_REJECTED(RmsProcessTask.PROJECT_DECLARE_SES_AUDIT, RmsProjectDeclareAuditStatus.REJECTED),
    // 立项申报意向单位审核通过
    PRJ_INTENDED_AUDIT_PASSED(RmsProcessTask.PROJECT_DECLARE_INTENDED_UNIT_AUDIT, RmsProjectDeclareAuditStatus.PASSED),
    // 立项申报意向单位审核驳回
    PRJ_INTENDED_AUDIT_REJECTED(RmsProcessTask.PROJECT_DECLARE_INTENDED_UNIT_AUDIT, RmsProjectDeclareAuditStatus.REJECTED);

    private final RmsProcessTask task;

    private final String checkStatus;

    RmsProjectDeclareAuditStatus(RmsProcessTask task, String checkStatus) {
        this.task = task;
        this.checkStatus = checkStatus;
    }

    public static final String PASSED = "1";

    public static final String REJECTED = "2";

    public static final String PENDING = "3";
}

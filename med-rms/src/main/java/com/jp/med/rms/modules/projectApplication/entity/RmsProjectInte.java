package com.jp.med.rms.modules.projectApplication.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 项目概览的知识产权一栏，为什么要单独列？
 * 谁tm知道这一栏要搞不搞骚操作呢
 * 比如拿去搞什么宣传，吹吹牛啥的，这里用来好区分的
 */
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class RmsProjectInte {
	/**
	 * 发明专利项
	 */
	private Integer inventPatentCnt;
	/**
	 * 实用新专利项
	 */
	private Integer utilPatentCnt;
	/**
	 * 其他
	 */
	private Integer otherPatentNum;
}

package com.jp.med.rms.modules.projectInfo.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectRichTextDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsProjectRichTextVo;

import java.util.List;

/**
 * 项目富文本编辑
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:03
 */
public interface RmsProjectRichTextReadService extends IService<RmsProjectRichTextDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsProjectRichTextVo> queryList(RmsProjectRichTextDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<RmsProjectRichTextVo> queryPageList(RmsProjectRichTextDto dto);
}


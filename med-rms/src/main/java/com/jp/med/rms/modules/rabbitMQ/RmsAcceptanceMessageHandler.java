package com.jp.med.rms.modules.rabbitMQ;

import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.rms.enums.RmsBaseAuditStatus;
import com.jp.med.rms.enums.RmsProcessModel;
import com.jp.med.rms.modules.projectApplication.dto.RmsProjectMainInfoDto;
import com.jp.med.rms.modules.projectApplication.service.write.RmsProjectMainInfoWriteService;
import com.rabbitmq.client.Channel;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;

/**
 * Created by IntelliJ IDEA.
 *
 * <AUTHOR>
 * @date: 2024/12/20
 * @time: 11:41
 */
@Slf4j
@RequiredArgsConstructor
@Component
public class RmsAcceptanceMessageHandler extends AbstractBpmApproveMessageHandle {
    private final RmsProjectMainInfoWriteService projectMainInfoWriteService;

    @Override
    public String[] getProcessIdentifier() {
        return new String[] {RmsProcessModel.PROJECT_ACCEPTANCE.getProcessId()};
    }

    @RabbitListener(queues = "RMS_PRJ_ACCEPTANCE_DECLARE")
    @Override
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        super.receiveMessage0(msg);
    }

    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {

    }

    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {
        projectMainInfoWriteService.updateById(
                RmsProjectMainInfoDto.builder()
                        .id(Integer.valueOf(message.getBusinessKey()))
                        .acceptanceAuditStatus(RmsBaseAuditStatus.APPROVED)
                        .acceptanceAuditTime(LocalDateTime.now())
                        .build()
        );
    }

    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        projectMainInfoWriteService.updateById(
                RmsProjectMainInfoDto.builder()
                        .id(Integer.valueOf(message.getBusinessKey()))
                        .acceptanceAuditStatus(RmsBaseAuditStatus.REJECTED)
                        .acceptanceAuditTime(LocalDateTime.now())
                        .build()
        );
    }

    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {

    }

    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {
        projectMainInfoWriteService.updateById(
                RmsProjectMainInfoDto.builder()
                        .id(Integer.valueOf(message.getBusinessKey()))
                        .acceptanceAuditStatus(RmsBaseAuditStatus.CANCELLED)
                        .acceptanceAuditTime(LocalDateTime.now())
                        .build()
        );
    }
}

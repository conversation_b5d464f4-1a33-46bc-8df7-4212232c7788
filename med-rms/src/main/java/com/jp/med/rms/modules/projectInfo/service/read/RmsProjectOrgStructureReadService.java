package com.jp.med.rms.modules.projectInfo.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.rms.modules.projectInfo.dto.RmsProjectOrgStructureDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsProjectOrgStructureVo;

import java.util.List;

/**
 * 项目组织结构
 * <AUTHOR>
 * @email -
 * @date 2024-09-14 11:40:12
 */
public interface RmsProjectOrgStructureReadService extends IService<RmsProjectOrgStructureDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsProjectOrgStructureVo> queryList(RmsProjectOrgStructureDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<RmsProjectOrgStructureVo> queryPageList(RmsProjectOrgStructureDto dto);
}


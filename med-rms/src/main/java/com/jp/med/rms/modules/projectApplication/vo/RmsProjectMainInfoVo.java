package com.jp.med.rms.modules.projectApplication.vo;

import cn.hutool.core.lang.tree.Tree;
import com.fasterxml.jackson.databind.JsonNode;
import com.jp.med.rms.enums.RmsBaseAuditStatus;
import com.jp.med.rms.modules.common.vo.RmsAuditRcdfmVo;
import com.jp.med.rms.modules.projectInfo.vo.*;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 项目信息
 * <AUTHOR>
 * @email -
 * @date 2024-09-10 16:37:08
 */
@Data
public class RmsProjectMainInfoVo {

	/** id */
	private Integer id;

	/**
	 * 项目编号
	 */
	private String projectCode;

	/** 项目名称 */
	private String projectName;

	/** 项目负责人 */
	private String projectLeader;

	/**
	 * 项目负责人用户名
	 */
	private String projectLeaderUsername;

	/** 联系电话 */
	private String telephone;

	/** 申报级别 */
	private String projectLevel;

	/** 课题类别 */
	private String topicCategory;

	/** 研究类型 */
	private String researchType;

	/** 与外单位合作 */
	private String externalCooperation;

	/** 项目起始日期 */
	private String projectStartDate;

	/** 项目结束日期 */
	private String projectEndDate;

	/** 报送日期 */
	private String submitDate;

	/** 申报状态 */
	private String applyStatus;

	/** 申请人 */
	private String appyer;

	/**
	 * 是否在审核名单标志位
	 */
	private String auditFlag;


	/**
	 * 申请科室
	 */
	private String apperyOrg;


	/** 科室id */
	private String appyOrgId;

	/** 申请人电话 */
	private String appyerPhone;

	/** 审核状态 */
	private String chkState;

	/** 审核批次号 */
	private String auditBchno;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 操作人 */
	private String updtr;

	/** 操作时间 */
	private String updateTime;

	/** 组织id */
	private String hospitalId;

	/**
	 * 立项拒绝原因
	 */
	private String prjRejectReason;

	/**
	 * 经费报销标记 0未生成计划，1：已生成计划待申请，2：已申请待报销，3：已完成报销
	 */
	private String reimFlag;

	/**
	 * 筹备流程实例编码
	 */
	private String preProcessInstanceCode;

	/**
	 * 立项流程实例编码
	 */
	private String prjProcessInstanceCode;

	/**
	 * 立项申报意向单位ID
	 */
	private Integer intendedUnitId;

	/**
	 * 立项申报意向单位 #其他
	 */
	private String intendedUnitOther;

	/**
	 * 立项申报意向单位科研专项id
	 */
	private Integer intendedUnitSpecialId;

	/**
	 * 立项申报意向单位科研专项 #其他
	 */
	private String intendedUnitSpecialOther;

	/**
	 * 六、已有研究基础、承担优势和项目实施的风险及应对策略的相关数据或成果佐证资料
	 * json字符串数组：json中包含ID, 源文件名、文件大小、文件类型、文件路径
	 */
	private List<Map<String, Object>> swotAttachments;

	/**
	 * 课题组成员签字附件
	 */
	private List<Map<String, Object>> researchersSignAttachments;

	/**
	 * 立项附件 #立项确认时上传
	 */
	private List<Map<String, Object>> prjAttachments;

	/**
	 * 是否立项通过
	 */
	private Boolean prjApproved;

	/**
	 * 立项通过时间
	 */
	private LocalDateTime prjApprovedTime;

	/**
	 * 结题总结
	 */
	private String conclusionSummary;

	/**
	 * 结题附件材料
	 */
	private List<Map<String, Object>> conclusionAttachments;

	/**
	 * 结题其他附件
	 */
	private JsonNode conclusionOtherAttachments;

	/**
	 * 结题提交时间
	 */
	private LocalDateTime conclusionSubmitTime;


	/**
	 * 结题状态 #0未提交 1已通过 2被驳回 3待审核
	 */
	private RmsBaseAuditStatus conclusionStatus;

	/**
	 * 结题审批时间
	 */
	private LocalDateTime conclusionAuditTime;

	/**
	 * 结题流程实例编码
	 */
	private String conclusionProcessInstanceCode;

	/**
	 * 伦理审查批件
	 */
	private JsonNode ethicsAttachments;

	/**
	 * 伦理审批状态 #0未提交 1已通过 2被驳回 3待审核 4已取消
	 */
	private RmsBaseAuditStatus ethicsAuditStatus;

	/**
	 * 伦理审批时间
	 */
	private LocalDateTime ethicsAuditTime;

	/**
	 * 伦理审批流程实例编码
	 */
	private String ethicsProcessInstanceCode;

	/**
	 * 承诺书附件 #任务书
	 */
	private List<Map<String, Object>> promiseAttachments;

	/**
	 * 任务书审核状态 #0未提交 1已通过 2被驳回 3待审核 4已取消
	 */
	private RmsBaseAuditStatus briefAuditStatus;

	/**
	 * 任务书审核时间
	 */
	private LocalDateTime briefAuditTime;

	/**
	 * 任务书流程实例编码
	 */
	private String briefProcessInstanceCode;

	/**
	 * 验收总结
	 */
	private String acceptanceSummary;

	/**
	 * 验收附件
	 */
	private List<Map<String, Object>> acceptanceAttachments;

	/**
	 * 验收其他附件
	 */
	private JsonNode acceptanceOtherAttachments;

	/**
	 * 验收审核状态 #0未提交 1已通过 2被驳回 3待审核 4已取消
	 */
	private RmsBaseAuditStatus acceptanceAuditStatus;

	/**
	 * 验收审核时间
	 */
	private LocalDateTime acceptanceAuditTime;

	/**
	 * 验收流程实例编码
	 */
	private String acceptanceProcessInstanceCode;

	/**
	 * 是否需要验收结题其他附件
	 */
	private Boolean requireAccConOtherAtt;

	/**
	 * 是否可延期
	 */
	private Boolean deferAllowed;

	/** 项目信息 */
	private RmsProjectInfoVo projectInfo;

//	/**
//	 * 以下是返回给前端的项目详情信息
//	 */
//	private String projectDescr;
//	/**
//	 * 创新类型
//	 */
//	private String innovationType;
//
//	/**
//	 * 产学研联合
//	 */
//	private String industryAcademiaCollab;
//
//	/**
//	 * 知识产权状况
//	 */
//	private String ipStatus;
//
//	/**
//	 * 成果水平
//	 */
//	private String resultLevel;
//
//	/**
//	 * 成果形式
//	 */
//	private String[] resultFormat;
//
//	/**
//	 * 预期成果
//	 * 发明专利项数
//	 * 新型专利项数
//	 * 其他专利项数
//	 * 经费预算
//	 */
//	private RmsProjectInte rmsProjectInfo;
//
//	/**
//	 * 技术标准制定
//	 */
//	private String techStdDev;
//
//	/**
//	 * 经费预算
//	 */
//	private BigDecimal budget;

	private List<RmsProjectPerformanceGoalVo> rmsProjectPerformanceGoalData;

	/**
	 * 项目绩效目标
	 */
	private RmsProjectPerformanceGoalsVo rmsProjectPerformanceGoals;

	/**
	 * 分年度研究内容与考核指标（按每半年度填写）
	 */
	private List<RmsProjectContentIndexVo>  researchIndexData;

	/**
	 * 研究内容与考核指标的补充说明与预期目标
	 */
	private RmsResearchIndexSupplyVo researchIndexSupply;

	/**
	 * 项目研究设备/仪器登记
	 */
	private List<RmsEquipmentRegistrationVo> equipmentRegData;


	/**
	 * 项目收入经费
	 */
	private RmsProjectFundingIncomeVo rmsProjectFundingIncomeDto;

	/**
	 * 项目支出经费
	 */
	private List<RmsProjectFundsExpenseVo> fundsExpenseData;

	/**
	 * 项目支出经费树形结构
	 */
	private List<Tree<Integer>> fundsExpenseTreeData;

	/**
	 * 项目申报单位、合作单位及主要研究人员情况
	 */
	private RmsProjectOrgStructureVo projectOrgStructure;

	/**
	 * 项目合作单位
	 */
	private List<RmsCooperativeUnitsVo> cooperativeUnitsData;

	/**
	 * 项目课题组主要研究人员（含项目负责人）
	 */
	private List<RmsResearchersVo> researchersData;

	/**
	 * 富文本内容
	 */
	private RmsProjectRichTextVo richText;

	/**
	 * 项目经费报销记录
	 */
	private List<EcsReimbursementDetailVo> ecsReimbursementData;



	/**
	 * 审核流程详情
	 */
	private List<RmsAuditRcdfmVo> auditDetails;


	/**
	 * 项目填报类型，0：直接填报，1:23年填
	 */
	private String projectApplyType;

	/**
	 * 技术查新附件
	 */
	private List<Map<String, Object>> techQueryAttachment;

	/**
	 * 立项申报中的伦理审批附件
	 */
	private List<Map<String,Object>> ethicsApprovalAttachment;

	/**
	 * 申报书备案附件
	 */
	private List<Map<String,Object>> applicationDeclarationAttachment;

	private List<Map<String,Object>> promiseAttachment;
}

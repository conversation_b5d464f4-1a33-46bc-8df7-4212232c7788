package com.jp.med.rms.modules.config.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 意向单位科研专项表
 * <AUTHOR>
 * @email 
 * @date 2024-12-15 17:14:43
 */
@Data
@TableName("rms_intended_unit_special" )
public class RmsIntendedUnitSpecialDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 科研专项名称 */
    @TableField("name")
    private String name;

    /** 意向单位ID */
    @TableField("unit_id")
    private Integer unitId;

    /** 是否删除 */
    @TableField("deleted")
    @TableLogic(value = "false", delval = "true")
    private Boolean deleted;

    /** 创建时间 */
    @TableField("create_time")
    private LocalDateTime createTime;

    /** 创建人 */
    @TableField("creator")
    private String creator;

    /** 修改时间 */
    @TableField("update_time")
    private LocalDateTime updateTime;

    /** 修改人 */
    @TableField("updater")
    private String updater;

    /** 组织ID */
    @TableField("hospital_id")
    private String hospitalId;

}

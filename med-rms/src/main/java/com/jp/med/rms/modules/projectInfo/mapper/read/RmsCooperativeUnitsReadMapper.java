package com.jp.med.rms.modules.projectInfo.mapper.read;

import com.jp.med.rms.modules.projectInfo.dto.RmsCooperativeUnitsDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsCooperativeUnitsVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 合作单位
 * <AUTHOR>
 * @email -
 * @date 2024-09-23 15:00:14
 */
@Mapper
public interface RmsCooperativeUnitsReadMapper extends BaseMapper<RmsCooperativeUnitsDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsCooperativeUnitsVo> queryList(RmsCooperativeUnitsDto dto);
}

package com.jp.med.rms.modules.projectInfo.mapper.read;

import com.jp.med.rms.modules.projectInfo.dto.RmsResearchIndexSupplyDto;
import com.jp.med.rms.modules.projectInfo.vo.RmsResearchIndexSupplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 项目研究指标补充描述
 * <AUTHOR>
 * @email -
 * @date 2024-09-18 09:56:30
 */
@Mapper
public interface RmsResearchIndexSupplyReadMapper extends BaseMapper<RmsResearchIndexSupplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<RmsResearchIndexSupplyVo> queryList(RmsResearchIndexSupplyDto dto);
}

package com.jp.med.rms.modules.projectInfo.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.rms.modules.projectInfo.dto.RmsProposalFileDto;
import com.jp.med.rms.modules.projectInfo.service.read.RmsProposalFileReadService;
import com.jp.med.rms.modules.projectInfo.service.write.RmsProposalFileWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 立项文件
 * <AUTHOR>
 * @email -
 * @date 2024-09-29 17:48:54
 */
@Api(value = "立项文件", tags = "立项文件")
@RestController
@RequestMapping("rmsProposalFile")
public class RmsProposalFileController {

    @Autowired
    private RmsProposalFileReadService rmsProposalFileReadService;

    @Autowired
    private RmsProposalFileWriteService rmsProposalFileWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询立项文件")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody RmsProposalFileDto dto){
        return CommonResult.paging(rmsProposalFileReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询立项文件")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody RmsProposalFileDto dto){
        return CommonResult.success(rmsProposalFileReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增立项文件")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody RmsProposalFileDto dto){
        rmsProposalFileWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改立项文件")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody RmsProposalFileDto dto){
        rmsProposalFileWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除立项文件")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody RmsProposalFileDto dto){
        rmsProposalFileWriteService.removeById(dto);
        return CommonResult.success();
    }

}

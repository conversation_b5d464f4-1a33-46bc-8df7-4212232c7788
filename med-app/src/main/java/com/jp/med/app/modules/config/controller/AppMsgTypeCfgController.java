package com.jp.med.app.modules.config.controller;

import com.jp.med.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.app.modules.config.dto.AppMsgTypeCfgDto;
import com.jp.med.app.modules.config.service.read.AppMsgTypeCfgReadService;
import com.jp.med.app.modules.config.service.write.AppMsgTypeCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 消息类型配置
 * <AUTHOR>
 * @email -
 * @date 2023-12-19 15:21:06
 */
@Api(value = "消息类型配置", tags = "消息类型配置")
@RestController
@RequestMapping("appMsgTypeCfg")
public class AppMsgTypeCfgController {

    @Autowired
    private AppMsgTypeCfgReadService appMsgTypeCfgReadService;

    @Autowired
    private AppMsgTypeCfgWriteService appMsgTypeCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询消息类型配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody AppMsgTypeCfgDto dto){
        return CommonResult.success(appMsgTypeCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增消息类型配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody AppMsgTypeCfgDto dto){
        dto.setCrter(dto.getSysUser().getUsername());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        appMsgTypeCfgWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改消息类型配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody AppMsgTypeCfgDto dto){
        appMsgTypeCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除消息类型配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody AppMsgTypeCfgDto dto){
        appMsgTypeCfgWriteService.removeById(dto);
        return CommonResult.success();
    }

}

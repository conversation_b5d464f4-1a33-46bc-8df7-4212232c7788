package com.jp.med.app.modules.message.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.common.dto.app.AppMsgDto;

/**
 * APP消息
 * <AUTHOR>
 * @email -
 * @date 2023-12-19 17:13:58
 */
public interface AppMsgWriteService extends IService<AppMsgDto> {

    /**
     * 新增消息
     * @param dto
     */
    Long addMessage(AppMsgDto dto);

    /**
     * 修改消息读取状态
     * @param dto
     */
    void updateReadState(AppMsgDto dto);

    /**
     * 添加验证码
     * @param dto
     */
    void addVerifyCode(AppMsgDto dto);
}


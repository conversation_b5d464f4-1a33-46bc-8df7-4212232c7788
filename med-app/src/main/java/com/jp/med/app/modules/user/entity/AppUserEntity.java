package com.jp.med.app.modules.user.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * APP用户
 * <AUTHOR>
 * @email -
 * @date 2023-12-20 14:04:01
 */
@Data
@TableName("app_user")
public class AppUserEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 客户端ID */
	@TableField("client_id")
	private String clientId;

	/** 用户名 */
	@TableField("username")
	private String username;

	/** 登录时间 */
	@TableField("login_time")
	private String loginTime;

	/** 登录IP */
	@TableField("ip")
	private String ip;

	/** 有效标志 */
	@TableField("active_flag")
	private String activeFlag;

}

package com.jp.med.cost.modules.calculation.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.calculation.dto.CostCalculationDto;
import com.jp.med.cost.modules.calculation.vo.*;
import com.jp.med.cost.modules.common.vo.ServiceLavelVo;


import java.util.List;
import java.util.Map;

public interface CostCalculationReadMapper extends BaseMapper<CostCalculationDto> {
    /**
     * 查询科室自身成本
     *
     * @param dto
     * @return
     */
    List<DeptMonSelfCostVo> queryDeptSelfCost(CostCalculationDto dto);

    /**
     * 查询科室分摊成本
     *
     * @param dto
     * @return
     */
    List<CostVo> queryDeptAllocationCost(CostCalculationDto dto);

    /**
     * 查询科室成本分摊参数
     *
     * @param dto
     * @return
     */
    List<DeptAllocationParamVo> queryDeptAllocationParam(CostCalculationDto dto);

    /**
     * 查询科室没有的分摊成本
     *
     * @param dto
     * @return
     */
    List<CostVo> queryDeptNotHaveAllocationCost(CostCalculationDto dto);

    /**
     * 查询服务项目测算数据
     *
     * @param dto
     * @return
     */
    // void queryAllServiceProjectData(CostCalculationDto dto, ResultHandler resultHandler);
    List<DeptItemCostDetlVo> queryAllServiceProjectData(CostCalculationDto dto);

    /**
     * 查询项目成本结果数据
     *
     * @param dto
     * @return
     */
    List<ServiceUnitMedItemCostDetlVo> queryServiceProjectCost(CostCalculationDto dto);

    /**
     * 查询是否有服务项目测算数据
     *
     * @param dto
     * @return
     */
    Integer queryIsExistData(CostCalculationDto dto);

    /**
     * 测算医院患者成本核算记录
     *
     * @param dto
     * @return
     */
    List<HospPatnCostingRcdVo> queryCalcPatientCost(CostCalculationDto dto);

    /**
     * 查询是否有医院患者成本核算记录
     *
     * @param dto
     * @return
     */
    Integer queryExistPatientData(CostCalculationDto dto);

    /**
     * 查询是否有病组成本核算记录
     *
     * @param dto
     * @return
     */
    Integer queryExistDiseaseGroupData(CostCalculationDto dto);

    /**
     * 查询病组测算数据
     *
     * @param dto
     * @return
     */
    List<DieaseGroupCostVo> queryDieaseGroup(CostCalculationDto dto);

    /**
     * 查询服务单元收入比数据
     *
     * @param dto
     * @return
     */
    List<CostingServModIncRatVo> queryDieaseData(CostCalculationDto dto);

    /**
     * 查询病案首页类型
     *
     * @param dto
     * @return
     */
    String queryAke554(CostCalculationDto dto);

    /**
     * 查询医院患者成本核算记录数据
     *
     * @param dto
     * @return
     */
    List<Map<String, Object>> queryKkr6Data(CostCalculationDto dto);

    /**
     * 查询病种成本数据
     *
     * @param dto
     * @return
     */
    List<HospDiseIcd10CostingRcdVo> queryKkr7Data(CostCalculationDto dto);

    /**
     * 查询服务单元信息
     *
     * @param dto
     * @return
     */
    List<ServiceLavelVo> queryServiceLabel(CostCalculationDto dto);

    /**
     * 查询成本核算服务单元信息
     *
     * @return
     */
    List<ServiceLavelVo> queryServiceUnit();

    /**
     * 查询所有服务项目总数
     *
     * @param dto
     * @return
     */
    Integer queryAllServiceProjectDataCount(CostCalculationDto dto);

    /**
     * 医疗服务单元成本汇总信息
     *
     * @param dto
     * @return
     */
    List<ServiceUnitCostVo> queryServiceUnitCostData(CostCalculationDto dto);

    /**
     * 查询医疗服务单元服务项目收入汇总信息
     *
     * @param dto
     * @return
     */
    List<ServiceUnitMedItemDetlVo> queryDeptChargeItemCostGather(CostCalculationDto dto);
}

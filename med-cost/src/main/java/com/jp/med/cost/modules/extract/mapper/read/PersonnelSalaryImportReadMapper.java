package com.jp.med.cost.modules.extract.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.extract.dto.PersonalSalaryDto;
import com.jp.med.cost.modules.extract.vo.PsnSalRcdVo;

import java.util.List;

/**
 * <p>
 * 人员薪资导入Mapper
 * </p>
 *
 * <AUTHOR>
 * @since 2021/4/13
 */
public interface PersonnelSalaryImportReadMapper extends BaseMapper<PersonalSalaryDto> {
    /**
     * 校验人员薪资信息是否存在
     *
     * @param dto
     * @return
     */
    int checkExistPersonalSalary(PsnSalRcdVo psnSalRcdVo);

    List<PsnSalRcdVo> queryPersonalSalaryList(PersonalSalaryDto dto);
}

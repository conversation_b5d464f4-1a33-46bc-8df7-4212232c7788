package com.jp.med.cost.modules.dataDictionaryConfig.enums;

import java.util.stream.Stream;

public enum DepartmentTypeEnum {
    WHOLE_HOSPITAL_COST("0", "全院成本"),
    ADMINISTRATIVE_DEPARTMENT("1", "行政后勤科室"),
    MEDICAL_ASSISTANCE("2", "医疗辅助科室"),
    MEDICAL_TECHNOLOGY("3", "医疗技术科室"),
    CLINICAL("4", "临床科室");

    private String code;
    private String name;

    DepartmentTypeEnum(String code, String name) {
        this.code = code;
        this.name = name;
    }

    public static DepartmentTypeEnum getByCode(String code) {
        return Stream.of(values()).filter(item -> code.equals(item.getCode())).findAny().orElse(null);
    }

    public String getCode() {
        return code;
    }

    public String getName() {
        return name;
    }

}

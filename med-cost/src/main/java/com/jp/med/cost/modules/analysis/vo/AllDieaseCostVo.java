package com.jp.med.cost.modules.analysis.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;

@Getter
@Setter
public class AllDieaseCostVo implements Serializable {
    /**
     * 病种数量
     */
    private Integer num;

    /**
     * 种均成本比例
     */
    private BigDecimal radio;

    /**
     * 病种名称
     */
    private String bke740;

    /**
     * 病种成本金额
     */
    private BigDecimal kkh262;

    /**
     * 可控成本金额
     */
    private BigDecimal kkh262c;

    /**
     * 不可控成本金额
     */
    private BigDecimal kkh262nc;
}

package com.jp.med.cost.modules.analysis.mapper.read;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.analysis.dto.BasicCostControlDto;
import com.jp.med.cost.modules.analysis.dto.ServiceProjectCostDto;
import com.jp.med.cost.modules.analysis.vo.CostingRepCfgVo;

import java.util.List;

public interface CostReportOutputReadMapper extends BaseMapper<BasicCostControlDto> {
    /**
     * 查询成本报表配置数据
     * @return
     */
    List<CostingRepCfgVo> queryCostReportTree();

    /**
     * 查询全部的报表文件名
     * @return
     */
    List<String> queryCostReportFileName();
}

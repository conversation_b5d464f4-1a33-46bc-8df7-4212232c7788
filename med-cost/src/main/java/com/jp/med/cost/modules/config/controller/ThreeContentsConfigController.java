package com.jp.med.cost.modules.config.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.config.dto.ThreeContentsConfigDto;
import com.jp.med.cost.modules.config.service.read.ThreeContentsConfigReadService;
import com.jp.med.cost.modules.config.service.write.ThreeContentsConfigWriteService;
import com.jp.med.cost.modules.config.vo.HospHilistCfgVo;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

@Api(value = "医院三目录配置", tags = "医院三目录配置")
@RestController
@RequestMapping("ThreeContentsConfigRestService")
@Validated
public class ThreeContentsConfigController extends HospBaseController {
    @Autowired
    private ThreeContentsConfigReadService threeContentsConfigReadService;
    @Autowired
    private ThreeContentsConfigWriteService threeContentsConfigWriteService;

    /**
     * 分页查询三目录
     *
     * @return
     */
    @PostMapping("queryThreeContentsConfig")
    public CommonResult<?> queryThreeContentsConfig(ThreeContentsConfigDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        List<HospHilistCfgVo> page = threeContentsConfigReadService.queryThreeContentsConfig(dto);
        return CommonResult.paging(page);
    }

    /**
     * 修改三目录
     *
     * @return
     */
    @RequestMapping("fnChangeThreeContentsConfig")
    public CommonResult<?> fnChangeThreeContentsConfig(ThreeContentsConfigDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(getOrgStatisticsData(request).getAkb020())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        threeContentsConfigWriteService.fnChangeThreeContentsConfig(dto);
        return CommonResult.success();
    }

    /**
     * 增加三目录
     *
     * @return
     */
    @RequestMapping("fnAddThreeContentsConfig")
    public CommonResult<?> fnAddThreeContentsConfig(ThreeContentsConfigDto dto, HttpServletRequest request) throws Exception {
        if (ValidateUtil.isEmpty(getOrgStatisticsData(request).getAkb020())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        threeContentsConfigWriteService.fnAddThreeContentsConfig(dto);
        return CommonResult.success();
    }
}

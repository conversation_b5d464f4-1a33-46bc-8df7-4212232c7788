package com.jp.med.cost.modules.calculation.mapper.write;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.cost.modules.calculation.dto.CostCalculationDto;
import com.jp.med.cost.modules.calculation.vo.*;
import com.jp.med.cost.modules.extract.vo.DeptCostImpRcdVo;

import java.util.List;
import java.util.Map;

public interface CostCalculationWriteMapper extends BaseMapper<CostCalculationDto> {
    /**
     * 移除科室自生成本
     *
     * @param dto
     * @return
     */
    int removeDeptSelfCost(CostCalculationDto dto);

    /**
     * 抽取科室自身成本
     *
     * @param dto
     * @return
     */
    int extractDeptSelfCost(CostCalculationDto dto);

    /**
     * 移除分摊成本数据
     *
     * @param dto
     * @return
     */
    int removeRepeatKkq7(CostCalculationDto dto);

    /**
     * 插入科室分摊成本
     *
     * @param list
     */
    void betchInsertKkq7(List<DeptMonAprtCostVo> list);

    /**
     * 科室月度成本统计
     *
     * @param dto
     * @return
     */
    int deptMonthlyCostStatistics(CostCalculationDto dto);

    /**
     * 科室月度成本统计数据去重
     *
     * @param dto
     * @return
     */
    int removeRepeatDeptMonthlyCost(CostCalculationDto dto);

    /**
     * 移除科室月度成本构成统计结果
     *
     * @param dto
     * @return
     */
    int removeRepeatDetpCostComposition(CostCalculationDto dto);

    /**
     * 插入科室月度成本构成
     *
     * @param dto
     * @return
     */
    int deptMonthlyDeptCostComposition(CostCalculationDto dto);

    /**
     * 插入科室项目成本明细
     *
     * @param list
     */
    void saveServiceProject(List<ServiceUnitMedItemCostDetlVo> list);

    /**
     * 删除科室项目成本明细
     *
     * @param dto
     */
    void deleteServiceProject(CostCalculationDto dto);

    /**
     * 插入医院患者成本核算记录
     *
     * @param kkr6Vo
     */
    void insertKkr6(List<Map<String, Object>> kkr6Vo);

    /**
     * 删除医院患者成本核算记录
     *
     * @param dto
     */
    void daleteKkr6(CostCalculationDto dto);

    /**
     * 插入病组成本记录
     *
     * @param vo
     */
    void insertDieaseGroup(List<DieaseGroupCostVo> vo);

    /**
     * 删除病组数据
     *
     * @param dto
     */
    void daleteDieaseGroup(CostCalculationDto dto);


    /**
     * 插入成本核算服务单元收入比
     *
     * @param vo
     */
    void insertKbd1(List<CostingServModIncRatVo> vo);

    /**
     * 删除成本核算服务单元收入比
     *
     * @param dto
     */
    void deleteKbd1(CostCalculationDto dto);

    /**
     * 插入病种成本数据
     *
     * @param vo
     */
    void insertKkr7(List<HospDiseIcd10CostingRcdVo> vo);

    /**
     * 删除病种成本数据
     *
     * @param dto
     */
    void deleteKkr7(CostCalculationDto dto);

    /**
     * 插入患者服务单元收入明细
     *
     * @param kbd2Vo
     */
    void insertKbd2(List<PatnServModIncDetlVo> kbd2Vo);

    /**
     * 删除患者服务单元收入明细
     *
     * @param dto
     */
    void deleteKbd2(CostCalculationDto dto);
}

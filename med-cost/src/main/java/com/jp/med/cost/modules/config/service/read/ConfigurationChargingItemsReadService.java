package com.jp.med.cost.modules.config.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cost.modules.config.dto.ConfigurationChargingDto;
import com.jp.med.cost.modules.config.vo.HospChrgItemCfgVo;

import java.util.List;

public interface ConfigurationChargingItemsReadService extends IService<ConfigurationChargingDto> {
    List<HospChrgItemCfgVo> queryConfiguration(ConfigurationChargingDto dto);

    List<String> querychrgItemlv();
}

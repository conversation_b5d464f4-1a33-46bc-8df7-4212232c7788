package com.jp.med.cost.modules.config.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.exception.AppException;
import com.jp.med.cost.modules.common.controller.HospBaseController;
import com.jp.med.cost.modules.common.util.ValidateUtil;
import com.jp.med.cost.modules.config.dto.CCHIConfigDto;
import com.jp.med.cost.modules.config.service.read.CCHIConfigReadService;
import com.jp.med.cost.modules.config.service.write.CCHIConfigWriteService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;

@Api(value = "CCHI技术难度和风险程度配置", tags = "CCHI技术难度和风险程度配置")
@RestController
@RequestMapping("CCHIConfigRestService")
@Validated
public class CCHIConfigController extends HospBaseController {

    @Autowired
    private CCHIConfigReadService cchiConfigReadService;

    @Autowired
    private CCHIConfigWriteService cchiConfigWriteService;

    /**
     * 分页查询CCHI
     *
     * @return
     */
    @RequestMapping("queryCCHIConfig")
    public CommonResult<?> queryCCHIConfig(CCHIConfigDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        return CommonResult.paging(cchiConfigReadService.queryCCHIConfig(dto));
    }

    /**
     * 修改CCHI
     *
     * @return
     */
    @RequestMapping("fnChangeCCHIConfig")
    public void fnChangeCCHIConfig(CCHIConfigDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        cchiConfigWriteService.changeCCHIConfig(dto);
        CommonResult.success();
    }

    /**
     * 增加查询CCHI
     *
     * @return
     */
    @RequestMapping("fnAddCCHIConfig")
    public void fnAddCCHIConfig(CCHIConfigDto dto) throws Exception {
        if (ValidateUtil.isEmpty(dto.getHospitalId())) {
            throw new AppException("查询不到当前人员所属的医院信息,不能使用该功能!");
        }
        cchiConfigWriteService.addCCHIConfig(dto);
        CommonResult.success();
    }
}

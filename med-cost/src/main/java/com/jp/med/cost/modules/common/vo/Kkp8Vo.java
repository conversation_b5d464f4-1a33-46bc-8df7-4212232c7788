package com.jp.med.cost.modules.common.vo;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;

/**
 * 医疗机构成本项配置Vo
 */

@Setter
@Getter
public class Kkp8Vo implements Serializable {
    private static final long serialVersionUID = -2073190815267370624L;
    private String aay003; // 全院成本项配置id,
    private String akb020; // 医疗机构编号,
    private String aae001; //  年度,
    private String kkh204; //  成本编码,
    private String kkh205; //  成本名称,
    private String kkh206; // 成本类型(1人员经费,2卫生材料费,3药品费,4固定资产折旧,5无形资产摊销6,提取医疗风险基金7,其它运行费用),
    private String kkh207; // 成本形态(1固定,2变动),
    private String kkh208; // 成本可控性(1可控,2不可控),
    private String kkh209; //  科室类型共有成本（1,2,3,4,5）,
    private String kkh239; // 成本分摊代码(8位,现阶段用前4位，后4位补0，0代表不分摊，1代表分摊)第一位人数，第二位面积，第三位收入，第四位工作量,
    private String aae036; //  操作时间,
    private String aae100; // 有效标志,
    private String key;
    private String title;
}

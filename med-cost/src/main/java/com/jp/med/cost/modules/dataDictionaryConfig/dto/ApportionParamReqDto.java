package com.jp.med.cost.modules.dataDictionaryConfig.dto;

import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;

import java.util.List;

@Data
/**
 * 成本分摊代码
 */
public class ApportionParamReqDto extends CommonQueryDto {
    //成本分摊代码(8位,现阶段用前4位，后4位补0，0代表不分摊，1代表分摊)第一位人数，第二位面积，第三位收入，第四位工作量
    //需要设置的id
    private List<String> ids;

    //职工人数
    private boolean is_aprt_dept_psncnt;

    //科室面积
    private boolean is_aprt_dept_used_area;

    //科室收入
    private boolean is_aprt_dept_inc;

    //科室工作量
    private boolean is_aprt_dept_wkld;

    //成本分摊代码
    private String cost_aprt_code;

    private List<String> codes;
}

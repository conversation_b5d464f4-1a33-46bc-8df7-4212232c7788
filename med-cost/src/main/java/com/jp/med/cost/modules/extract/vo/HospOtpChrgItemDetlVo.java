package com.jp.med.cost.modules.extract.vo;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * class HospOtpChrgItemDetlVo
 * desc   医院门诊收费项目明细 - hosp_otp_chrg_item_detl
 *
 * <AUTHOR>
 */
public class HospOtpChrgItemDetlVo implements Serializable {

    private static final long serialVersionUID = 3707398326137213556L;
    /**
     * 病人ID
     * VARCHAR(16)
     * isNullable true
     */
    private String patn_id;
    /**
     * 主页ID
     * VARCHAR(16)
     * isNullable true
     */
    private String home_id;
    /**
     * 门诊号
     * VARCHAR(16)
     * isNullable true
     */
    private String otp_no;
    /**
     * 姓名
     * VARCHAR(100)
     * isNullable true
     */
    private String name;
    /**
     * 项目编码
     * VARCHAR(32)
     * isNullable true
     */
    private String item_codg;
    /**
     * 项目名称
     * VARCHAR(128)
     * isNullable true
     */
    private String itemname;
    /**
     * 项目分类
     * VARCHAR(16)
     * isNullable true
     */
    private String item_type;
    /**
     * 计价单位
     * VARCHAR(32)
     * isNullable true
     */
    private String pric_emp;
    /**
     * 单价
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double pric;
    /**
     * 总费用
     * NUMERIC(12, 4)
     * isNullable true
     */
    private Double sumfee;
    /**
     * 医嘱
     * VARCHAR(128)
     * isNullable true
     */
    private String drord;
    /**
     * 开单科室编码
     * VARCHAR(20)
     * isNullable true
     */
    private String bilg_dept_codg;
    /**
     * 开单科室名称
     * VARCHAR(50)
     * isNullable true
     */
    private String bilg_dept_name;
    /**
     * 开单医生
     * VARCHAR(50)
     * isNullable true
     */
    private String bilg_dr;
    /**
     * 受单科室编码
     * VARCHAR(20)
     * isNullable true
     */
    private String acord_dept_codg;
    /**
     * 受单科室名称
     * VARCHAR(50)
     * isNullable true
     */
    private String acord_dept_name;
    /**
     * 受单医生
     * VARCHAR(50)
     * isNullable true
     */
    private String acord_dr;
    /**
     * 收费时间
     * TIMESTAMP
     * isNullable true
     */
    private Timestamp chrg_time;
    /**
     * 经办时间
     * TIMESTAMP
     * isNullable true
     */
    private Timestamp opt_time;
    /**
     * 有效标志
     * VARCHAR(2)
     * isNullable true
     */
    private String vali_flag;
    /**
     * 医疗机构编号
     * VARCHAR(30)
     * isNullable false
     */
    private String medins_no;

    /**
     * 设置 patn_id 病人ID
     *
     * @param patn_id 病人ID
     */
    public void setPatn_id(String patn_id) {
        this.patn_id = patn_id;
    }

    /**
     * 设置 home_id 主页ID
     *
     * @param home_id 主页ID
     */
    public void setHome_id(String home_id) {
        this.home_id = home_id;
    }

    /**
     * 设置 otp_no 门诊号
     *
     * @param otp_no 门诊号
     */
    public void setOtp_no(String otp_no) {
        this.otp_no = otp_no;
    }

    /**
     * 设置 name 姓名
     *
     * @param name 姓名
     */
    public void setName(String name) {
        this.name = name;
    }

    /**
     * 设置 item_codg 项目编码
     *
     * @param item_codg 项目编码
     */
    public void setItem_codg(String item_codg) {
        this.item_codg = item_codg;
    }

    /**
     * 设置 itemname 项目名称
     *
     * @param itemname 项目名称
     */
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    /**
     * 设置 item_type 项目分类
     *
     * @param item_type 项目分类
     */
    public void setItem_type(String item_type) {
        this.item_type = item_type;
    }

    /**
     * 设置 pric_emp 计价单位
     *
     * @param pric_emp 计价单位
     */
    public void setPric_emp(String pric_emp) {
        this.pric_emp = pric_emp;
    }

    /**
     * 设置 pric 单价
     *
     * @param pric 单价
     */
    public void setPric(Double pric) {
        this.pric = pric;
    }

    /**
     * 设置 sumfee 总费用
     *
     * @param sumfee 总费用
     */
    public void setSumfee(Double sumfee) {
        this.sumfee = sumfee;
    }

    /**
     * 设置 drord 医嘱
     *
     * @param drord 医嘱
     */
    public void setDrord(String drord) {
        this.drord = drord;
    }

    /**
     * 设置 bilg_dept_codg 开单科室编码
     *
     * @param bilg_dept_codg 开单科室编码
     */
    public void setBilg_dept_codg(String bilg_dept_codg) {
        this.bilg_dept_codg = bilg_dept_codg;
    }

    /**
     * 设置 bilg_dept_name 开单科室名称
     *
     * @param bilg_dept_name 开单科室名称
     */
    public void setBilg_dept_name(String bilg_dept_name) {
        this.bilg_dept_name = bilg_dept_name;
    }

    /**
     * 设置 bilg_dr 开单医生
     *
     * @param bilg_dr 开单医生
     */
    public void setBilg_dr(String bilg_dr) {
        this.bilg_dr = bilg_dr;
    }

    /**
     * 设置 acord_dept_codg 受单科室编码
     *
     * @param acord_dept_codg 受单科室编码
     */
    public void setAcord_dept_codg(String acord_dept_codg) {
        this.acord_dept_codg = acord_dept_codg;
    }

    /**
     * 设置 acord_dept_name 受单科室名称
     *
     * @param acord_dept_name 受单科室名称
     */
    public void setAcord_dept_name(String acord_dept_name) {
        this.acord_dept_name = acord_dept_name;
    }

    /**
     * 设置 acord_dr 受单医生
     *
     * @param acord_dr 受单医生
     */
    public void setAcord_dr(String acord_dr) {
        this.acord_dr = acord_dr;
    }

    /**
     * 设置 chrg_time 收费时间
     *
     * @param chrg_time 收费时间
     */
    public void setChrg_time(Timestamp chrg_time) {
        this.chrg_time = chrg_time;
    }

    /**
     * 设置 opt_time 经办时间
     *
     * @param opt_time 经办时间
     */
    public void setOpt_time(Timestamp opt_time) {
        this.opt_time = opt_time;
    }

    /**
     * 设置 vali_flag 有效标志
     *
     * @param vali_flag 有效标志
     */
    public void setVali_flag(String vali_flag) {
        this.vali_flag = vali_flag;
    }

    /**
     * 设置 medins_no 医疗机构编号
     *
     * @param medins_no 医疗机构编号
     */
    public void setMedins_no(String medins_no) {
        this.medins_no = medins_no;
    }

    /**
     * 获取 patn_id 病人ID
     *
     * @return patn_id
     */
    public String getPatn_id() {
        return this.patn_id;
    }

    /**
     * 获取 home_id 主页ID
     *
     * @return home_id
     */
    public String getHome_id() {
        return this.home_id;
    }

    /**
     * 获取 otp_no 门诊号
     *
     * @return otp_no
     */
    public String getOtp_no() {
        return this.otp_no;
    }

    /**
     * 获取 name 姓名
     *
     * @return name
     */
    public String getName() {
        return this.name;
    }

    /**
     * 获取 item_codg 项目编码
     *
     * @return item_codg
     */
    public String getItem_codg() {
        return this.item_codg;
    }

    /**
     * 获取 itemname 项目名称
     *
     * @return itemname
     */
    public String getItemname() {
        return this.itemname;
    }

    /**
     * 获取 item_type 项目分类
     *
     * @return item_type
     */
    public String getItem_type() {
        return this.item_type;
    }

    /**
     * 获取 pric_emp 计价单位
     *
     * @return pric_emp
     */
    public String getPric_emp() {
        return this.pric_emp;
    }

    /**
     * 获取 pric 单价
     *
     * @return pric
     */
    public Double getPric() {
        return this.pric;
    }

    /**
     * 获取 sumfee 总费用
     *
     * @return sumfee
     */
    public Double getSumfee() {
        return this.sumfee;
    }

    /**
     * 获取 drord 医嘱
     *
     * @return drord
     */
    public String getDrord() {
        return this.drord;
    }

    /**
     * 获取 bilg_dept_codg 开单科室编码
     *
     * @return bilg_dept_codg
     */
    public String getBilg_dept_codg() {
        return this.bilg_dept_codg;
    }

    /**
     * 获取 bilg_dept_name 开单科室名称
     *
     * @return bilg_dept_name
     */
    public String getBilg_dept_name() {
        return this.bilg_dept_name;
    }

    /**
     * 获取 bilg_dr 开单医生
     *
     * @return bilg_dr
     */
    public String getBilg_dr() {
        return this.bilg_dr;
    }

    /**
     * 获取 acord_dept_codg 受单科室编码
     *
     * @return acord_dept_codg
     */
    public String getAcord_dept_codg() {
        return this.acord_dept_codg;
    }

    /**
     * 获取 acord_dept_name 受单科室名称
     *
     * @return acord_dept_name
     */
    public String getAcord_dept_name() {
        return this.acord_dept_name;
    }

    /**
     * 获取 acord_dr 受单医生
     *
     * @return acord_dr
     */
    public String getAcord_dr() {
        return this.acord_dr;
    }

    /**
     * 获取 chrg_time 收费时间
     *
     * @return chrg_time
     */
    public Timestamp getChrg_time() {
        return this.chrg_time;
    }

    /**
     * 获取 opt_time 经办时间
     *
     * @return opt_time
     */
    public Timestamp getOpt_time() {
        return this.opt_time;
    }

    /**
     * 获取 vali_flag 有效标志
     *
     * @return vali_flag
     */
    public String getVali_flag() {
        return this.vali_flag;
    }

    /**
     * 获取 medins_no 医疗机构编号
     *
     * @return medins_no
     */
    public String getMedins_no() {
        return this.medins_no;
    }

    /**
     * 转换为map对象
     *
     * @return Map
     */
    public java.util.Map toMap() {
        java.util.Map map = new java.util.HashMap();
        // patn_id 病人ID
        map.put("patn_id", getPatn_id());
        // home_id 主页ID
        map.put("home_id", getHome_id());
        // otp_no 门诊号
        map.put("otp_no", getOtp_no());
        // name 姓名
        map.put("name", getName());
        // item_codg 项目编码
        map.put("item_codg", getItem_codg());
        // itemname 项目名称
        map.put("itemname", getItemname());
        // item_type 项目分类
        map.put("item_type", getItem_type());
        // pric_emp 计价单位
        map.put("pric_emp", getPric_emp());
        // pric 单价
        map.put("pric", getPric());
        // sumfee 总费用
        map.put("sumfee", getSumfee());
        // drord 医嘱
        map.put("drord", getDrord());
        // bilg_dept_codg 开单科室编码
        map.put("bilg_dept_codg", getBilg_dept_codg());
        // bilg_dept_name 开单科室名称
        map.put("bilg_dept_name", getBilg_dept_name());
        // bilg_dr 开单医生
        map.put("bilg_dr", getBilg_dr());
        // acord_dept_codg 受单科室编码
        map.put("acord_dept_codg", getAcord_dept_codg());
        // acord_dept_name 受单科室名称
        map.put("acord_dept_name", getAcord_dept_name());
        // acord_dr 受单医生
        map.put("acord_dr", getAcord_dr());
        // chrg_time 收费时间
        map.put("chrg_time", getChrg_time());
        // opt_time 经办时间
        map.put("opt_time", getOpt_time());
        // vali_flag 有效标志
        map.put("vali_flag", getVali_flag());
        // medins_no 医疗机构编号
        map.put("medins_no", getMedins_no());
        return map;
    }

}
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.jp.med.cost.modules.extract.mapper.read.BasicDataTransmissionReadMapper">
    <!--查询收费项目明细-->
    <select id="queryChargeDetailsList" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="com.jp.med.cost.modules.common.vo.HospChrgItemDetlVo">
        SELECT
        patn_id,<!--病人ID-->
        home_id,<!--主页ID-->
        ipt_no,<!--住院号-->
        name,<!--姓名-->
        item_codg,<!--项目编码-->
        itemname,<!--项目名称-->
        chrg_type,<!--项目分类-->
        cnt,<!--数量-->
        pric_emp,<!--计价单位-->
        pric,<!--单价-->
        sumfee,<!--总费用-->
        drord,<!--医嘱-->
        bilg_dept_codg,<!--开单科室编码-->
        bilg_dept_name,<!--开单科室名称-->
        bilg_dr,<!--开单医生-->
        acord_dept_codg,<!--受单科室编码-->
        acord_dept_name,<!--受单科室名称-->
        acord_dr,<!--受单医生-->
        chrg_time,<!--收费时间-->
        opt_time,<!--经办时间-->
        vali_flag,<!--有效标志-->
        data_ym,<!--结算期号-->
        medins_no<!--医疗机构编号-->
        FROM hosp_chrg_item_detl
        <where>
            vali_flag='1'
            <!--住院号-->
            <if test="ipt_no!=null and ipt_no!=''">
                AND ipt_no=#{ipt_no,jdbcType=VARCHAR}
            </if>
            <!--姓名-->
            <if test="name!=null and name!=''">
                AND name LIKE '%'||#{name,jdbcType=VARCHAR}||'%'
            </if>
            <!--项目名称-->
            <if test="itemname!=null and itemname!=''">
                AND itemname LIKE '%'||#{itemname,jdbcType=VARCHAR}||'%'
            </if>
            <!--项目分类-->
            <if test="chrg_type!=null and chrg_type!=''">
                AND chrg_type=#{chrg_type,jdbcType=VARCHAR}
            </if>
            <!--开单科室编码-->
            <if test="bilg_dept_codg!=null and bilg_dept_codg!=''">
                AND bilg_dept_codg=#{bilg_dept_codg,jdbcType=VARCHAR}
            </if>
            <!--受单科室编码-->
            <if test="acord_dept_codg!=null and acord_dept_codg!=''">
                AND acord_dept_codg=#{acord_dept_codg,jdbcType=VARCHAR}
            </if>
            <!--开单科室名称-->
            <if test="bilg_dept_name!=null and bilg_dept_name!=''">
                AND bilg_dept_name LIKE '%'||#{bilg_dept_name,jdbcType=VARCHAR}||'%'
            </if>
            <!--受单科室名称-->
            <if test="acord_dept_name!=null and acord_dept_name!=''">
                AND acord_dept_name LIKE '%'||#{acord_dept_name,jdbcType=VARCHAR}||'%'
            </if>
            <!--收费时间大于某个时间段-->
            <if test="datetime_start!=null and datetime_start!=''">
                AND <![CDATA[ TO_CHAR(chrg_time,'yyyy-mm')>=#{datetime_start,jdbcType=VARCHAR}]]>
            </if>
            <!--收费时间小于某个时间段-->
            <if test="datetime_end!=null and datetime_end!=''">
                AND <![CDATA[ TO_CHAR(datetime_end,'yyyy-mm')<=#{datetime_end,jdbcType=VARCHAR}]]>
            </if>
            <!--期号时间查询条件-->
            <if test="issue!=null and issue!=''">
                AND data_ym=#{issue,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--查询指定期号表中的数据-->
    <select id="checkMonthData" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="java.lang.Integer">
        SELECT
        COUNT(*)
        FROM
        <!--结算明细-->
        <if test='dataType eq "1"'>
            hosp_chrg_item_detl
        </if>
        <!--药品耗材收费记录-->
        <if test='dataType eq "2"'>
            drug_mcs_rece_rcd
        </if>
        <!--医疗风险保证金提取-->
        <if test='dataType eq "3"'>
            med_rgold_exta_rcd
        </if>
        <where>
            vali_flag='1'
            <if test="issue!=null and issue!=''">
                AND data_ym=#{issue,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--药品耗材领用记录查询-->
    <select id="queryConsumableAndDrugsList"
            parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="com.jp.med.cost.modules.extract.vo.DrugMcsReceRcdVo">
        SELECT
        drug_mcs_rece_rcd_id,<!--药品领用ID-->
        medins_no,<!--医疗机构编号-->
        data_ym,<!--数据期号-->
        dept_codg,<!--领用科室编码-->
        dept_name,<!--领用科室名称-->
        med_ast_type,<!--医疗物资类别(1药品,2耗材)-->
        med_ast_subd_item,<!--疗物资细分项(11西药12中成药13中草药,2耗材)-->
        ast_codg,<!--物资编码-->
        ast_name,<!--物资名称-->
        cost_pric,<!--物资单价-->
        cost_codg,<!--成本编码-->
        cost_name,<!--成本名称-->
        rece_cnt,<!--领用数量-->
        rece_total_cost,<!--总费用-->
        is_chrg_ast,<!--是否收费物资 1是 0 否-->
        prem_prop,<!--溢价比例-->
        rece_time,<!--领用时间-->
        vali_flag<!--有效标志-->
        FROM drug_mcs_rece_rcd
        <where>
            vali_flag='1'
            <!--医疗物资类别-->
            <if test="med_ast_type!=null and med_ast_type!=''">
                AND med_ast_type=#{med_ast_type,jdbcType=VARCHAR}
            </if>
            <!--领用科室名称-->
            <if test="dept_name!=null and dept_name!=''">
                AND dept_name LIKE '%'||#{dept_name,jdbcType=VARCHAR}||'%'
            </if>
            <!--领用物资名称-->
            <if test="ast_name!=null and ast_name!=''">
                AND ast_name LIKE '%'||#{ast_name,jdbcType=VARCHAR}||'%'
            </if>
            <!--期号时间查询条件-->
            <if test="issue!=null and issue!=''">
                AND data_ym=#{issue,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--获取指定期号的药品耗材溢价比例-->
    <select id="getPremiumRatio" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="java.math.BigDecimal">
        SELECT DISTINCT prem_prop * 100
        FROM drug_mcs_rece_rcd
        WHERE vali_flag = '1'
          and data_ym = #{issue,jdbcType=VARCHAR}
          and med_ast_type = #{med_ast_type,jdbcType=VARCHAR}
    </select>

    <!--查询医疗风险基金-->
    <select id="queryMedicalFundList" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="com.jp.med.cost.modules.extract.vo.MedRgoldExtaRcdVo">
        SELECT
        med_rgold_exta_rcd_id,<!--医疗风险金提取记录ID-->
        medins_no,<!--医疗机构编号-->
        data_ym,<!--数据期号-->
        dept_codg,<!--科室编码-->
        dept_name,<!--科室名称-->
        cost_codg,<!--成本编码-->
        cost_type,<!--成本类型(1人员经费,2卫生材料费,3药品费,4固定资产折旧,5无形资产摊销6,提取医疗风险基金7,其它运行费用)-->
        cost_shape,<!--成本形态-->
        cost_contrly,<!--成本可控性-->
        cost_name,<!--成本名称-->
        rece_total_cost,<!--总成本-->
        med_rgold_exta_prop,<!--医疗风险金提取比例-->
        oprt_time,<!--日期-->
        vali_flag<!--有效标志-->
        FROM med_rgold_exta_rcd
        <where>
            vali_flag='1'
            <!--科室名称-->
            <if test="dept_name!=null and dept_name!=''">
                AND dept_name LIKE '%'||#{dept_name,jdbcType=VARCHAR}||'%'
            </if>
            <!--期号时间查询条件-->
            <if test="issue!=null and issue!=''">
                AND data_ym=#{issue,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <!--抽取医疗风险基金-->
    <select id="queryMedicalFundListFromKKK9"
            parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="com.jp.med.cost.modules.extract.vo.MedRgoldExtaRcdVo">
        SELECT
        medins_no AS medins_no,<!--医疗机构编号-->
        data_ym AS data_ym,<!--数据期号-->
        acord_dept_codg AS dept_codg,<!--科室编码-->
        acord_dept_name AS dept_name,<!--科室名称-->
        (SELECT cost_codg FROM medins_cost_item_cfg WHERE cost_name= #{cost_name,jdbcType=VARCHAR} AND vali_flag='1') AS
        cost_codg,<!--成本编码-->
        (SELECT cost_type FROM medins_cost_item_cfg WHERE cost_name= #{cost_name,jdbcType=VARCHAR} AND vali_flag='1') AS
        cost_type,<!--成本类型(1人员经费,2卫生材料费,3药品费,4固定资产折旧,5无形资产摊销6,提取医疗风险基金7,其它运行费用)-->
        (SELECT cost_shape FROM medins_cost_item_cfg WHERE cost_name= #{cost_name,jdbcType=VARCHAR} AND vali_flag='1')
        AS
        cost_shape,<!--成本形态-->
        (SELECT cost_contrly FROM medins_cost_item_cfg WHERE cost_name= #{cost_name,jdbcType=VARCHAR} AND vali_flag='1')
        AS
        cost_contrly,<!--成本可控性-->
        #{cost_name,jdbcType=VARCHAR} AS cost_name,<!--成本名称-->
        sum(sumfee)*(#{med_rgold_exta_prop,jdbcType=NUMERIC}/1000) AS rece_total_cost,<!--总成本-->
        #{med_rgold_exta_prop,jdbcType=NUMERIC} AS med_rgold_exta_prop<!--医疗风险金提取比例-->
        FROM hosp_chrg_item_detl
        <where>
            vali_flag='1'
            <!--期号时间查询条件-->
            AND data_ym=#{issue,jdbcType=VARCHAR}
        </where>
        GROUP BY data_ym,acord_dept_codg,acord_dept_name,medins_no
    </select>

    <!--获取医疗保证金提取比例-->
    <select id="getMedicalFundRatio" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="java.math.BigDecimal">
        SELECT
        DISTINCT med_rgold_exta_prop*1000<!--医疗风险金提取比例-->
        FROM med_rgold_exta_rcd
        <where>
            vali_flag='1'
            <!--期号时间查询条件-->
            AND data_ym=#{issue,jdbcType=VARCHAR}
        </where>
    </select>

    <!--查询成本配置-->
    <select id="queryKkp8List" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo">
        SELECT
        cost_codg,<!--成本编码-->
        cost_name<!--成本名称-->
        FROM medins_cost_item_cfg
        WHERE vali_flag='1'
        <if test="medins_no != null and medins_no != ''">
            AND medins_no=#{medins_no,jdbcType=VARCHAR}
        </if>
        <if test="cost_type != null and cost_type != ''">
            AND cost_type=#{cost_type,jdbcType=VARCHAR}
        </if>
        ORDER BY cost_codg ASC
    </select>

    <!-- 查询人员薪资成本数据（科室成本项） -->
    <select id="queryPersonnelExpensesList"
            parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="java.util.HashMap">
        SELECT
        dept_labr_cost_detl_id,<!--人员薪资成本id-->
        medins_no,<!--医疗机构编号-->
        data_ym,<!--期号-->
        dept_codg,<!--科室编码-->
        dept_name,<!--科室名称-->
        dept_type,<!--科室类别-->
        psn_type,<!--人员类别-->
        psncnt<!--人数-->
        FROM dept_labr_cost_detl C
        <where>
            vali_flag='1'
            <!--期号时间查询条件-->
            AND data_ym=#{issue,jdbcType=VARCHAR}
            <if test="medins_no!=null and medins_no!=''">
                AND medins_no=#{medins_no,jdbcType=VARCHAR}
            </if>
            <!--科室名称-->
            <if test="dept_name!=null and dept_name!=''">
                AND dept_name LIKE '%'||#{dept_name,jdbcType=VARCHAR}||'%'
            </if>
        </where>
    </select>

    <!--  查询科室成本明细（科室人员成本明细信息） -->
    <select id="queryPersonnelExpensesDeptDetailList"
            parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="java.util.HashMap">
        select dept_codg,labr_cost_codg,sum(cost_amt) as cost_amt FROM (
        SELECT
        medins_no,<!--医疗机构编号-->
        data_ym,<!--期号-->
        dept_codg,<!--科室编码-->
        dept_name,<!--科室名称-->
        'F'||labr_cost_codg AS labr_cost_codg,<!--成本编码-->
        labr_cost_name,<!--成本名称-->
        cost_amt
        FROM dept_labr_cost_detl a
        <where>
            vali_flag='1'
            <!--期号时间查询条件-->
            AND data_ym=#{issue,jdbcType=VARCHAR}
            <if test="medins_no!=null and medins_no!=''">
                AND medins_no=#{medins_no,jdbcType=VARCHAR}
            </if>
            <!--科室名称-->
            <if test="dept_name!=null and dept_name!=''">
                AND dept_name LIKE '%'||#{dept_name,jdbcType=VARCHAR}||'%'
            </if>
        </where>
        ) b
        group by dept_codg,labr_cost_codg
        order by dept_codg,labr_cost_codg
    </select>
    <!--    查询上一期人员经费期号-->
    <select id="checkPersonnelExpensesPreData"
            parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto" resultType="java.lang.String">
        SELECT MAX(data_ym) as issue
        FROM dept_labr_cost_detl
        WHERE data_ym  <![CDATA[ < #{issue,jdbcType=VARCHAR}]]>
    </select>

    <!--    查询所有科室-->
    <select id="queryAllDept" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="com.jp.med.cost.modules.extract.vo.MedinsDeptVo">
        select medins_dept_id,<!--医疗机构科室ID-->
        fix_medins_id,<!--定点医疗机构ID-->
        medins_no,<!--医疗机构编号-->
        dept_obj_type,<!--科室分类-->
        dept_type,<!--科室类别-->
        clnc_type,<!--临床类别-->
        dept_codg,<!--科室编码-->
        dept_name,<!--科室名称-->
        dept_resper,<!--科室负责人-->
        dept_coninfo,<!--科室联系方式-->
        dept_used_area,<!--科室使用面积-->
        bed_cnt,<!--床位数-->
        dept_lv,<!--科室层次-->
        open_date,<!--开设日期-->
        prnt_medins_dept_codg,<!--上级医疗机构科室编码-->
        is_cost_aprt,<!--是否成本分摊-->
        std_dept_codg,<!--标准科室编码-->
        std_dept_name,<!--标准科室名称-->
        is_hosp_toplv_dept,<!--是否全院顶级科室-->
        vali_flag,<!--当前有效标志-->
        opter,<!--经办人-->
        opt_time<!--经办时间-->
        from medins_dept
        where vali_flag = '1'
        <if test="dept_obj_type != null and dept_obj_type != ''">
            and dept_obj_type = #{dept_obj_type,jdbcType=VARCHAR}
        </if>
        <if test="medins_no != null and medins_no != ''">
            AND medins_no=#{medins_no,jdbcType=VARCHAR}
        </if>
    </select>

    <!--查询资产-->
    <select id="queryAssetsList" parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto"
            resultType="com.jp.med.cost.modules.extract.vo.DeptAsetAsgnRcdVo">
        SELECT
        dept_aset_cost_detl,<!--科室资产成本明细-->
        medins_no,<!--医疗机构编号-->
        data_ym,<!--上传期号-->
        dept_codg,<!--科室编码-->
        dept_name,<!--科室名称-->
        dept_type,<!--科室类别-->
        aset_codg,<!--资产编码-->
        aset_cost_codg,<!--资产成本编码-->
        aset_name,<!--资产名称-->
        cost_name,<!--成本名称-->
        cost_type,<!--成本类型-->
        aset_booked_in_time,<!--资产入账时间-->
        emp_initval,<!--单位原值-->
        cnt,<!--数量-->
        depr_ddln,<!--折旧期限-->
        depr_way,<!--折旧方式-->
        oprt_time,<!--操作时间-->
        vali_flag<!--有效标志-->
        FROM dept_aset_asgn_rcd
        <where>
            vali_flag='1'
            <!--科室名称-->
            <if test="dept_name!=null and dept_name!=''">
                AND dept_name LIKE '%'||#{dept_name,jdbcType=VARCHAR}||'%'
            </if>
            <!--期号时间查询条件-->
            <if test="issue!=null and issue!=''">
                AND upld_ym=#{issue,jdbcType=VARCHAR}
            </if>
            <!--资产名称-->
            <if test="aset_name!=null and aset_name!=''">
                AND aset_name LIKE '%'||#{aset_name,jdbcType=VARCHAR}||'%'
            </if>
        </where>
        ORDER BY aset_codg ASC
    </select>

    <!--查询其他成本-->
    <select id="queryOtherCost" resultType="com.jp.med.cost.modules.extract.vo.OthCostDetlVo"
            parameterType="com.jp.med.cost.modules.extract.dto.BasicDataTransmissionDto">
        select
        dept_oth_cost_detl_id, <!--科室其它成本明细id-->
        medins_no, <!--医疗机构编码-->
        data_ym, <!--医疗机构编码-->
        dept_codg, <!--科室编码-->
        dept_name, <!--科室名称-->
        dept_type, <!--科室类别-->
        oth_cost_codg, <!--其它成本编码-->
        oth_cost_name, <!--其它成本名称-->
        round(cost_amt,2) as cost_amt, <!--成本金额-->
        oprt_time, <!--操作时间-->
        vali_flag <!--有效标志-->
        from oth_cost_detl
        where vali_flag = '1'
        <if test="medins_no != null and medins_no != ''">
            AND medins_no=#{medins_no,jdbcType=VARCHAR}
        </if>
        <if test="issue != null and issue != ''">
            AND data_ym=#{issue,jdbcType=VARCHAR}
        </if>
        <if test="dept_name != null and dept_name != ''">
            AND dept_name like '%' || trim(#{dept_name,jdbcType=VARCHAR}) || '%'
        </if>
        <if test="none_oth_cost_codg">
            and oth_cost_codg is null
        </if>
    </select>

    <!--查询期号下是否有其他成本数据-->
    <select id="queryOtherCostNumByIssue" resultType="java.lang.Integer">
        select 1
        from none_oth_cost_codg
        where vali_flag = '1'
        <if test="medins_no != null and medins_no != ''">
            AND medins_no = #{medins_no,jdbcType=VARCHAR}
        </if>
        <if test="issue != null and issue != ''">
            AND data_ym = #{issue,jdbcType=VARCHAR}
        </if>
        limit 1
    </select>

    <select id="queryAllOtherCost" resultType="com.jp.med.cost.modules.common.vo.MedinsCostItemCfgVo">
        <!-- 全院非其他成本-->
        select distinct cost_name, 1 as key
        from medins_cost_item_cfg
        where vali_flag = '1'
        and cost_type != '7'
        and cost_name is not null
        <if test="medins_no != null and medins_no != ''">
            AND medins_no = #{medins_no,jdbcType=VARCHAR}
        </if>

        union all

        <!-- 全院其他成本-->
        select distinct cost_name, 2 as key
        from medins_cost_item_cfg
        where vali_flag = '1'
        and cost_type = '7'
        and cost_name is not null
        <if test="medins_no != null and medins_no != ''">
            AND medins_no = #{medins_no,jdbcType=VARCHAR}
        </if>
    </select>

</mapper>

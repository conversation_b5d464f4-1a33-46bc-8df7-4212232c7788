package com.jp.med.hrm.modules.push.logic.impl.wn;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.hrm.modules.emp.dto.EmpEmployeeInfoDto;
import com.jp.med.hrm.modules.emp.mapper.read.EmpEmployeeInfoReadMapper;
import com.jp.med.hrm.modules.push.api.PushData;
import com.jp.med.hrm.modules.push.dto.PushDataDto;
import com.jp.med.hrm.modules.push.enums.DataType;
import com.jp.med.hrm.modules.push.enums.Platform;
import com.jp.med.hrm.modules.push.logic.PushLogicAbs;
import com.jp.med.hrm.modules.push.selected.PlatformSelected;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 推送 员工 逻辑 实现
 *
 * <AUTHOR>
 * @date 2025/05/14
 */
@Slf4j
@Component
@ConditionalOnProperty(name = "wn.hospitalOid")
public class PushEmpLogic extends PushLogicAbs implements CommandLineRunner {
    /**
     * 员工信息mapper
     */
    @Resource
    private EmpEmployeeInfoReadMapper empEmployeeInfoReadMapper;

    @Resource(name = "wnPushEmpData")
    private PushData pushData;

    /**
     * 推送员工
     */
    @Override
    public void push() {
        List<EmpEmployeeInfoDto> empEmployeeInfoDtos = empEmployeeInfoReadMapper.selectList(
                Wrappers.lambdaQuery(EmpEmployeeInfoDto.class)
                        .isNull(EmpEmployeeInfoDto::getWnOid)
        );

        Set<Integer> empIds = empEmployeeInfoDtos.stream().map(EmpEmployeeInfoDto::getId).collect(Collectors.toSet());

        // 去查变更表
        empEmployeeInfoDtos.addAll(empEmployeeInfoReadMapper.selectModifyEmpInfoByDate(LocalDate.now().toString(), empIds));
        if (CollUtil.isEmpty(empEmployeeInfoDtos)) {
            log.info("今日无可推送的用户数据");
            return;
        }

        for (EmpEmployeeInfoDto empEmployeeInfoDto : empEmployeeInfoDtos) {
            try {
                boolean flag = StrUtil.isEmpty(empEmployeeInfoDto.getWnOid());
                String status = empEmployeeInfoReadMapper.getUserStatus(empEmployeeInfoDto.getEmpCode());
                if (status == null || status.equals("1")) {
                    status = "0";
                } else {
                    status = "1";
                }
                empEmployeeInfoDto.setEnable(status);
                boolean result = pushData.push(new PushDataDto().setEmpEmployeeInfoDto(empEmployeeInfoDto));
                if (flag && result) {
                    empEmployeeInfoReadMapper.updateById(empEmployeeInfoDto);
                }
                if (!result) {
                    log.error("推送员工数据失败，员工code：{}，员工名称：{}", empEmployeeInfoDto.getEmpCode(), empEmployeeInfoDto.getEmpName());
                }
            } catch (Exception e) {
                log.error("推送员工数据发生错误，跳过改员工，员工code：{}，员工名称：{}", empEmployeeInfoDto.getEmpCode(), empEmployeeInfoDto.getEmpName(),e);
            }
        }
    }

    @Override
    public void run(String... args) throws Exception {
        PlatformSelected.addPushLogic(Platform.WN_ZSJGLRJ, DataType.EMP, this);
    }
}

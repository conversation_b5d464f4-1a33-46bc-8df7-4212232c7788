package com.jp.med.hrm.modules.workforceMgt.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.DateUtil;
import com.jp.med.hrm.modules.workforceMgt.dto.EmpMyScduTagDto;
import com.jp.med.hrm.modules.workforceMgt.mapper.read.EmpMyScduTagReadMapper;
import com.jp.med.hrm.modules.workforceMgt.mapper.write.EmpMyScduTagWriteMapper;
import com.jp.med.hrm.modules.workforceMgt.service.write.EmpMyScduTagWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 排班标签
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-20 14:34:43
 */
@Service
@Transactional(readOnly = false)
public class EmpMyScduTagWriteServiceImpl extends ServiceImpl<EmpMyScduTagWriteMapper, EmpMyScduTagDto> implements EmpMyScduTagWriteService {

    @Autowired
    private EmpMyScduTagReadMapper empMyScduTagReadMapper;

    @Override
    public void saveTag(EmpMyScduTagDto dto) {
        dto.setId(null);
        dto.setCrter(dto.getSysUser().getHrmUser().getEmpCode());
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        dto.setOrgId(dto.getSysUser().getHrmUser().getHrmOrgId());
        //tagName不允许存在 ，,
        if (dto.getTagName().contains(",") || dto.getTagName().contains("，")) {
            throw new AppException("标签名不允许存在逗号");
        }
        //根据tagName判重
        dto.setSqlAutowiredHospitalCondition(true);
        if (empMyScduTagReadMapper.verifyTag(dto) > 0) {
            throw new AppException("当前标签已存在");
        }

        baseMapper.insert(dto);
    }
}

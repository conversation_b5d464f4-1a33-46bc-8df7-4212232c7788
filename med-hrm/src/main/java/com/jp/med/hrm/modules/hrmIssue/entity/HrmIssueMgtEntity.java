package com.jp.med.hrm.modules.hrmIssue.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 议题申报表
 * <AUTHOR>
 * @email -
 * @date 2024-11-06 16:04:44
 */
@Data
@TableName("hrm_issue_mgt")
public class HrmIssueMgtEntity {


	@TableId("id")
	private Integer id;

	/** 类型（2:党委会议题 1:院长办公会议题） */
	@TableField("type")
	private String type;

	/** 标题 */
	@TableField("title")
	private String title;

	/** 提交科室 */
	@TableField("submit_org")
	private String submitOrg;

	/** 提交时间 */
	@TableField("submit_date")
	private String submitDate;

	/** 议题名称 */
	@TableField("topic")
	private String topic;

	/** 议题内容 */
	@TableField("content")
	private String content;

	/** 列席人员 */
	@TableField("attendees")
	private String attendees;

	/** 请示事项/工作建议 */
	@TableField("suggestions")
	private String suggestions;

	/** 议题类型（一般议题/重要议题） */
	@TableField("topic_type")
	private String topicType;

	/** 决议内容 */
	@TableField("resolution_content")
	private String resolutionContent;

	/** 决议时间 */
	@TableField("resolution_time")
	private String resolutionTime;

	/** 决议签章地址 */
	@TableField("resolution_sign_path")
	private String resolutionSignPath;

	/** 是否需要提交党委会议审议 */
	@TableField("is_need_council")
	private String isNeedCouncil;

	/** 业务状态(0:待审核,1:已通过,2:未通过,3:已取消) */
	@TableField("status")
	private String status;

	/** 预算金额 */
	@TableField("budget_amount")
	private BigDecimal budgetAmount;

	/** 决议金额 */
	@TableField("resolution_amount")
	private BigDecimal resolutionAmount;

	/** 备注1 */
	@TableField("remark1")
	private String remark1;

	/** 备注2 */
	@TableField("remark2")
	private String remark2;

	/** 附件路径 */
	@TableField("att")
	private String att;

	/** 附件名称 */
	@TableField("att_name")
	private String attName;

	/** 创建人 */
	@TableField("creator")
	private String creator;

	/** 创建时间 */
	@TableField("create_time")
	private String createTime;

	/** 有效标志(1:有效,0:无效) */
	@TableField("active_flag")
	private String activeFlag;


	@TableField("hospital_id")
	private String hospitalId;

	/** 流程实例编码 */
	@TableField("process_instance_code")
	private String processInstanceCode;

}

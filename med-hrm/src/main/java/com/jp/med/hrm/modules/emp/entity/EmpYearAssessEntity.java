package com.jp.med.hrm.modules.emp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 *  年度考核
 * <AUTHOR>
 * @email -
 * @date 2023-09-15 14:18:22
 */
@Data
@TableName("hrm_year_assess")
public class EmpYearAssessEntity {


    @TableId("id")
	private Integer id;

	/** 年度 */
	@TableField("year")
	private String year;

	/** 考核结果 */
	@TableField("result")
	private String result;

	/** 备注 */
	@TableField("remark")
	private String remark;


    @TableField("status")
	private Integer status;

	/** 是否逻辑删除 */
	@TableField("is_deleted")
	private Integer isDeleted;


    @TableField("hospital_id")
	private String hospitalId;


    @TableField("emp_id")
	private Integer empId;

}

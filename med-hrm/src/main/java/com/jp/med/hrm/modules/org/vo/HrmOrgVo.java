package com.jp.med.hrm.modules.org.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023-05-04 11:12
 */
@Data
@TableName("hrm_org")
public class HrmOrgVo {
    /**
     * 组织架构ID
     */
    @TableId
    private String orgId;

    /**
     * 组织架构ID( 新增和修改时使用 )
     */
    private String organizationId;
    /**
     * 组织架构名称
     */
    private String orgName;
    /**
     * 父组织架构ID
     */
    private String orgParentId;
    /**
     * 父组织架构名字
     */
    private String orgParentName;

    /**
     * 医疗机构编号
     */
    private String hospitalId;

    /**
     * 键值
     */
    private String key;

    /**
     * 下级数量
     */
    private int lowNum;

    /**
     * 子集
     */
    private List<HrmOrgVo> children;

    /**
     * 是否可选
     */
    private Boolean disabled;

    /**
     * 有效标志
     */
    private String activeFlag;

    /**
     * 科室类型
     */
    private String orgType;


    /**
     * 更新人
     */
    private String updter;

    /**
     * 更新时间
     */
    private String updateTime;
}

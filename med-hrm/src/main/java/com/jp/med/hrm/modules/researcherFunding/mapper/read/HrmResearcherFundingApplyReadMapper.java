package com.jp.med.hrm.modules.researcherFunding.mapper.read;

import com.jp.med.hrm.modules.researcherFunding.dto.HrmResearcherFundingApplyDto;
import com.jp.med.hrm.modules.researcherFunding.vo.HrmResearcherFundingApplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 临床试验研究者经费发放申请表
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 09:08:59
 */
@Mapper
public interface HrmResearcherFundingApplyReadMapper extends BaseMapper<HrmResearcherFundingApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<HrmResearcherFundingApplyVo> queryList(HrmResearcherFundingApplyDto dto);

    List<HrmResearcherFundingApplyVo> queryListByStatus(HrmResearcherFundingApplyDto dto);
}

package com.jp.med.hrm.modules.emp.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.hrm.modules.emp.mapper.read.EmpEmployeeTypeReadMapper;
import com.jp.med.hrm.modules.emp.dto.EmpEmployeeTypeDto;
import com.jp.med.hrm.modules.emp.vo.EmpEmployeeTypeVo;
import com.jp.med.hrm.modules.emp.service.read.EmpEmployeeTypeReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EmpEmployeeTypeReadServiceImpl extends ServiceImpl<EmpEmployeeTypeReadMapper, EmpEmployeeTypeDto> implements EmpEmployeeTypeReadService {

    @Autowired
    private EmpEmployeeTypeReadMapper empEmployeeTypeReadMapper;

    @Override
    public List<EmpEmployeeTypeVo> queryList(EmpEmployeeTypeDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return empEmployeeTypeReadMapper.queryList(dto);
    }

}

package com.jp.med.hrm.modules.emp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 聘任岗位职级
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Data
@TableName("hrm_engage_level")
public class EmpEngageLevelEntity {


    @TableId("id")
	private Integer id;

	/** 聘任开始时间 */
	@TableField("start_time")
	private String startTime;

	/** 聘任结束时间 */
	@TableField("end_time")
	private String endTime;

	/** 聘任职务 */
	@TableField("engage_rank")
	private String engageRank;

	/** 岗位级别 */
	@TableField("rank_level")
	private String rankLevel;

	/** 证书编号 */
	@TableField("certificate_no")
	private String certificateNo;

	/** 聘任职位级别 */
	@TableField("engage_level")
	private String engageLevel;

	/** 工资岗位级别 */
	@TableField("job_salary_level")
	private String jobSalaryLevel;

	/** 聘任单位 */
	@TableField("engage_org")
	private String engageOrg;

	/** 津补贴岗位等级 */
	@TableField("salary_help_level")
	private BigDecimal salaryHelpLevel;

	/** 附件 */
	@TableField("file")
	private String file;

	/** 备注 */
	@TableField("remark")
	private String remark;


    @TableField("status")
	private Integer status;


    @TableField("is_deleted")
	private Integer isDeleted;


    @TableField("hospital_id")
	private String hospitalId;


    @TableField("emp_id")
	private Integer empId;

}

package com.jp.med.hrm.modules.thesisCondition.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;


@Data
@TableName("hrm_paper_page_charge")
public class HrmPaperPageChargeDto extends CommonQueryDto {

    /**
     * 主键
     */
    @TableId("id")
    private Integer id;

    /**
     * 流程实例编码
     */
    @TableField("process_instance_code")
    private String processInstanceCode;

    /**
     * 项目名称
     */
    @TableField("thesis_name")
    private String thesisName;


    /**
     * 所属部门
     */
    @TableField("department")
    private String department;


    /**
     * 主要研究者
     */
    @TableField("main_researcher")
    private String mainResearcher;



    /**
     * 有效标志
     */
    @TableField("active_flag")
    private String activeFlag;

    /**
     * 创建时间
     */
    @TableField("crte_time")
    private String crteTime;

    /**
     * 审核状态（0:待审，1:通过，2:未通过，3：已取消）
     */
    @TableField("chk_state")
    private String chkState;

    /**
     * 创建人
     */
    @TableField("crter")
    private String crter;

    /**
     * 申请部门
     */
    @TableField("apply_department")
    private String applyDepartment;


    /**
     * 流程定义Key
     */
    @TableField(exist = false)
    private String processDefinitionKey;

    /**
     * 下一个审批人
     */
    @TableField(exist = false)
    private String nextApprover;

    /**
     * 医疗机构id
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * dtojson格式
     */
    @TableField(exist = false)
    private String dtoJson;


    /**
     * 期刊名称
     */
    @TableField("journal_name")
    private String journalName;

    /**
     * 申请费用
     */
    @TableField("apply_cost")
    private String applyCost;

    /**
     * 开户银行
     */
    @TableField("bank_name")
    private String bankName;

    /**
     * 户名
     */
    @TableField("account_name")
    private String accountName;

    /**
     * 银行账号
     */
    @TableField("bank_account")
    private String bankAccount;

    /**
     * 报销状态
     */
    @TableField("status")
    private String status;

    /**
     * 签名
     */
    @TableField("sign_file")
    private String signFile;

    /**
     * 附件
     */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;
    @TableField("att")
    private String att;
    @TableField("att_name")
    private String attName;

    /**
     * 论文公共信息
     */
    @TableField(exist = false)
    private List<PaperRecordDto> paperRecords;

}

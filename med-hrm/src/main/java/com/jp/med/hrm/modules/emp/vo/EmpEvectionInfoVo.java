package com.jp.med.hrm.modules.emp.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 离岗/外出记录表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Data
public class EmpEvectionInfoVo {


    private Integer id;

	/** 离岗/外出类型 */
	private String type;

	/** 离岗/ 外出时间 */
	private String outTime;

	/** 预计返岗/返回时间 */
	private String backTime;

	/** 是否离岗/外出 */
	private String evectionType;

	/** 是否返岗/返回 */
	private String backType;

	/** 离岗/外出原因 */
	private String remark;

	/** 附件 */
	private String file;

	/** 附件名称 */
	private String fileName;

    private Integer status;


    private String hospitalId;


    private Integer empId;

}

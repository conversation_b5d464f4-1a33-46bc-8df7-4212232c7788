package com.jp.med.hrm.modules.probation.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.jp.med.common.dto.common.CommonQueryDto;
import java.util.List;
import lombok.Data;

@Data
public class ProbationDto extends CommonQueryDto {

    /**
     * 查询条件
     */
    private String orgId;

    /**
     * 查询条件
     */
    private String empCode;

    /**
     * 员工在职状态
     */
    private String status;

    /**
     * 员工ids
     */
    private List<Long> empIds;

    /**
     * 员工工号
     */
    private List<String> empCodes;

    /**
     * 试用期考核结果
     */
    @ExcelProperty("试用期考核结果")
    private String prebationaryResult;

    /**
     * 转正日期
     */
    @ExcelProperty("转正日期")
    private String confirmationDate;

    /**
     * 试用期考核情况
     */
    @ExcelProperty("试用期考核情况")
    private String prebationaryCondition;

    /**
     * 转正备注说明
     */
    @ExcelProperty("转正备注")
    private String confirmationRemark;

    /**
     * 离职日期
     */
    @ExcelProperty("离职日期")
    private String leaveDate;

    /**
     * 离职备注
     */
    @ExcelProperty("离职备注")
    private String leaveRemark;

    /**
     * 辞退日期
     */
    @ExcelProperty("辞退日期")
    private String exitDate;

    /**
     * 辞退备注
     */
    @ExcelProperty("辞退备注")
    private String exitRemark;

    /**
     * 状态变动时间
     */
    @ExcelProperty("状态变动时间")
    private String statusChgDate;

    /**
     * 状态变动时间
     */
    @ExcelProperty("状态变动备注")
    private String statusChgDscr;

}

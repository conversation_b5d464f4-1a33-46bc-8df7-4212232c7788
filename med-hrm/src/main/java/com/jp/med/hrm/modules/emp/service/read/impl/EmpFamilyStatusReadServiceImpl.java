package com.jp.med.hrm.modules.emp.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.hrm.modules.emp.mapper.read.EmpFamilyStatusReadMapper;
import com.jp.med.hrm.modules.emp.dto.EmpFamilyStatusDto;
import com.jp.med.hrm.modules.emp.vo.EmpFamilyStatusVo;
import com.jp.med.hrm.modules.emp.service.read.EmpFamilyStatusReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class EmpFamilyStatusReadServiceImpl extends ServiceImpl<EmpFamilyStatusReadMapper, EmpFamilyStatusDto> implements EmpFamilyStatusReadService {

    @Autowired
    private EmpFamilyStatusReadMapper empFamilyStatusReadMapper;

    @Override
    public List<EmpFamilyStatusVo> queryList(EmpFamilyStatusDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return empFamilyStatusReadMapper.queryList(dto);
    }

}

package com.jp.med.hrm.modules.researcherFunding.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.researcherFunding.dto.HrmResearcherFundingDistributionDto;
import com.jp.med.hrm.modules.researcherFunding.vo.HrmResearcherFundingDistributionVo;

import java.util.List;

/**
 * 临床试验研究者经费分配明细表
 * <AUTHOR>
 * @email -
 * @date 2025-02-11 09:08:58
 */
public interface HrmResearcherFundingDistributionReadService extends IService<HrmResearcherFundingDistributionDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<HrmResearcherFundingDistributionVo> queryList(HrmResearcherFundingDistributionDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<HrmResearcherFundingDistributionVo> queryPageList(HrmResearcherFundingDistributionDto dto);
}


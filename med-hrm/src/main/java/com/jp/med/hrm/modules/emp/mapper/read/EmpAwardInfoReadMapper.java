package com.jp.med.hrm.modules.emp.mapper.read;

import com.jp.med.hrm.modules.emp.dto.EmpAwardInfoDto;
import com.jp.med.hrm.modules.emp.vo.EmpAwardInfoVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 离岗/外出记录表
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Mapper
public interface EmpAwardInfoReadMapper extends BaseMapper<EmpAwardInfoDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EmpAwardInfoVo> queryList(EmpAwardInfoDto dto);
}

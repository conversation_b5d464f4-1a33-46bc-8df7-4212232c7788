package com.jp.med.hrm.modules.emp.vo;

import lombok.Data;

/**
 * 培训记录
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:36
 */
@Data
public class EmpTrainRecordVo {

	/** id */
	private Integer id;

	/** 培训开始时间 */
	private String startTime;

	/** 培训结束时间 */
	private String endTime;

	/** 进修专业 */
	private String professional;

	/** 专业类别 */
	private String professionalType;

	/** 返回时间 */
	private String backTime;

	/** 进修单位 */
	private String trainOrg;

	/** 结业证书编号 */
	private String resultNo;

	/** 是否资助 */
	private String isFund;

	/** 附件 */
	private String file;

	/** 附件名称 */
	private String fileName;

	/** 备注 */
	private String remark;


    private Integer status;


    private Integer isDeleted;

	/** 医院id */
	private String hospitalId;

	/** 员工id */
	private Integer empId;

}

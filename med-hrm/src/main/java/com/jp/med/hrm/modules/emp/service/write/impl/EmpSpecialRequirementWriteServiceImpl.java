package com.jp.med.hrm.modules.emp.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.hrm.modules.emp.mapper.write.EmpSpecialRequirementWriteMapper;
import com.jp.med.hrm.modules.emp.dto.EmpSpecialRequirementDto;
import com.jp.med.hrm.modules.emp.service.write.EmpSpecialRequirementWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 特殊资质
 * <AUTHOR>
 * @email -
 * @date 2023-09-15 14:18:22
 */
@Service
@Transactional(readOnly = false)
public class EmpSpecialRequirementWriteServiceImpl extends ServiceImpl<EmpSpecialRequirementWriteMapper, EmpSpecialRequirementDto> implements EmpSpecialRequirementWriteService {
}

package com.jp.med.hrm.modules.push.enums.wn;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 结果代码枚举类
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Getter
@AllArgsConstructor
public enum AckCode {

    /**
     * 成功
     */
    SUCCESS("100", "成功"),
    SUCCESS_WITH_DATA("100.1", "成功（有数据）"),
    SUCCESS_WITHOUT_DATA("100.2", "成功（无数据）"),

    /**
     * 客户端错误
     */
    CLIENT_ERROR("200", "客户端错误"),
    REQUEST_ROOT_ERROR("200.1", "消息根节点错误(Request节点)"),
    HEAD_NODE_ERROR("200.2", "消息头错误(Head节点)"),
    BODY_NODE_ERROR("200.3", "消息体错误(Body节点)"),
    VERSION_ERROR("200.4", "版本号缺失或错误"),
    PARAMETER_ERROR("200.5", "参数错误（具体错误详情见描述信息节点，可能包含多个参数错误）"),

    /**
     * 客户端安全类错误
     */
    CLIENT_SECURITY_ERROR("201", "客户端安全类错误"),
    ACCOUNT_OR_PASSWORD_ERROR("201.1", "帐号或密码错误"),
    ACCOUNT_EXPIRED("201.2", "帐号过期"),

    /**
     * 服务端错误
     */
    SERVER_ERROR("300", "服务端错误"),
    PROGRAM_ERROR("300.1", "程序错误（具体错误详情见描述信息节点）"),
    DATABASE_EXECUTION_ERROR("300.2", "数据库执行错误"),

    /**
     * 未知错误
     */
    UNKNOWN_ERROR("400", "未知错误"),

    /**
     * ESB错误
     */
    ESB_ERROR("500", "ESB错误"),
    ROUTING_ERROR("500.1", "路由报错"),
    CONNECTION_ERROR("500.2", "连接服务提供者系统失败");

    private final String code;
    private final String description;

}

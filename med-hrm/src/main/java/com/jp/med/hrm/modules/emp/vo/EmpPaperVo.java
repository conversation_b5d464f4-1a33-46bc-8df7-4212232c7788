package com.jp.med.hrm.modules.emp.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

/**
 * 论文信息
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:36
 */
@Data
public class EmpPaperVo {

	/** id */
	private Integer id;

	/** 论文名称 */
	private String paperName;

	/** 所属类别 */
	private String paperType;

	/** 所属学科专业类别 */
	private String professionalType;

	/** 出版社 */
	private String press;

	/** 角色 */
	private String paperRole;

	/** 期刊类型 */
	private String periodicalType;

	/** 摘要 */
	private String description;

	/**
	 * 刊号
	 */
	private String periodicalNo;

	/** 关键字 */
	private String keyword;

	/** 刊物名称 */
	private String periodicalName;

	/** 刊物期数 */
	private String periodicalPeriods;

	/** 发表时间 */
	private String publishTime;

	/** 个人在论文著作中的排名 */
	private Integer ranking;

	/** 附件 */
	private String file;

	/** 附件名称 */
	private String fileName;

    private Integer status;

	/** 备注 */
	private String remark;

	/** 医院id */
	private String hospitalId;

	/** 员工id */
	private Integer empId;

}

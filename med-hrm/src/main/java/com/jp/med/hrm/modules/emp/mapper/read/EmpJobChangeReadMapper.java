package com.jp.med.hrm.modules.emp.mapper.read;

import com.jp.med.hrm.modules.emp.dto.EmpJobChangeDto;
import com.jp.med.hrm.modules.emp.vo.EmpJobChangeVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 岗位类别变动
 * <AUTHOR>
 * @email -
 * @date 2023-09-15 14:18:22
 */
@Mapper
public interface EmpJobChangeReadMapper extends BaseMapper<EmpJobChangeDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EmpJobChangeVo> queryList(EmpJobChangeDto dto);
}

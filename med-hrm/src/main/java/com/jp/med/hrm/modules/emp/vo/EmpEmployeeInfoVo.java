package com.jp.med.hrm.modules.emp.vo;

import lombok.Data;

import java.util.List;

/**
 * 员工信息表
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Data
public class EmpEmployeeInfoVo {

    /**
     * id
     */
    private Integer id;

    /**
     * 员工编号
     */
    private String empCode;

    /**
     * 员工姓名
     */
    private String empName;

    /**
     * 职工类型
     */
    private String empType;
    private String empTypeName;
    private String empTypeValue;

    /**
     * 执业资格
     */
    private String practicingTypeName;
    private String practicingType;
    /**
     * 组织
     */
    private String hospitalId;

    /**
     * 部门
     */
    private String orgId;

    /**
     * 组织架构名称
     */
    private String orgName;
    /**
     * 父组织架构ID
     */
    private String orgParentId;
    /**
     * 父组织架构名字
     */
    private String orgParentName;

    /**
     * 职务
     */
    private String job;

    /**
     * 证件号码
     */
    private String icdCard;

    /**
     * 出生日期
     */
    private String birthday;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 联系电话1
     */
    private String phone;

    /**
     * 员工状态
     */
    private String status;

    /**
     * 员工状态说明
     */
    private String statusName;

    /**
     * 员工状态变动时间
     */
    private String statusChgDate;

    /**
     * 员工状态变动说明
     */
    private String statusChgDscr;

    /**
     * 证件类型（0身份证，1护照，2其他）
     */
    private String icdCardType;

    /**
     * 性别（1男，2女）
     */
    private String sex;

    /**
     * 各种任职状态结束时间（如转正为转正结束时间）
     */
    private String endTime;

    /**
     * 各种任职状态开始时间（如试用为试用开始时间，转正为转正开始时间）
     */
    private String startTime;

    /**
     * 参加工作时间
     */
    private String joinJobTime;

    /**
     * 血型
     */
    private String bloodType;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 婚姻状况（0:未婚、1:已婚、2:离婚、3:丧偶）
     */
    private String marriageCondition;

    /**
     * 民族
     */
    private String mz;

    /**
     * 户口类型(0:农村，1:非农村）
     */
    private String registered;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 户籍所在地
     */
    private String censusRegister;

    /**
     * 出生地
     */
    private String birthPlace;

    /**
     * 通讯地址
     */
    private String communicationAddress;

    /**
     * 爱好特长
     */
    private String hobby;

    /**
     * 健康状况
     */
    private String healthCondition;

    /**
     * 政治面貌
     */
    private String politicsStatus;

    /**
     * 工作状态（0:不可调配，1：可调配）
     */
    private String jobType;

    /**
     * 入职渠道
     */
    private String enterChannel;

    /**
     * 到本单位时间
     */
    private String toCompanyTime;

    /**
     * 黑名单
     */
    private String blacklist;

    /**
     * 头像路径
     */
    private String portraitPath;

    /**
     * 头像路径名称
     */
    private String portraitPathName;

    /**
     * 档案号
     */
    private String archiveNum;

    /**
     * 档案所在地
     */
    private String archiveLocation;

    /**
     * 个人身高
     */
    private Double personHeight;

    /**
     * 个人体重
     */
    private Double personWeight;

    /**
     * 身份证有效期
     */
    private String icdValidityDate;

    /**
     * 经费性质
     */
    private String fundsNature;

    /**
     * 人员性质
     */
    private String employNature;

    /**
     * 联系电话2
     */
    private String phone2;

    /**
     * 短号
     */
    private String sortPhone;

    /**
     * 教护身份
     */
    private String religiousIdentity;

    /**
     * 转正日期
     */
    private String confirmationDate;

    /**
     * 试用期考核结果
     */
    private String prebationaryResult;

    /**
     * 试用期考核情况
     */
    private String prebationaryCondition;

    /**
     * 身份证附件
     */
    private String icdFile;

    /**
     * 备注
     */
    private String remark;

    /**
     * 档案出生日期
     */
    private String archiveBirthday;

    /**
     * 入编时间
     */
    private String compileTime;

    /**
     * 调入时间
     */
    private String transferredTime;

    /**
     * 连续工龄认定时间
     */
    private String senioritySureTime;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 兼任科室
     */
    private List<String> ajtOrgIds;

    /**
     * 兼任科室
     */
    private String ajtOrgIdsStr;

    /**
     * 实际科室(用于科室合并)
     */
    private String oriOrg;

    /**
     * 转正备注
     */
    private String confirmationRemark;

    /**
     * 离职日期
     */
    private String leaveDate;

    /**
     * 离职备注
     */
    private String leaveRemark;

    /**
     * 辞退日期
     */
    private String exitDate;

    /**
     * 辞退备注
     */
    private String exitRemark;

    /**
     * 退休日期
     */
    private String retireDate;

    /**
     * 退休备注
     */
    private String retireRemark;

    /**
     * 年度休假总天数
     */
    private Double annualLeaveTotal;
    /**
     * 年度已休假天数
     */
    private Double annualLeaveUsed;
    /**
     * 上年度考核情况
     */
    private Double lastYearAssessment;
}

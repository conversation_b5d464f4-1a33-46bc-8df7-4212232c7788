package com.jp.med.hrm.modules.rabbitMQ;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.dto.bpm.BpmProcessInstanceStatus;
import com.jp.med.common.messsage.AbstractBpmApproveMessageHandle;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmThesisEffectivenessConditionDto;
import com.jp.med.hrm.modules.thesisCondition.mapper.read.HrmThesisEffectivenessConditionReadMapper;
import com.jp.med.hrm.modules.thesisCondition.mapper.write.HrmThesisEffectivenessConditionWriteMapper;
import com.jp.med.hrm.modules.thesisCondition.service.Write.HrmThesisEffectivenessConditionWriteService;
import com.rabbitmq.client.Channel;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 论文有效认定MQ消息通知处理
 */
@Component
@Slf4j
@Getter
@Setter
public class HrmThesisEffectivenessConditionMessageHandle extends AbstractBpmApproveMessageHandle {

    @Autowired
    HrmThesisEffectivenessConditionWriteMapper hrmThesisEffectivenessConditionWriteMapper;
    @Autowired
    HrmThesisEffectivenessConditionReadMapper hrmThesisEffectivenessConditionReadMapper;
    @Autowired
    HrmThesisEffectivenessConditionWriteService hrmThesisEffectivenessConditionWriteService;

    @Override
    public String[] getProcessIdentifier() {
        return new String[]{"PAPER_EFFECTIVENESS_CONDITION"};
    }

    @Override
    @RabbitListener(queues = {"PAPER_EFFECTIVENESS_CONDITION"})
    public void onMessage(BpmProcessInstanceStatus msg, Message message, Channel channel) throws Exception {
        super.receiveMessage0(msg);
    }

    @Override
    protected void handleCreate(BpmProcessInstanceStatus message) {

    }

    @Override
    protected void handleApproved(BpmProcessInstanceStatus message) {

        updateHrmIssueStatus(message.getBusinessKey(), MedConst.TYPE_1);
    }

    @Override
    protected void handleRejected(BpmProcessInstanceStatus message) {
        updateHrmIssueStatus(message.getBusinessKey(), MedConst.TYPE_2);
    }

    @Override
    protected void handleRunning(BpmProcessInstanceStatus message) {

    }

    @Override
    protected void handleCancelled(BpmProcessInstanceStatus message) {
        updateHrmIssueStatus(message.getBusinessKey(), MedConst.TYPE_3);
    }

    /**
     * 更新申请状态
     *
     * @param businessKey
     * @param status
     */
    private void updateHrmIssueStatus(String businessKey, String status) {
        //1.更新经费申请状态
        UpdateWrapper<HrmThesisEffectivenessConditionDto> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", Integer.valueOf(businessKey)).set("chk_state", status);
        hrmThesisEffectivenessConditionWriteMapper.update(null, updateWrapper);
    }
}

package com.jp.med.hrm.modules.emp.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.hrm.modules.emp.dto.EmpSalaryChangeDto;
import com.jp.med.hrm.modules.emp.vo.EmpSalaryChangeVo;

import java.util.List;

/**
 * 薪级变动
 * <AUTHOR>
 * @email -
 * @date 2023-09-15 14:18:22
 */
public interface EmpSalaryChangeReadService extends IService<EmpSalaryChangeDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<EmpSalaryChangeVo> queryList(EmpSalaryChangeDto dto);
}


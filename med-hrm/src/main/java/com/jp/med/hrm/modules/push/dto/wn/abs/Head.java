package com.jp.med.hrm.modules.push.dto.wn.abs;

import com.alibaba.fastjson.annotation.JSONField;
import com.jp.med.hrm.modules.push.enums.DataType;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * 头参数信息
 *
 * <AUTHOR>
 * @date 2025/05/12
 */
@Data
@Accessors(chain = true)
public abstract class Head {
    /**
     * 消息类型
     * 必填
     */
    @JSONField(name = "ContentType")
    private String ContentType;
    /**
     * 版本号（按照该版本规范开发的服务接口，该节点使用固定值：1.0） 集团化时传’GROUP’
     * 必填
     */
    @JSONField(name = "Version")
    private String Version;

    /**
     * 主数据交易代码
     * 必填
     */
    @JSONField(name = "TranCode")
    private String TranCode;

    /**
     * 数据所属院区代码【由医院现场分配】
     * 必填（根据情况传入其一即可）
     */
    @JSONField(name = "OrgIds")
    private List<String> OrgIds;


    /**
     * 发送方系统代码【长码】
     * 必填
     */
    @JSONField(name = "AppId")
    private String AppId;

    /**
     * 接收方所属院区代码【由医院现场分配】
     * 必填
     */
    @JSONField(name = "RecOrgIds")
    private List<String> RecOrgIds;


    /**
     * 接收方系统代码
     * 必填
     */
    @JSONField(name = "RecAppId")
    private String RecAppId;

    /**
     * 消息ID，建议随机生成GUID(可用于后续消息追溯)
     * 必填
     */
    @JSONField(name = "MessageId")
    private String MessageId;

    /**
     * 请求消息生成的时间戳（精确到毫秒），示例：2017-03-23 14:22:49.727
     * 必填
     */
    @JSONField(name = "Timestamp")
    private String Timestamp;

    /**
     * 定时发布时间
     */
    @JSONField(name = "PublishTimeList")
    private List<String> PublishTimeList;

    /**
     * 主数据交易代码
     * 必填
     */
    @JSONField(serialize = false, deserialize = false)
    private Map<DataType,String> TranCodes;

    public void fillTranCode(DataType dataType){
        TranCode = TranCodes.get(dataType);
    }
}

package com.jp.med.hrm.modules.emp.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.hrm.modules.emp.mapper.write.EmpEmployeeTypeWriteMapper;
import com.jp.med.hrm.modules.emp.dto.EmpEmployeeTypeDto;
import com.jp.med.hrm.modules.emp.service.write.EmpEmployeeTypeWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 员工职工类型
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Service
@Transactional(readOnly = false)
public class EmpEmployeeTypeWriteServiceImpl extends ServiceImpl<EmpEmployeeTypeWriteMapper, EmpEmployeeTypeDto> implements EmpEmployeeTypeWriteService {
}

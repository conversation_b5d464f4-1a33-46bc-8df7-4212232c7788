package com.jp.med.hrm.modules.org.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.hrm.modules.org.dto.HrmOrgDto;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023-05-04 11:10
 */
@Mapper
public interface HrmOrgWriteMapper extends BaseMapper<HrmOrgDto> {
    /**
     * 添加组织架构
     * @param dto
     */
    void insertSysOrg(HrmOrgDto dto);

    /**
     * 修改组织架构
     * @param dto
     */
    void updateSysOrg(HrmOrgDto dto);

    /**
     * 删除组织架构
     * @param dto
     */
    void deleteSysOrg(HrmOrgDto dto);

    /**
     * 删除组织架构-子集
     * @param dto
     */
    void deleteSysOrgChildren(HrmOrgDto dto);
}

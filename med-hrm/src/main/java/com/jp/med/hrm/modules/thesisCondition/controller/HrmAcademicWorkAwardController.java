package com.jp.med.hrm.modules.thesisCondition.controller;


import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.hrm.modules.thesisCondition.dto.HrmAcademicWorkAwardDto;
import com.jp.med.hrm.modules.thesisCondition.service.Write.HrmAcademicWorkAwardWriteService;
import com.jp.med.hrm.modules.thesisCondition.service.read.HrmAcademicWorkAwardReadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

@RequestMapping("academicWorkAward")
@RestController
@Api(value = "学术著作奖励申请" , tags = "学术著作奖励申请")
public class HrmAcademicWorkAwardController {

    @Autowired
    HrmAcademicWorkAwardWriteService hrmAcademicWorkAwardWriteService;
    @Autowired
    HrmAcademicWorkAwardReadService hrmAcademicWorkAwardReadService;

    @ApiOperation("新增申请")
    @PostMapping("save")
    public CommonResult<?> save(HrmAcademicWorkAwardDto dto){
        hrmAcademicWorkAwardWriteService.saveThesis(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询申请表")
    @PostMapping("/pageQueryThesis")
    public CommonResult<?> list(@RequestBody HrmAcademicWorkAwardDto dto) {
        return CommonResult.paging(hrmAcademicWorkAwardReadService.queryPageList(dto));
    }

    @ApiOperation("根据id查询(详情)")
    @GetMapping("/getBpmDetailById")
    public CommonResult<?> getBpmDetailById(@RequestParam Integer id) {
        return CommonResult.success(hrmAcademicWorkAwardReadService.getBpmDetailById(id));
    }

}

package com.jp.med.hrm.modules.workforceMgt.tablePolicy;

import cn.hutool.core.collection.CollectionUtil;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.Rows;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.deepoove.poi.policy.TableRenderPolicy;
import com.deepoove.poi.util.TableTools;
import com.jp.med.hrm.modules.workforceMgt.dto.MyScduPdfDto;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STJc;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description //word表格排班填充策略
 * <AUTHOR>
 * @Date 2024/1/15 21:45
 */
public class ScudDetailTablePolicy extends DynamicTableRenderPolicy {


    @Override
    public void render(XWPFTable table, Object data) throws Exception {
        String styleID = table.getStyleID();
        int startRow = 0;
        if (null == data) return;
        List<RowRenderData> detailData = (List<RowRenderData>) data;
        if (CollectionUtil.isNotEmpty(detailData)) {
            for (int i = 0; i < detailData.size(); i++) {
                //根据数据长度创建对应行数
                if (i > 2) {
                    XWPFTableRow insertNewTableRow = table.insertNewTableRow(i);
                    insertNewTableRow.setHeight(32);
                    for (int j = 0; j < 19; j++) {
                        //根据列的数量创建对应单元格
                        insertNewTableRow.createCell();
                    }
                }
                // 单行渲染
                TableRenderPolicy.Helper.renderRow(table.getRow(i), detailData.get(i));
            }
            // 合并单元格

            //日期
            TableTools.mergeCellsHorizonal(table, 0, 1, 2);
            TableTools.mergeCellsHorizonal(table, 0, 2, 3);
            TableTools.mergeCellsHorizonal(table, 0, 3, 4);
            TableTools.mergeCellsHorizonal(table, 0, 4, 5);
            TableTools.mergeCellsHorizonal(table, 0, 5, 6);
            TableTools.mergeCellsHorizonal(table, 0, 6, 7);
            TableTools.mergeCellsHorizonal(table, 0, 7, 8);
            //周
            TableTools.mergeCellsHorizonal(table, 1, 1, 2);
            TableTools.mergeCellsHorizonal(table, 1, 2, 3);
            TableTools.mergeCellsHorizonal(table, 1, 3, 4);
            TableTools.mergeCellsHorizonal(table, 1, 4, 5);
            TableTools.mergeCellsHorizonal(table, 1, 5, 6);
            TableTools.mergeCellsHorizonal(table, 1, 6, 7);
            TableTools.mergeCellsHorizonal(table, 1, 7, 8);
            //备注
            TableTools.mergeCellsVertically(table, 8, 0, 2);

            table.setStyleID(styleID);
        }
    }
}

package com.jp.med.hrm.modules.employeeDict.vo;

import com.jp.med.common.interceptors.BaseTree;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import lombok.Data;

/**
 * 员工字典信息
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-20 19:10:33
 */
@Data
public class EmpEmployeeDictVo implements BaseTree<Integer, EmpEmployeeDictVo> {

    /**
     * $column.comments
     */
    private Integer id;

    /**
     * 标签名
     */
    private String codeLable;

    /**
     * 标签名值  id-->label-->value  20->中国建设银行->12011
     */
    private String codeValue;

    /**
     * 父标签名
     */
    private String parentCodeLabel;

    /**
     * 类型
     */
    private String codeType;

    /**
     * 排序
     */
    private Integer codeSort;

    /**
     * 父ID
     */
    private Integer parentId;

    private List<EmpEmployeeDictVo> children;

    /**
     * 备注
     */
    private String codeRemark;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String lastUpdateBy;

    /**
     * 上次更新时间
     */
    private Date lastUpdateTime;

    private Integer key;

    /**
     * 岗位工资对应绩效标准
     * @param code
     */
    private String performanceId;

    @Override
    public void setCode(Integer code) {
        this.id = code;
    }

    @Override
    public Integer getCode() {
        return this.id;
    }

    @Override
    public Integer getPid() {
        return this.parentId;
    }

    @Override
    public void setPid(Integer pid) {
        this.parentId = pid;
    }

    @Override
    public void addChild(EmpEmployeeDictVo node) {
        if (Objects.isNull(this.children)) {
            this.children = new ArrayList<>();
        }
        this.children.add(node);
    }
}

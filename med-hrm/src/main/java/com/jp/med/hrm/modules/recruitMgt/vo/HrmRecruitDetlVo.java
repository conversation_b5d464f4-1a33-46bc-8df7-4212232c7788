package com.jp.med.hrm.modules.recruitMgt.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 招聘填报详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 20:59:31
 */
@Data
public class HrmRecruitDetlVo {

	/** ID */
	private Integer id;

	/** 招聘任务ID */
	private Integer pubdId;

	/** 数据JSON */
	private String dataStr;

	/** 招聘结果 */
	private String recruitRslt;

	/** 姓名 */
	private String name;

	/** 性别 */
	private String sex;

	/** 年龄 */
	private Integer age;

	/** 电话 */
	private String phone;

	/** 邮箱 */
	private String email;

	/** 创建时间 */
	private String createTime;

	/** 医疗机构id */
	private String hospitalId;

}

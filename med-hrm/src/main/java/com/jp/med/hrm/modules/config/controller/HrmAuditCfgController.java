package com.jp.med.hrm.modules.config.controller;

import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.hrm.modules.config.dto.HrmAuditCfgDto;
import com.jp.med.hrm.modules.config.service.read.HrmAuditCfgReadService;
import com.jp.med.hrm.modules.config.service.write.HrmAuditCfgWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 审核配置表
 * <AUTHOR>
 * @email -
 * @date 2023-09-27 17:25:49
 */
@Api(value = "审核配置表", tags = "审核配置表")
@RestController
@RequestMapping("hrmAuditCfg")
public class HrmAuditCfgController {

    @Autowired
    private HrmAuditCfgReadService hrmAuditCfgReadService;

    @Autowired
    private HrmAuditCfgWriteService hrmAuditCfgWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询审核配置表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody HrmAuditCfgDto dto){
        return CommonResult.success(hrmAuditCfgReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增审核配置表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody HrmAuditCfgDto dto){
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        hrmAuditCfgWriteService.save(dto);
        // 保存审核明细
        hrmAuditCfgWriteService.saveDetail(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改审核配置表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody HrmAuditCfgDto dto){
        hrmAuditCfgWriteService.updateById(dto);
        return CommonResult.success();
    }

    @ApiOperation("修改详情")
    @PutMapping("/updateDetails")
    public CommonResult<?> updateDetails(@RequestBody HrmAuditCfgDto dto){
        hrmAuditCfgWriteService.updateDetails(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除审核配置表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody HrmAuditCfgDto dto){
        hrmAuditCfgWriteService.removeById(dto);
        hrmAuditCfgWriteService.deleteDetailsById(dto);
        return CommonResult.success();
    }

}

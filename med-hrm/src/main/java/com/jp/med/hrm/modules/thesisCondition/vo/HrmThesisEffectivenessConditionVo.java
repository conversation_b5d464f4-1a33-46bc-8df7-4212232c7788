package com.jp.med.hrm.modules.thesisCondition.vo;

import com.jp.med.hrm.modules.thesisCondition.dto.PaperRecordDto;
import com.jp.med.hrm.modules.thesisCondition.entity.FirstAuthorEntity;
import com.jp.med.hrm.modules.thesisCondition.entity.CoAuthorEntity;
import lombok.Data;

import java.util.List;
import java.util.Date;

@Data
public class HrmThesisEffectivenessConditionVo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 流程实例编码
     */
    private String processInstanceCode;

    /**
     * 项目名称
     */
    private String thesisName;


    /**
     * 所属部门
     */
    private String department;

    /**
     * 所属部门Name
     */
    private String departmentName;

    /**
     * 主要研究者
     */
    private String mainResearcher;

    private String mainResearcherName;

    /**
     * 认定年份
     */
    private String conditionYear;

    /**
     * 期刊名称
     */
    private String journalName;

    /**
     * 刊号
     */
    private String serialNumber;

    /**
     *  主管单位
     */
    private String supervisor;

    /**
     *  主办单位
     */
    private String hostUnit;

    /**
     * 有效标志
     */
    private String activeFlag;

    /**
     * 创建时间
     */
    private String crteTime;

    /**
     * 审核状态（0:待审，1:通过，2:未通过，3：已取消）
     */
    private String chkState;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 申请部门
     */
    private String applyDepartment;


    /**
     * 流程定义Key
     */
    private String processDefinitionKey;

    /**
     * 下一个审批人
     */
    private String nextApprover;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    /**
     * dtojson格式
     */
    private String dtoJson;

    /**
     * 期刊数据库
     */
    private String journalDatabase;

    /**
     * 期刊等级
     */
    private String journalLevel;

    /**
     * 论文类别
     */
    private String paperType;

    /**
     * 论文级别
     */
    private String paperLevel;

    /**
     * 签名url
     */
    private String signFile;

    /**
     * 附件
     */
    private String att;
    private String attName;

    /**
     * 报销状态
     */
    private String status;

    private List<PaperRecordDto> paperRecords;

    /**
     * 科研产出
     */
    private String projectName;

    /**
     * issn
     */
    private String serialNumberIssn;

    /**
     * cn
     */
    private String serialNumberCn;

    /**
     * 出版日期
     */
    private Date publicationDate;

    /**
     * 卷号
     */
    private String volumeNumber;

    /**
     * 期号
     */
    private String issueNumber;

    /**
     * 影响因子
     */
    private String impactFactor;

    /**
     * 第一作者
     */
    private List<FirstAuthorEntity> firstAuthor;

    /**
     * 通讯作者
     */
    private List<CoAuthorEntity> coAuthor;
}

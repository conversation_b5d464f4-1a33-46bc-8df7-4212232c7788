package com.jp.med.hrm.modules.emp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 紧急联系人
 * <AUTHOR>
 * @email -
 * @date 2023-09-13 11:12:37
 */
@Data
@TableName("hrm_emergency_contact")
public class EmpEmergencyContactEntity {


    @TableId("id")
	private Integer id;

	/** 姓名 */
	@TableField("name")
	private String name;

	/** 性别 */
	@TableField("sex")
	private String sex;

	/** 与本人关系 */
	@TableField("relation")
	private String relation;

	/** 联系电话 */
	@TableField("telephone")
	private String telephone;

	/** 联系地址 */
	@TableField("address")
	private String address;

	/** 备注 */
	@TableField("remark")
	private String remark;


    @TableField("status")
	private Integer status;

	/** 是否逻辑删除 */
	@TableField("is_deleted")
	private Integer isDeleted;


    @TableField("emp_id")
	private Integer empId;


    @TableField("hospital_id")
	private String hospitalId;

}

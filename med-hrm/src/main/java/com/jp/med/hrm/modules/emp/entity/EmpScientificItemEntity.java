package com.jp.med.hrm.modules.emp.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 科研项目
 * <AUTHOR>
 * @email -
 * @date 2024-01-18 11:14:30
 */
@Data
@TableName("hrm_scientific_item")
public class EmpScientificItemEntity {


    @TableId("id")
	private Long id;

	/** 结题日期 */
	@TableField("end_time")
	private String endTime;

	/** 项目名称 */
	@TableField("item_name")
	private String itemName;

	/** 批准单位 */
	@TableField("aprv_emp")
	private String aprvEmp;

	/** 项目负责人 */
	@TableField("item_resper")
	private String itemResper;

	/** 本人排名 */
	@TableField("my_rank")
	private String myRank;

	/** 备注 */
	@TableField("remark")
	private String remark;

	/** 附件 */
	@TableField("att")
	private String att;

	/** 附件名称 */
	@TableField("att_name")
	private String attName;

	/** 职工id */
	@TableField("emp_id")
	private Long empId;

	/** 是否逻辑删除 */
	@TableField("is_deleted")
	private Long isDeleted;

	/** 医疗机构id */
	@TableField("hospital_id")
	private String hospitalId;

}

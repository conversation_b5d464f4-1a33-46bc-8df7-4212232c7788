package com.jp.med.hrm.modules.org.service.write.impl;

import com.alibaba.nacos.common.utils.CollectionUtils;
import com.alibaba.nacos.common.utils.StringUtils;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.hrm.modules.org.dto.HrmOrgDto;


import com.jp.med.hrm.modules.org.mapper.write.HrmOrgWriteMapper;

import com.jp.med.hrm.modules.org.service.write.HrmOrgWriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @description:
 * @date 2023-05-04 11:32
 */
@Service
public class HrmOrgWriteServiceImpl extends ServiceImpl<HrmOrgWriteMapper, HrmOrgDto> implements HrmOrgWriteService {
    @Autowired
    private HrmOrgWriteMapper hrmOrgWriteMapper;

    @Override
    public void insertSysOrg(HrmOrgDto dto) {
        dto.setActiveFlag(MedConst.ACTIVE_FLAG_1);
        String empCode = dto.getSysUser().getHrmUser().getEmpCode();
        if (StringUtils.isBlank(empCode)) {
            empCode = dto.getSysUser().getUsername();
        }
        dto.setUpdter(empCode);
        dto.setUpdateTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        hrmOrgWriteMapper.insertSysOrg(dto);
    }

    @Override
    public void updateSysOrg(HrmOrgDto dto) {
        String empCode = dto.getSysUser().getHrmUser().getEmpCode();
        if (StringUtils.isBlank(empCode)) {
            empCode = dto.getSysUser().getUsername();
        }
        dto.setUpdter(empCode);
        dto.setUpdateTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
        hrmOrgWriteMapper.updateSysOrg(dto);
    }

    @Override
    public void deleteSysOrg(HrmOrgDto dto) {
        // 先删除子集
        hrmOrgWriteMapper.deleteSysOrgChildren(dto);
        hrmOrgWriteMapper.deleteSysOrg(dto);
    }

    @Override
    public void uploadData(HrmOrgDto dto) {
//        SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH,false);
//        MultipartFile file = dto.getFile();
//        try {
//            List<HrmOrgDto> HrmOrgDtos = EasyPoiUtil.importExcel(file, HrmOrgDto.class);
//            List<SysOrgEntity> orgEntities = sysOrgReadMapper.queryOrg(dto);
//            Map<String, List<SysOrgEntity>> collect = orgEntities.stream().collect(Collectors.groupingBy(SysOrgEntity::getOrgId));
//            hrmOrgWriteMapper mapper = sqlSession.getMapper(hrmOrgWriteMapper.class);
//            int count = HrmOrgDtos.size();
//            int all = 0;
//            for (int i = 0; i < HrmOrgDtos.size(); i++) {
//                HrmOrgDto HrmOrgDto = HrmOrgDtos.get(i);
//                if (StringUtils.isEmpty(HrmOrgDto.getOrgParentId())){
//                    HrmOrgDto.setOrgParentId(dto.getHospitalId());
//                }
//                HrmOrgDto.setHospitalId(dto.getHospitalId());
//                List<SysOrgEntity> sysOrgEntities = collect.get(HrmOrgDto.getOrgId());
//                if (CollectionUtils.isEmpty(sysOrgEntities) || sysOrgEntities.size() == 0){
//                    all++;
//                    mapper.insertSysOrg(HrmOrgDto);
//                    if (i % 1000 == 0 || all == count){
//                        sqlSession.commit();
//                        sqlSession.clearCache();
//                    }
//                }else {
//                    count--;
//                }
//            }
//        }catch (Exception e){
//            sqlSession.rollback();
//            sqlSession.close();
//            e.printStackTrace();
//        }finally {
//            sqlSession.close();
//        }
    }

    @Override
    public void graphModelDataSave(HrmOrgDto dto) {
        if (CollectionUtils.isNotEmpty(dto.getDeleteList()) && CollectionUtils.isNotEmpty(dto.getAddList())) {
            String empCode = dto.getSysUser().getHrmUser().getEmpCode();
            if (StringUtils.isBlank(empCode)) {
                empCode = dto.getSysUser().getUsername();
            }
            String finalEmpCode = empCode;
            dto.getAddList().forEach(item -> {
                item.setUpdter(finalEmpCode);
                item.setUpdateTime(DateUtil.getCurrentTime("yyyy-MM-dd HH:mm:ss"));
            });
            BatchUtil.batch("deleteSysOrg", dto.getDeleteList(), HrmOrgWriteMapper.class);
            BatchUtil.batch("insertSysOrg", dto.getAddList(), HrmOrgWriteMapper.class);
        }
    }
}

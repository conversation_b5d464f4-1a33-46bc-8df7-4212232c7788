package com.jp.med.hrm.modules.probation.vo;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
/**
 * 试用期信息
 */
@Getter
@Setter
public class ProbationVo implements Serializable {
    /** 序号 */
    private Integer id;

    /** 员工编号 */
    private String empCode;

    /** 姓名 */
    private String empName;

    /** 性别 */
    private String sex;

    /** 年龄 */
    private String age;

    /** 岗位 */
    private String job;

    /** 出生日期 */
    private String birthday;

    /** 直接所属上级部门*/
    private String orgName;

    /** 试用状态 */
    private String status;

    /** 身份证号 */
    private String icdCard;

    /** 电话 */
    private String phone;

    /**籍贯*/
    private String homeTown;

    /** 开始时间 */
    private String startTime;

    /** 结束时间 */
    private String endTime;

    /** 民族 */
    private String mz;

}

package com.jp.med.hrm.modules.recruitMgt.controller;

import cn.hutool.core.collection.CollectionUtil;
import com.jp.med.common.constant.MedConst;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.DateUtil;
import com.jp.med.hrm.modules.recruitMgt.mapper.write.HrmRecruitDetlWriteMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.hrm.modules.recruitMgt.dto.HrmRecruitDetlDto;
import com.jp.med.hrm.modules.recruitMgt.service.read.HrmRecruitDetlReadService;
import com.jp.med.hrm.modules.recruitMgt.service.write.HrmRecruitDetlWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

import java.util.ArrayList;
import java.util.List;


/**
 * 招聘填报详情
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 20:59:31
 */
@Api(value = "招聘填报详情", tags = "招聘填报详情")
@RestController
@RequestMapping("hrmRecruitDetl")
public class HrmRecruitDetlController {

    @Autowired
    private HrmRecruitDetlReadService hrmRecruitDetlReadService;

    @Autowired
    private HrmRecruitDetlWriteService hrmRecruitDetlWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询招聘填报详情")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody HrmRecruitDetlDto dto){
        return CommonResult.success(hrmRecruitDetlReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增招聘填报详情")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody HrmRecruitDetlDto dto){
        dto.setCreateTime(DateUtil.getCurrentTime(null));
        hrmRecruitDetlWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改招聘填报详情")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody HrmRecruitDetlDto dto){
        if (CollectionUtil.isNotEmpty(dto.getIds())) {
            List<HrmRecruitDetlDto> dtos = new ArrayList<>();
            dto.getIds().forEach(id -> {
                HrmRecruitDetlDto d = new HrmRecruitDetlDto();
                d.setRecruitRslt(dto.getRecruitRslt());
                d.setId(id);
                dtos.add(d);
            });
            BatchUtil.batch("updateById", dtos, HrmRecruitDetlWriteMapper.class);
            return CommonResult.success();
        }
        hrmRecruitDetlWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除招聘填报详情")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody HrmRecruitDetlDto dto){
        hrmRecruitDetlWriteService.removeById(dto);
        return CommonResult.success();
    }

}

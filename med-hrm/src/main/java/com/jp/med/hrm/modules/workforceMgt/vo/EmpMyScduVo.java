package com.jp.med.hrm.modules.workforceMgt.vo;

import lombok.Data;

/**
 * 我的排班
 *
 * <AUTHOR>
 * @email -
 * @date 2023-09-20 14:26:06
 */
@Data
public class EmpMyScduVo {

    /**
     * id
     */
    private Long id;

    /**
     * 职工id
     */
    private Integer empId;

    /**
     * 员工名称
     */
    private String empName;

    /**
     * 聘任职称
     */
    private String engageRank;

    /**
     * 执业资格
     */
    private String practicingType;

    /**
     * 执业资格
     */
    private String practicingTypeLabel;

    /**
     * 员工编码
     */
    private String empCode;

    /**
     * 排班日期
     */
    private String scduDate;

    /**
     * 排班标签ID
     */
    private String scduTagId;


    /**
     * 请假类型
     */
    private String leaveType;

    /**
     * 开始时间
     */
    private String beginTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 创建人
     */
    private String crter;

    /**
     * 创建时间
     */
    private String createTime;

    /**
     * 医疗机构id
     */
    private String hospitalId;

    /**
     * 有效标准
     */
    private String activeFlag;

    /**
     * 排序
     */
    private Long sort;

}

package com.jp.med.hrm.modules.emp.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.hrm.modules.emp.mapper.read.HrmPaymentInfoReadMapper;
import com.jp.med.hrm.modules.emp.dto.HrmPaymentInfoDto;
import com.jp.med.hrm.modules.emp.vo.HrmPaymentInfoVo;
import com.jp.med.hrm.modules.emp.service.read.HrmPaymentInfoReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class HrmPaymentInfoReadServiceImpl extends ServiceImpl<HrmPaymentInfoReadMapper, HrmPaymentInfoDto> implements HrmPaymentInfoReadService {

    @Autowired
    private HrmPaymentInfoReadMapper hrmPaymentInfoReadMapper;

    @Override
    public List<HrmPaymentInfoVo> queryList(HrmPaymentInfoDto dto) {
        return hrmPaymentInfoReadMapper.queryList(dto);
    }

    @Override
    public List<HrmPaymentInfoVo> queryPageList(HrmPaymentInfoDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return hrmPaymentInfoReadMapper.queryList(dto);
    }

}

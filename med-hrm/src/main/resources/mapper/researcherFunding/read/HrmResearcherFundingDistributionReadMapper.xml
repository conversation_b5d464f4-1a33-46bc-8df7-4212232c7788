<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.researcherFunding.mapper.read.HrmResearcherFundingDistributionReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.researcherFunding.vo.HrmResearcherFundingDistributionVo" id="researcherFundingDistributionMap">
        <result property="id" column="id"/>
        <result property="applyId" column="apply_id"/>
        <result property="orgName" column="org_name"/>
        <result property="empCode" column="emp_code"/>
        <result property="empName" column="emp_name"/>
        <result property="amount" column="amount"/>
        <result property="remark" column="remark"/>
        <result property="crter" column="crter"/>
        <result property="crteTime" column="crte_time"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.researcherFunding.vo.HrmResearcherFundingDistributionVo">
        select
            id as id,
            apply_id as applyId,
            org_name as orgName,
            emp_code as empCode,
            emp_name as empName,
            amount as amount,
            remark as remark,
            crter as crter,
            crte_time as crteTime,
            active_flag as activeFlag,
            hospital_id as hospitalId
        from hrm_researcher_funding_distribution
    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.thesisCondition.mapper.read.HrmThesisEffectivenessConditionReadMapper">
    <select id="queryList" resultType="com.jp.med.hrm.modules.thesisCondition.vo.HrmThesisEffectivenessConditionVo">
        select a.id                                                             as id,
        a.process_instance_code                                          as processInstanceCode,
        a.thesis_name                                                   as thesisName,
        a.department                                                     as department,
        org2.org_name                                                    as departmentName,
        a.main_researcher                                                as mainResearcher,
        (SELECT STRING_AGG(emp_name, ', ')
        FROM hrm_employee_info
        WHERE emp_code = ANY (string_to_array(a.main_researcher, ','))) AS mainResearcherName,
        a.condition_year                                                as conditionYear,
        a.journal_name                                                  as journalName,
        a.serial_number                                                  as serialN<PERSON>ber,
        a.supervisor                                                     as supervisor,
        a.host_unit                                                      as hostUnit,
        a.chk_state                                                      as chkState,
        a.crter                                                          as crter,
        emp.emp_name                                                     as crterName,
        a.crte_time                                                      as crteTime,
        a.active_flag                                                    as activeFlag,
        a.hospital_id                                                    as hospitalId,
        a.sign_file as signFile,
        a.att as att,
        a.att_name as attName,
        a.status as status
        from hrm_researcher_thesis_effectiveness_condition a
        left join hrm_org org on a.apply_department = org.org_id
        left join hrm_employee_info emp on a.crter = emp.emp_code
        left join hrm_org org2 on a.department = org2.org_id
        <where>
            <if test="id != null">
                and a.id = #{id}
            </if>
            <if test="processInstanceCode != null and processInstanceCode != ''">
                and a.process_instance_code = #{processInstanceCode}
            </if>
            <if test="thesisName != null and thesisName != ''">
                and a.thesis_name = #{thesisName}
            </if>
            <if test="department != null and department != ''">
                and a.department = #{department}
            </if>
            <if test="chkState != null and chkState != ''">
                and a.chk_state = #{chkState}
            </if>
            <if test="curSysOrgId != null and curSysOrgId != ''">
                and a.apply_department = #{curSysOrgId}
            </if>
            <if test="curEmpCode != null and curEmpCode != ''">
                and a.crter = #{curEmpCode}
            </if>
            <if test="status != null and status != ''">
                and a.status = #{status}
            </if>
            and a.active_flag = '1'
        </where>
        order by a.crte_time desc
    </select>

    <resultMap id="paperThesis" type="com.jp.med.hrm.modules.thesisCondition.dto.HrmThesisEffectivenessConditionDto">
        <id column="id" property="id"/>
        <result property="processInstanceCode" column="process_instance_code"/>
        <result property="thesisName" column="thesis_name"/>
        <result property="department" column="department"/>
        <result property="mainResearcher" column="main_researcher"/>
        <result property="conditionYear" column="condition_year"/>
        <result property="journalName" column="journal_name"/>
        <result property="serialNumber" column="serial_number"/>
        <result property="supervisor" column="supervisor"/>
        <result property="hostUnit" column="host_unit"/>
        <result property="activeFlag" column="active_flag"/>
        <result property="crteTime" column="crte_time"/>
        <result property="chkState" column="chk_state"/>
        <result property="crter" column="crter"/>
        <result property="applyDepartment" column="apply_department"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="signFile" column="sign_file"/>
        <result property="att" column="att"/>
        <result property="attName" column="att_name"/>
        <result property="journalDatabase" column="journal_database"/>
        <result property="journalLevel" column="journal_level"/>
        <result property="paperType" column="paper_type"/>
        <result property="paperLevel" column="paper_level"/>
        <result property="paperClassification" column="paper_classification"/>
        <result property="projectName" column="project_name"/>
        <result property="serialNumberIssn" column="serial_number_issn"/>
        <result property="serialNumberCn" column="serial_number_cn"/>
        <result property="publicationDate" column="publication_date"/>
        <result property="volumeNumber" column="volume_number"/>
        <result property="issueNumber" column="issue_number"/>
        <result property="impactFactor" column="impact_factor"/>
        <result property="isStatus" column="is_status"/>
        <result property="journalNameAudit" column="journal_name_audit"/>
        <result property="serialNumberIssnAudit" column="serial_number_issn_audit"/>
        <result property="serialNumberCnAudit" column="serial_number_cn_audit"/>
        <result property="supervisorAudit" column="supervisor_audit"/>
        <result property="hostUnitAudit" column="host_unit_audit"/>
        <result property="publicationDateAudit" column="publication_date_audit"/>
        <result property="volumeNumberAudit" column="volume_number_audit"/>
        <result property="issueNumberAudit" column="issue_number_audit"/>
        <result property="journalDatabaseAudit" column="journal_database_audit"/>
        <result property="journalLevelAudit" column="journal_level_audit"/>
        <result property="paperTypeAudit" column="paper_type_audit"/>
        <result property="paperLevelAudit" column="paper_level_audit"/>
        <result property="paperClassificationAudit" column="paper_classification_audit"/>   
        <result property="impactFactorAudit" column="impact_factor_audit"/>
        <collection property="paperRecords" ofType="com.jp.med.hrm.modules.thesisCondition.dto.PaperRecordDto">
            <id column="b_id" property="id"/>
            <result column="b_processInstanceCode" property="processInstanceCode"/>
            <result column="b_department" property="department"/>
            <result column="b_mainResearcher" property="mainResearcherName"/>
            <result column="b_thesisName" property="thesisName"/>
        </collection>
        <collection property="firstAuthor" ofType="com.jp.med.hrm.modules.thesisCondition.entity.FirstAuthorEntity">
            <result column="c_name" property="name"/>
            <result column="c_unit" property="unit"/>
            <result column="c_phone" property="phone"/>
        </collection>
        <collection property="coAuthor" ofType="com.jp.med.hrm.modules.thesisCondition.entity.CoAuthorEntity">
            <result column="d_name" property="name"/>
            <result column="d_unit" property="unit"/>
            <result column="d_phone" property="phone"/>
        </collection>

    </resultMap>

    <select id="queryById" resultMap="paperThesis">
        SELECT
            a.id AS id,
            a.process_instance_code AS process_instance_code,
            a.thesis_name AS thesis_name,
            a.department AS department,
            a.main_researcher AS main_researcher,
            a.condition_year AS condition_year,
            a.journal_name AS journal_name,
            a.serial_number AS serial_number,
            a.supervisor AS supervisor,
            a.host_unit AS host_unit,
            a.active_flag AS active_flag,
            a.crte_time AS crte_time,
            a.chk_state AS chk_state,
            a.crter AS crter,
            a.apply_department AS apply_department,
            a.hospital_id AS hospital_id,
            a.sign_file AS sign_file,
            a.att AS att,
            a.att_name AS att_name,
            a.journal_database,
            a.paper_type,
            a.journal_level,
            a.paper_level,
            a.paper_classification,
            a.project_name,
            a.serial_number_issn,
            a.serial_number_cn,
            a.publication_date,
            a.volume_number,
            a.issue_number,
            a.impact_factor,
            a.is_status,
            a.journal_name_audit,
            a.serial_number_issn_audit,
            a.serial_number_cn_audit,
            a.supervisor_audit,
            a.host_unit_audit,
            a.publication_date_audit,   
            a.volume_number_audit,
            a.issue_number_audit,
            a.journal_database_audit,
            a.journal_level_audit,
            a.paper_type_audit,
            a.paper_level_audit,
            a.paper_classification_audit,   
            a.impact_factor_audit,
            b.id AS b_id,
            b.process_instance_code AS b_processInstanceCode,
            b.department AS b_department,
            b.main_researcher_name AS b_mainResearcher,
            b.thesis_name AS b_thesisName,
            c.name as c_name,
            c.unit as c_unit,
            c.phone as c_phone,
            d.name as d_name,
            d.unit as d_unit,
            d.phone as d_phone
        FROM hrm_researcher_thesis_effectiveness_condition a
                 LEFT JOIN hrm_researcher_paper_record b
                           ON a.process_instance_code = b.effectiveness_code
        LEFT JOIN  hrm_researcher_thesis_first_author c
                    ON a.process_instance_code = c.process_instance_code
        LEFT JOIN  hrm_researcher_thesis_co_author d
                    ON a.process_instance_code = d.process_instance_code
        WHERE a.id = #{id}
    </select>
</mapper>
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.config.mapper.write.HrmSalaryCfgWriteMapper">

    <update id="updateCfg">
        update hrm_salary_cfg
        <set>
            upter=#{upter,jdbcType=VARCHAR},
            upte_time=#{upteTime,jdbcType=VARCHAR},
            <foreach collection="list" item="item" separator=",">
                ${item.realKey} = #{item.presentValue}
            </foreach>
        </set>
        where id = #{id}
    </update>
</mapper>

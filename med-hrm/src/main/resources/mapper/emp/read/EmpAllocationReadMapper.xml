<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.read.EmpAllocationReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.emp.vo.EmpAllocationVo" id="allocationMap">
        <result property="id" column="id"/>
        <result property="allocation" column="allocation"/>
        <result property="allocationStatus" column="allocation_status"/>
        <result property="type" column="type"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="orgId" column="org_id"/>
        <result property="post" column="post"/>
        <result property="position" column="position"/>
        <result property="takeOfficeStartTime" column="take_office_start_time"/>
        <result property="takeOfficeEndTime" column="take_office_end_time"/>
        <result property="file" column="file"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="empId" column="emp_id"/>
        <result property="hospitalid" column="hospitalid"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.emp.vo.EmpAllocationVo">
        select id                as id,
               allocation        as allocation,
               allocation_status as allocationStatus,
               type              as type,
               hospital_id       as hospitalId,
               org_id            as orgId,
               post              as post,
               position as position,
                take_office_start_time as takeOfficeStartTime,
                take_office_end_time as takeOfficeEndTime,
                file as file,
                remark as remark,
                status as status,
                is_deleted as isDeleted,
                emp_id as empId
        from hrm_allocation
    </select>

</mapper>

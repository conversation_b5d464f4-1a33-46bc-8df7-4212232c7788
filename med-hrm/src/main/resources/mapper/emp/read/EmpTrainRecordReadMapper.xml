<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.read.EmpTrainRecordReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.emp.vo.EmpTrainRecordVo" id="trainRecordMap">
        <result property="id" column="id"/>
        <result property="startTime" column="start_time"/>
        <result property="endTime" column="end_time"/>
        <result property="professional" column="professional"/>
        <result property="professionalType" column="professional_type"/>
        <result property="backTime" column="back_time"/>
        <result property="trainOrg" column="train_org"/>
        <result property="resultNo" column="result_no"/>
        <result property="isFund" column="is_fund"/>
        <result property="file" column="file"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="empId" column="emp_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.emp.vo.EmpTrainRecordVo">
        select
            id as id,
            start_time as startTime,
            end_time as endTime,
            professional as professional,
            professional_type as professionalType,
            back_time as backTime,
            train_org as trainOrg,
            result_no as resultNo,
            is_fund as isFund,
            file as file,
            remark as remark,
            status as status,
            is_deleted as isDeleted,
            hospital_id as hospitalId,
            emp_id as empId
        from hrm_train_record
    </select>

</mapper>

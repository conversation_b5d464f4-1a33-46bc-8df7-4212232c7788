<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.hrm.modules.emp.mapper.read.EmpAwardInfoReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.hrm.modules.emp.vo.EmpAwardInfoVo" id="awardInfoMap">
        <result property="id" column="id"/>
        <result property="awardTime" column="award_time"/>
        <result property="awardName" column="award_name"/>
        <result property="giveOrg" column="give_org"/>
        <result property="awardLevel" column="award_level"/>
        <result property="file" column="file"/>
        <result property="remark" column="remark"/>
        <result property="status" column="status"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="empId" column="emp_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.hrm.modules.emp.vo.EmpAwardInfoVo">
        select
            id as id,
            award_time as awardTime,
            award_name as awardName,
            give_org as giveOrg,
            award_level as awardLevel,
            file as file,
            remark as remark,
            status as status,
            is_deleted as isDeleted,
            hospital_id as hospitalId,
            emp_id as empId
        from hrm_award_info
    </select>

</mapper>

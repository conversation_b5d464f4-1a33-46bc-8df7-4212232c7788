
package com.jp.med.generate.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.ComputerUtil;
import com.jp.med.generate.dao.GeneratorDao;
import com.jp.med.generate.utils.GenUtils;
import com.jp.med.generate.utils.PageUtils;
import com.jp.med.generate.utils.Query;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;
import java.net.URL;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipOutputStream;

/**
 * 代码生成器
 *
 */
@Service
public class SysGeneratorService {

    @Autowired
    private GeneratorDao generatorDao;

    @Value("${generate.to.java-file-path}")
    private String javaFilePath;

    @Value("${generate.to.xml-file-path}")
    private String xmlFilePath;

    @Value("${generate.to.file-prefix}")
    private String filePrefix;

    @Value("${generate.to.enable:true}")
    private Boolean enable;


    public PageUtils queryList(Query query) {
        Page<?> page = PageHelper.startPage(query.getPage(), query.getLimit());
        List<Map<String, Object>> list = generatorDao.queryList(query);
        int total = (int) page.getTotal();

        return new PageUtils(list, total, query.getLimit(), query.getPage());
    }

    public Map<String, String> queryTable(String tableName) {
        return generatorDao.queryTable(tableName);
    }

    public List<Map<String, String>> queryColumns(String tableName) {
        return generatorDao.queryColumns(tableName);
    }


    public byte[] generatorCode(Map<String, Object> params) {
        String[] tableNames = params.get("tables").toString().split(",");
        String genType = (String) params.get("genType");
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        ZipOutputStream zip = new ZipOutputStream(outputStream);
        for (String tableName : tableNames) {
            //查询表信息
            Map<String, String> table = queryTable(tableName);
            //查询列信息
            List<Map<String, String>> columns = queryColumns(tableName);
            //生成代码
            GenUtils.generatorCode(table, columns, zip, genType, enable);
            // 调用bat移动位置
            if (enable) {
                exeBat("bat/code_to.bat");
            }
        }

       IOUtils.closeQuietly(zip);
        return outputStream.toByteArray();
    }

    private void exeBat(String batName){
        if (StringUtils.isEmpty(javaFilePath) || StringUtils.isEmpty(xmlFilePath) || StringUtils.isEmpty(filePrefix)) {
            throw new AppException("文件路径为空");
        }
        // 获取生成后的文件路径
        String tempPath = GenUtils.class.getClassLoader().getResource("").getPath();
        tempPath = tempPath.substring(1);
        String separator = ComputerUtil.isWin() ? "\\" : "/";
        tempPath = tempPath.substring(tempPath.lastIndexOf(separator) + 1);
        // 使用 ClassLoader 获取资源文件的输入流
        InputStream inputStream = SysGeneratorService.class.getClassLoader().getResourceAsStream(batName);

        if (inputStream != null) {
            try {
                File logFile = new File(getLogPath());
                logFile.createNewFile();
                // 将资源文件内容复制到临时文件
                File tempFile = File.createTempFile("temp", ".bat");
                tempFile.deleteOnExit();
                OutputStream outputStream = new FileOutputStream(tempFile);
                byte[] buffer = new byte[1024];
                int bytesRead;
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                }

                // 关闭输入流和输出流
                inputStream.close();
                outputStream.close();

                // 创建 ProcessBuilder 对象并执行临时文件
                ProcessBuilder processBuilder = new ProcessBuilder(tempFile.getAbsolutePath(), javaFilePath, xmlFilePath, tempPath, filePrefix);

                // 将标准输出和标准错误流重定向到日志文件
                processBuilder.redirectOutput(ProcessBuilder.Redirect.appendTo(logFile));
                processBuilder.redirectErrorStream(true);

                Process process = processBuilder.start();

                // 等待执行完成
                int exitCode = process.waitFor();

                // 输出执行结果
                System.out.println("执行 BAT 文件完毕，退出码：" + exitCode);
            } catch (IOException | InterruptedException e) {
                e.printStackTrace();
            }
        } else {
            System.out.println("未找到 BAT 文件：" + batName);
        }
    }
    private String getLogPath(){
        // 使用 ClassLoader 获取资源路径
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        URL resourceUrl = classLoader.getResource("bat/batLog.txt");
        String resourcePath = "";
        if (resourceUrl != null) {
            resourcePath = resourceUrl.getPath();
            System.out.println("资源文件路径：" + resourcePath);
        } else {
            throw new AppException("无法找到log文件");
        }
        return resourcePath;
    }
}

package com.jp.med.cms.modules.common.mapper.read;

import com.jp.med.cms.modules.common.dto.CmsAuditResDto;
import com.jp.med.cms.modules.common.vo.CmsAuditResVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 审核结果表
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 09:33:58
 */
@Mapper
public interface CmsAuditResReadMapper extends BaseMapper<CmsAuditResDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsAuditResVo> queryList(CmsAuditResDto dto);
}

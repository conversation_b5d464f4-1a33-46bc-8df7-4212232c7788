package com.jp.med.cms.modules.contractMain.service.write.impl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;
import com.jp.med.cms.modules.contractMain.mapper.write.CmsMainFinanceWriteMapper;
import com.jp.med.cms.modules.contractMain.dto.CmsMainFinanceDto;
import com.jp.med.cms.modules.contractMain.service.write.CmsMainFinanceWriteService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 主体财务信息
 * <AUTHOR>
 * @email -
 * @date 2024-07-10 10:44:06
 */
@Service
@Transactional(readOnly = false)
public class CmsMainFinanceWriteServiceImpl extends ServiceImpl<CmsMainFinanceWriteMapper, CmsMainFinanceDto> implements CmsMainFinanceWriteService {
}

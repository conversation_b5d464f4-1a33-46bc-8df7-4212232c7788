package com.jp.med.cms.modules.contractManage.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 违约金赔付计划表
 * <AUTHOR>
 * @email -
 * @date 2024-08-16 11:33:28
 */
@Data
@TableName("cms_liquidated_damages")
public class CmsLiquidatedDamagesEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 合同 */
	@TableField("contract_id")
	private Integer contractId;

	/** 合同终止申请id */
	@TableField("termin_id")
	private Integer terminId;

	/** 赔付方 */
	@TableField("a_or_b")
	private String aOrB;

	/** 赔付金额 */
	@TableField("damages_amt")
	private BigDecimal damagesAmt;

	/** 当期顺序 */
	@TableField("seq")
	private Integer seq;

	/** 业务状态 */
	@TableField("business_status")
	private String businessStatus;

	/** 当期赔付金额 */
	@TableField("current_pay_amt")
	private BigDecimal currentPayAmt;

	/** 赔付期数 */
	@TableField("damages_periods")
	private Integer damagesPeriods;

	/** 当期计划赔付日期 */
	@TableField("current_pay_time")
	private String currentPayTime;

	/** 实际赔付日期 */
	@TableField("act_pay_time")
	private String actPayTime;

	/** 报销id */
	@TableField("reim_id")
	private Integer reimId;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

	/** 更新人 */
	@TableField("updtr")
	private String updtr;

	/** 更新时间 */
	@TableField("update_time")
	private String updateTime;

}

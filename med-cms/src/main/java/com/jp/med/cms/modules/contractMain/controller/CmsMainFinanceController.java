package com.jp.med.cms.modules.contractMain.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.cms.modules.contractMain.dto.CmsMainFinanceDto;
import com.jp.med.cms.modules.contractMain.service.read.CmsMainFinanceReadService;
import com.jp.med.cms.modules.contractMain.service.write.CmsMainFinanceWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 主体财务信息
 * <AUTHOR>
 * @email -
 * @date 2024-07-10 10:44:06
 */
@Api(value = "主体财务信息", tags = "主体财务信息")
@RestController
@RequestMapping("cmsMainFinance")
public class CmsMainFinanceController {

    @Autowired
    private CmsMainFinanceReadService cmsMainFinanceReadService;

    @Autowired
    private CmsMainFinanceWriteService cmsMainFinanceWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询主体财务信息")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody CmsMainFinanceDto dto){
        return CommonResult.paging(cmsMainFinanceReadService.queryPageList(dto));
    }

    /**
 * 列表
 */
    @ApiOperation("查询主体财务信息")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody CmsMainFinanceDto dto){
        return CommonResult.success(cmsMainFinanceReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增主体财务信息")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody CmsMainFinanceDto dto){
        cmsMainFinanceWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改主体财务信息")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody CmsMainFinanceDto dto){
        cmsMainFinanceWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除主体财务信息")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody CmsMainFinanceDto dto){
        cmsMainFinanceWriteService.removeById(dto);
        return CommonResult.success();
    }

}

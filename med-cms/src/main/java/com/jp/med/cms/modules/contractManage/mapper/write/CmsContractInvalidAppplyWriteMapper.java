package com.jp.med.cms.modules.contractManage.mapper.write;

import com.jp.med.cms.modules.contractManage.dto.CmsContractInvalidAppplyDto;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 合同作废申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-14 16:29:12
 */
@Mapper
public interface CmsContractInvalidAppplyWriteMapper extends BaseMapper<CmsContractInvalidAppplyDto> {
	void updateChkState(@Param("auditBchno")String bchno, @Param("chkState") String stateSuccess);
}

package com.jp.med.cms.modules.contractManage.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 合同进度追踪时间线
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 11:01:44
 */
@Data
@TableName("cms_progress_tracking")
public class CmsProgressTrackingEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 合同id */
	@TableField("contract_id")
	private Integer contractId;

	/** 标题 */
	@TableField("title")
	private String title;

	/** 进度内容 */
	@TableField("content")
	private String content;

	/** 节点类型 */
	@TableField("type")
	private String type;

	/** 时间 */
	@TableField("time")
	private String time;

	/** 跳转路径 */
	@TableField("push_url")
	private String pushUrl;

	/** 组织id */
	@TableField("hospital_id")
	private String hospitalId;

}

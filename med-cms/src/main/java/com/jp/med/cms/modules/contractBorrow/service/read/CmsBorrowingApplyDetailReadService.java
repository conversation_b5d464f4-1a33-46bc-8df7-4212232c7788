package com.jp.med.cms.modules.contractBorrow.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.contractBorrow.dto.CmsBorrowingApplyDetailDto;
import com.jp.med.cms.modules.contractBorrow.vo.CmsBorrowingApplyDetailVo;

import java.util.List;

/**
 * 合同借阅详情
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 21:10:47
 */
public interface CmsBorrowingApplyDetailReadService extends IService<CmsBorrowingApplyDetailDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsBorrowingApplyDetailVo> queryList(CmsBorrowingApplyDetailDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<CmsBorrowingApplyDetailVo> queryPageList(CmsBorrowingApplyDetailDto dto);

	List<CmsBorrowingApplyDetailVo> detailList(CmsBorrowingApplyDetailDto dto);

	String queryDocBorrowFlag(CmsBorrowingApplyDetailDto dto);
}


package com.jp.med.cms.modules.templateConfig.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.templateConfig.dto.CmsContractTemplateDto;
import com.jp.med.cms.modules.templateConfig.vo.CmsContractTemplateVo;

import java.util.List;

/**
 * 合同模板
 * <AUTHOR>
 * @email -
 * @date 2024-07-03 17:57:37
 */
public interface CmsContractTemplateReadService extends IService<CmsContractTemplateDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsContractTemplateVo> queryList(CmsContractTemplateDto dto);

    /**
 * 分页查询列表
 * @param dto
 * @return
*/
    List<CmsContractTemplateVo> queryPageList(CmsContractTemplateDto dto);
}


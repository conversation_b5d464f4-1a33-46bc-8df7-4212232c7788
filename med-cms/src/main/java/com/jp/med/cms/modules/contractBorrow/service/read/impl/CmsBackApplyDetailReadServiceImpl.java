package com.jp.med.cms.modules.contractBorrow.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.cms.modules.contractBorrow.mapper.read.CmsBackApplyDetailReadMapper;
import com.jp.med.cms.modules.contractBorrow.dto.CmsBackApplyDetailDto;
import com.jp.med.cms.modules.contractBorrow.vo.CmsBackApplyDetailVo;
import com.jp.med.cms.modules.contractBorrow.service.read.CmsBackApplyDetailReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class CmsBackApplyDetailReadServiceImpl extends ServiceImpl<CmsBackApplyDetailReadMapper, CmsBackApplyDetailDto> implements CmsBackApplyDetailReadService {

    @Autowired
    private CmsBackApplyDetailReadMapper cmsBackApplyDetailReadMapper;

    @Override
    public List<CmsBackApplyDetailVo> queryList(CmsBackApplyDetailDto dto) {
        return cmsBackApplyDetailReadMapper.queryList(dto);
    }

    @Override
    public List<CmsBackApplyDetailVo> queryPageList(CmsBackApplyDetailDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return cmsBackApplyDetailReadMapper.queryList(dto);
    }

}

package com.jp.med.cms.modules.common.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.cms.modules.common.dto.CmsAuditResDto;
import com.jp.med.cms.modules.common.vo.CmsAuditResVo;

import java.util.List;

/**
 * 审核结果表
 * <AUTHOR>
 * @email -
 * @date 2024-04-23 09:33:58
 */
public interface CmsAuditResReadService extends IService<CmsAuditResDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsAuditResVo> queryList(CmsAuditResDto dto);
}


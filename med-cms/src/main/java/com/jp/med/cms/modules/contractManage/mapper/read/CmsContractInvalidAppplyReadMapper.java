package com.jp.med.cms.modules.contractManage.mapper.read;

import com.jp.med.cms.modules.contractManage.dto.CmsContractInvalidAppplyDto;
import com.jp.med.cms.modules.contractManage.vo.CmsContractInvalidAppplyVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 合同作废申请
 * <AUTHOR>
 * @email -
 * @date 2024-08-14 16:29:12
 */
@Mapper
public interface CmsContractInvalidAppplyReadMapper extends BaseMapper<CmsContractInvalidAppplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<CmsContractInvalidAppplyVo> queryList(CmsContractInvalidAppplyDto dto);
}

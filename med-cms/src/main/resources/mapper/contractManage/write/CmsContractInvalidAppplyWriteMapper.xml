<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.cms.modules.contractManage.mapper.write.CmsContractInvalidAppplyWriteMapper">
    <update id="updateChkState">
        update cms_contract_invalid_appply
        set chk_state= #{chkState,jdbcType=VARCHAR}

        where audit_bchno = #{auditBchno,jdbcType=VARCHAR}
    </update>
</mapper>

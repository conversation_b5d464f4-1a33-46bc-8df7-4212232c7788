package com.jp.med.bdp.modules.medRcdQA.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bdp.modules.medRcdQA.dto.BdpMrQsGroupCfgDto;
import com.jp.med.bdp.modules.medRcdQA.vo.BdpMrQsGroupCfgVo;

import java.util.List;

/**
 * 病案首页问卷调查选项组配置
 * <AUTHOR>
 * @email -
 * @date 2024-03-15 10:23:01
 */
public interface BdpMrQsGroupCfgReadService extends IService<BdpMrQsGroupCfgDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BdpMrQsGroupCfgVo> queryList(BdpMrQsGroupCfgDto dto);

    /**
     * 查询不是tree的数据
     * @param dto
     * @return
     */
    List<BdpMrQsGroupCfgVo>queryListOrigin(BdpMrQsGroupCfgDto dto);
}


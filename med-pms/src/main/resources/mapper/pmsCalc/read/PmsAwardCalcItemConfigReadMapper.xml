<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsCalcTemplate.mapper.read.PmsAwardCalcItemConfigReadMapper">
    <!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcItemConfigVo"
               id="awardCalcItemConfigMap">
        <result property="id" column="id"/>
        <result property="itemName" column="item_name"/>
        <result property="itemCode" column="item_code"/>
        <result property="active" column="active"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcItemConfigVo">
        SELECT a.*,
               (SELECT string_agg(ho.org_name, ',')
                FROM hrm_org ho
                WHERE ho.org_id = ANY (
                    string_to_array(a.upload_depts, ',')))      as uploadDeptNames,
               (SELECT string_agg(hu.emp_name,
                                  ',')
                FROM hrm_employee_info hu
                WHERE hu.emp_code = ANY (string_to_array(a.report_users,
                                                         ','))) as reportUserNames

        <if
                test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            ,
                subquery.lastCollectTime,
                subquery.lastCollectId,
                subquery.reportedDeptsCount
        </if>
        from
        pms_award_calc_item_config a
        <if
                test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            LEFT JOIN LATERAL ( SELECT m1.collection_date           AS lastCollectTime,
                                       m1.id                        AS lastCollectId,
                                       (SELECT COUNT(DISTINCT r.id)
                                        FROM pms_award_collection_result r
                                        WHERE r.batch_code = m1.id) AS
                                                                       reportedDeptsCount
                                FROM pms_calc_import_metadata m1
                                WHERE m1.id = (SELECT MAX(m.id)
                                               FROM pms_calc_import_metadata m
                                               WHERE m.item_code = a.item_code
                                                 AND m.collection_date BETWEEN
                                                   CAST(#{startTime,jdbcType=VARCHAR} AS DATE) AND CAST(#{endTime,jdbcType=VARCHAR} AS DATE)) ) subquery
                      ON TRUE
        </if>

        <where>
            <if test="id != null">
                and a.id = #{id}
            </if>

            <if test="active != null">
                and a.active =
                    #{active}
            </if>
            <if test="classification != null and classification != ''">
                and a.
                        classification = #{classification}
            </if>
            <if test="itemCode != null and itemCode != ''">
                and a.item_code = #{itemCode}
            </if>
            <if test="itemName != null and itemName != ''">
                and a.
                        item_name like concat('%', #{itemName}, '%')
            </if>

            <if
                    test="deptCodeVersion != null and deptCodeVersion != ''">
                and a.dept_code_version =
                    #{deptCodeVersion}
            </if>
            <if test="collectionMethod != null and collectionMethod != ''">
                and a.collection_method like concat('%', #{collectionMethod}, '%')
            </if>
            <if
                    test="calculationRule != null and calculationRule != ''">
                and a.calculation_rule
                    like concat('%', #{calculationRule}, '%')
            </if>
            <if
                    test="statisticsFrequency != null and statisticsFrequency != ''">
                and a.
                        statistics_frequency = #{statisticsFrequency}
            </if>
            <if
                    test="classification != null and classification != ''">
                and a.classification =
                    #{classification}
            </if>

            <if
                    test="searchCollectionSourceList != null and searchCollectionSourceList.size() != 0">
                and collection_source in
                <foreach collection="searchCollectionSourceList" item="item"
                         separator="," open="(" close=")">
                    #{item}
                </foreach>
            </if>

            <if
                    test="reportStatus != null and reportStatus == '0'.toString()">
                <if
                        test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                    and subquery.lastCollectId is null
                </if>
            </if>

            <if
                    test="reportStatus != null and reportStatus == '1'.toString()">
                <if
                        test="startTime != null and startTime != '' and endTime != null and endTime != ''">
                    and subquery.lastCollectId is not null
                </if>
            </if>

            <!-- uploadDepts -->
            <if
                    test="uploadDepts != null and uploadDepts != ''">
                and exists (select 1
                            from unnest(string_to_array(a.upload_depts, ',')) as dept1
                            where dept1 in (select dept2
                                            from unnest(string_to_array(#{uploadDepts}, ',')) as dept2))
            </if>
        </where>
        order by id
    </select>
    <select id="queryFirstLevelItem" resultType="java.lang.String">

        SELECT distinct classification
        FROM pms_award_calc_item_config
        WHERE classification != '常量'
          and classification is not null
    </select>

</mapper>
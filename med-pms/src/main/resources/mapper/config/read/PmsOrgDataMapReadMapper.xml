<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsBaseConfig.PmsOrgDataMap.mapper.PmsOrgDataMapReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsBaseConfig.PmsOrgDataMap.vo.PmsOrgDataMapVo" id="orgDataMapMap">
        <result property="id" column="id"/>
        <result property="orgId" column="org_id"/>
        <result property="orgDataIds" column="org_data_ids"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="activeFlag" column="active_flag"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsBaseConfig.PmsOrgDataMap.vo.PmsOrgDataMapVo">
        select
            id as id,
            org_id as orgId,
            org_data_ids as orgDataIds,
            hospital_id as hospitalId,
            active_flag as activeFlag
        from pms_org_data_map
        
    </select>
    <select id="selectOneByOrgId" resultType="com.jp.med.pms.modules.pmsBaseConfig.PmsOrgDataMap.vo.PmsOrgDataMapVo">
        select id as id,
        org_id as orgId,
        org_data_ids as orgDataIds,
        hospital_id as hospitalId,
        active_flag as activeFlag
        from pms_org_data_map
        <where>
            <if test="orgId != null and orgId != ''">
                org_id = #{orgId,jdbcType=VARCHAR}
            </if>
            and ( active_flag is null or active_flag != '1')
        </where>
    </select>
</mapper>

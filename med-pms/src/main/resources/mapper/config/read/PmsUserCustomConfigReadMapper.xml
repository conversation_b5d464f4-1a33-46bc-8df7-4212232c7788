<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsBaseConfig.PmsUserCustomConfig.mapper.PmsUserCustomConfigReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.pms.modules.pmsBaseConfig.PmsUserCustomConfig.vo.PmsUserCustomConfigVo" id="userCustomConfigMap">
        <result property="id" column="id"/>
        <result property="configName" column="config_name"/>
        <result property="configCode" column="config_code"/>
        <result property="status" column="status"/>
        <result property="hospitalId" column="hospital_id"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.pms.modules.pmsBaseConfig.PmsUserCustomConfig.vo.PmsUserCustomConfigVo">
        select
            id as id,
            config_name as configName,
            config_code as configCode,
            status as status,
            hospital_id as hospitalId,
            create_time as createTime,
            create_by as createBy,
            update_time as updateTime,
            update_by as updateBy
        from pms_user_custom_config
    </select>

</mapper>

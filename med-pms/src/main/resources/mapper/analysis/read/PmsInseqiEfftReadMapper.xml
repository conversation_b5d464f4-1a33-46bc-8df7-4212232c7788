<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.mapper.read.PmsInseqiEfftReadMapper">
    <select id="queryList1" resultType="com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.vo.PmsInseqiEfftVo">
        select
            a.id as id,
            a.ym as ym,
            a.org_id as orgId,
            a.asset_name as assetName,
            a.cnt as cnt,
            a.asset_nav as assetNav,
            a.depr_mon as deprMon,
            a.exam_num as examNum,
            a.inc as inc,
            a.rpt_type as rptType,
            a.hospital_id as hospitalId
        from pms_inseqi_efft a
    </select>



    <select id="queryList" resultType="com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.vo.PmsInseqiEfftVo">
        select o.org_id               as orgId,
               z.org_name             as orgName,
               asset_name             as assetName,
               max(o.cnt)             as cnt,
               z.hospital_id          as hospitalId,
               #{ym,jdbcType=VARCHAR} as ym,
               sum(peo)               as examNum,
               sum(mon)               as inc from
        (select * from
        (select a.ksdm,
                a.zxks,
                case when COALESCE(a.lcxmdm, '0') = '0' or a.lcxmdm = '' then a.ypdm else a.lcxmdm end item_code1,
                case when COALESCE(a.lcxmdm, '0') = '0' or a.lcxmdm = '' then a.ypmc else a.lcxmmc end,
                count(distinct a.syxh)                                                                 peo,
                count(distinct a.yzxh),
                sum(cast(a.ypsl as numeric)),
                sum(cast(a.zje as numeric)) as                                                         mon
         from v_setl_list_item a
        <!--inner join ZY_BRSYK d on a.syxh=d.syxh and d.brzt<>'9'-->
        where a.ksdm != '9999'
          and substr(a.zxrq, 1, 6) = #{ym,jdbcType=VARCHAR}
        group by a.ksdm, a.zxks, case when COALESCE(a.lcxmdm, '0') = '0' or a.lcxmdm = '' then a.ypdm else a.lcxmdm end,
                 case when COALESCE(a.lcxmdm, '0') = '0' or a.lcxmdm = '' then a.ypmc else a.lcxmmc end) x
            inner join (select m.org_id,
                               m.asset_name,
                               unnest(string_to_array(m.item_code, ','))                 as item_code,
                               n.org_id_orign,
                               array_length(regexp_split_to_array(m.asset_code, ','), 1) as cnt,
                               m.item_type
                        from pms_asset_charge_cfg m
                                 left join sys_dept_mapping n on m.org_id = n.org_id
                        where m.active_flag = '1') y
                       on case when item_type = '1' then trim(x.zxks) else trim(x.ksdm) end = y.org_id_orign and
                          x.item_code1 = y.item_code
        ) o
            left join hrm_org z
                      on o.org_id = z.org_id
        <where>
            <if test="asset != '' and asset != null">
                o.asset_name = #{asset,jdbcType=VARCHAR}
            </if>
        </where>
        group by o.org_id, asset_name, z.org_name, z.hospital_id
    </select>

</mapper>

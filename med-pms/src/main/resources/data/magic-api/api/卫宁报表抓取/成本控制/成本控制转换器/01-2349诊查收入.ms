{
  "properties" : { },
  "id" : "copy1747128251558d98477",
  "script" : null,
  "groupId" : "e95075ab77da4b8c9abc8a82aba78da1",
  "name" : "01-2349诊查收入",
  "createTime" : null,
  "updateTime" : 1747380267909,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2349_zcsr",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : null,
  "description" : "",
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.math.BigDecimal
import java.util.stream.Collectors
import java.util.Collections
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/deptCodeTransform' as deptCodeTransform;
import '@/pms/performanceDeptNameMatch' as performanceDeptNameMatch;

import '@post:/WinningReportFetch/extractData0' as extractData;
import java.math.BigDecimal;

// 需要的 后续修改为可配置的 
// todo3
var departmentCategory = {
    "住院": [
        "肾内、内分泌科",
        "呼吸与危重症医学科",
        "消化内科",
        "消化内科内镜室",
        "心血管内科",
        "介入放射室",
        "神经内科",
        "老年病科",
        "全科医学科",
        "眼科",
        "耳鼻咽喉头颈外科",
        "儿科",
        "新生儿科",
        "感染性疾病科",
        "肿瘤科、安宁疗护科",
        "骨科",
        "儿童保健门诊",
        "肝胆胰外科",
        "胃肠外科",
        "神经外科",
        "胸外科",
        "泌尿外科",
        "妇产科",
        "手麻科",
        "疼痛科",
        "急诊科",
        "重症医学科",
        "中医科",
        "皮肤科",
        "肛肠科",
        "康复医学科"
    ],
    "门诊": [
        "皮肤科",
        "口腔科门诊",
        "妇科专家门诊"
    ]
}


// 定义日期变量
var startDate
var endDate
let data
var isTest = false;
if (!body || !(body.extractedData)) {
    log.info("测试环境")
    isTest = true

    startDate = "2025-02-01"
    endDate = "2025-02-28"
} else {
    log.info("正式环境")
    startDate = body.extractedData.variablesMap.get("&{last.month.firstDay}")
    endDate = body.extractedData.variablesMap.get("&{last.month.lastDay}")
}

// 住院
// @trigger("住院")
var trigger1 = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2349",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "执行科室",
                // "specifyOssFile": "/wnReport/2025/04/16/1657/wnbb_cw_医院收入统计.cpt.xlsx",
                "KSRQ": startDate,
                "JSRQ": endDate,
                "tjfs": "1",
                "tjfw": "1",
                "xmlx": "1",
                "tjkj": "2",
                "ksjb": "3",
                "xmdmj": "19",
                "tjkj2": "-1",
                "kdorzx": "1",
                "ksdm": "-1",
                "dxmdm": "-1",
                "xmdm": "-1"
            }
        }
    }
}
// 门诊
// @Extract("门诊")
var trigger2 = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2349",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "执行科室",
                // "specifyOssFile": "/wnReport/2025/04/16/1658/wnbb_cw_医院收入统计.cpt.xlsx",
                "KSRQ": startDate,
                "JSRQ": endDate,
                "tjfs": "1",
                "tjfw": "2",
                "xmdmj": "19",
                "xmlx": "1",
                "tjkj": "2",
                "ksjb": "3",
                "tjkj2": "-1",
                "kdorzx": "1",
                "ksdm": "-1",
                "dxmdm": "-1",
                "xmdm": "-1"
            }
        }
    }
}



var newDataMap = {}
let result = {
    MEDICAL_SERVICE_TREATMENT: {}, // 治疗
    MEDICAL_SERVICE_DIAGNOSTIC: {}, // 诊察
}

var {
    deptMap,
    deptMergeMap
} = performanceDeptNameMatch()

// 初始化用于存储计算过程详情的Map
var calculationDetails = {};
// 成本控制 卫宁报表  2349
var processingData = (triggerData, type) => {
    let data;


    body = {
        trigger: triggerData
    }
    const resData = extractData()

    data = resData.jsonData
    var dataObj = data
    var newDataMap = {}
    var {
        deptMap,
        deptMergeMap
    } = performanceDeptNameMatch()
    deptMergeMap.forEach((匹配科室, 执行科室List) => {
        newDataMap.computeIfAbsent(匹配科室, (k, v) => {
            return 执行科室List
                .stream()
                .filter(name => dataObj.containsKey(name))
                .map(name => dataObj.get(name))
                .collect(Collectors.toList());
        })
    })
    newDataMap.forEach((dept, vList) => {
        // 检查科室是否在 departmentCategory 中
        var isIncluded = false
        if (type == 1) { // 住院
            isIncluded = departmentCategory.住院.contains(dept)
        } else if (type == 2) { // 门诊
            isIncluded = departmentCategory.门诊.contains(dept)
        }

        if (isIncluded) {
            var totalZcf = BigDecimal.ZERO // 用于记录总的诊查费
            vList.forEach(b => {
                var zcf = b.get("诊查费").asDecimal(BigDecimal.ZERO)
                var xj = b.get("小计").asDecimal(BigDecimal.ZERO)
                totalZcf = totalZcf.add(zcf)
            })
            if (result.MEDICAL_SERVICE_DIAGNOSTIC.get(dept) != null) {

                result.MEDICAL_SERVICE_DIAGNOSTIC.put(
                    dept, totalZcf.add(result.MEDICAL_SERVICE_DIAGNOSTIC.get(dept).asDecimal())
                )
                log.info("门诊+住院{} ,{},{}", dept, totalZcf, result.MEDICAL_SERVICE_DIAGNOSTIC.get(dept))

            } else {
                result.MEDICAL_SERVICE_DIAGNOSTIC.put(
                    dept, totalZcf
                )
            }
        }
    });
}



processingData(trigger1, 1)
processingData(trigger2, 2)




return result
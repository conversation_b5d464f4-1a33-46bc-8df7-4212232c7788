{
  "properties" : { },
  "id" : "copy1736738009598d16538",
  "script" : null,
  "groupId" : "24f1c8948f1943819669e1f42cb3918b",
  "name" : "2766专科门诊",
  "createTime" : null,
  "updateTime" : 1743576333175,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2766_zkmz",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : "",
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import WinningReportExporter
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import java.math.BigDecimal;
import '@post:/WinningReportFetch/extractData0' as extractData;
import com.jp.med.pms.util.HolidayUtils

// var winningReportExporter = new WinningReportExporter()
// return winningReportExporter.login(winningReportExporter.username, winningReportExporter.password);

let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2766",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "科室名称",
                "returnArrayData": true,
                "KSRQ": "2025-02-01",
                "JSRQ": "2025-02-28",
                // // "specifyOssFile": "/wnReport/2025/01/06/1525/挂号统计报表.xlsx",
                "bucket": "pms"

            }
        }
    }
}

body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData



const result = {}
/**
 * 专科门诊 zkmz 挂号统计报表
 * 报表编号: 2766
 * 状态: 进行中
 *
 * <p>该报表用于统计挂号收费合计及挂号人次。</p>
 *
 * <h3>工作量-成本控制指标</h3>
 * <p>该指标每月统计一次，适用于各科室医护人员。</p>
 */

var map = {
    "1030": "急诊科",
    "1036": "胸外科",
    "1037": "神经外科",
    "1043": "急诊儿科",
    "1044": "急诊科",
    "1045": "急诊科",
    "1048": "口腔科门诊",
    "1049": "中医科",
    "1050": "门诊肛肠科",
    "1051": "皮肤科门诊",
    "1052": "麻醉科",
    "1054": "便民门诊",
    "1055": "伤口造口门诊",
    "1056": "麻醉科",
    "1063": "消化内科",
    "1066": "感染性疾病科",
    "1069": "肝胆胰外科",
    "1085": "老年病科",
    "1086": "放射科",
    "1088": "放射科",
    "1102": "麻醉科",
    "1126": "神经内科",
    "1127": "重症医学科",
    "1128": "呼吸与危重症医学科",
    "1129": "消化内科",
    "1130": "心血管内科",
    "1131": "神经内科",
    "1132": "老年病科",
    "1133": "妇产科",
    "1134": "妇产科",
    "1135": "眼科",
    "1136": "耳鼻咽喉头颈外科",
    "1137": "康复医学科",
    "1138": "儿科",
    "1140": "泌尿外科",
    "1144": "肾内、内分泌科",
    "1148": "肝胆胰外科",
    "1149": "老年病科",
    "1150": "门诊肛肠科",
    "1151": "城南院区妇产科门诊",
    "1155": "康复医学科",
    "1158": "肾内、内分泌科",
    "1160": "健康体检科",
    "1162": "儿童保健门诊",
    "1163": "急诊科",
    "1164": "急诊妇产科",
    "1165": "急诊耳鼻咽喉头颈外科",
    "1166": "急诊眼科",
    "1167": "急诊产科",
    "1169": "麻醉科",
    "1170": "新生儿科",
    "1176": "骨科",
    "1178": "麻醉科",
    "1180": "骨科",
    "1193": "肾内、内分泌科",
    "1206": "肿瘤科",
    "1207": "PICC门诊",
    "1208": "伤口造口门诊",
    "1209": "儿童保健门诊",
    "1214": "肾内、内分泌科",
    "1217": "综合内科(专家)门诊",
    "1220": "精神科",
    "1226": "老年病科",
    "1229": "感染性疾病科",
    "1230": "全科医学安宁疗护科",
    "1231": "精神科",
    "1232": "皮肤科门诊",
    "1236": "老年病科",
    "1237": "泌尿外科",
    "1239": "神经外科",
    "1240": "胸外科",
    "1243": "骨科",
    "1244": "老年病科",
    "1246": "胃肠外科",
    "1247": "胃肠外科",
    "1248": "胃肠外科",
    "1249": "神经外科",
    "1250": "肾内、内分泌科",
    "1251": "肝胆胰外科",
    "1252": "肝胆胰外科"
}

var map1 = {
    "老年病科": "急诊科",
    "胸外科、肺结节门诊": "胸外科",
    "神经外科、头昏头痛门诊": "神经外科",
    "急诊儿科": "急诊儿科",
    "急诊内科": "急诊科",
    "急诊外科": "急诊科",
    "口腔科门诊": "口腔科门诊",
    "中医科门诊": "中医科",
    "肛肠科门诊": "门诊肛肠科",
    "皮肤科门诊": "皮肤科门诊",
    "麻醉疼痛科门诊": "麻醉科",
    "便民门诊": "便民门诊",
    "伤口造口门诊": "伤口造口门诊",
    "门诊手术室": "麻醉科",
    "消化内科内镜室": "消化内科",
    "感染性疾病门诊（肝炎、结核、腹泻": "感染性疾病科",
    "乳腺科门诊": "肝胆胰外科",
    "全科医疗科": "老年病科",
    "DR摄影室": "放射科",
    "CT室": "放射科",
    "手术室(本部)": "麻醉科",
    "神经内科检查室": "神经内科",
    "普通内科门诊": "重症医学科",
    "呼吸与危重症医学科门诊": "呼吸与危重症医学科",
    "消化内科门诊": "消化内科",
    "心血管内科门诊": "心血管内科",
    "神经内科门诊": "神经内科",
    "老年病科门诊": "老年病科",
    "产科门诊": "妇产科",
    "妇科门诊": "妇产科",
    "眼科门诊": "眼科",
    "耳鼻咽喉头颈外科门诊": "耳鼻咽喉头颈外科",
    "康复医学科门诊": "康复医学科",
    "儿科门诊": "儿科",
    "泌尿外科门诊": "泌尿外科",
    "肾内、内分泌科门诊": "肾内、内分泌科",
    "肝胆胰外科门诊": "肝胆胰外科",
    "肿瘤门诊": "老年病科",
    "胃肠外科、疝与腹壁外科门诊": "门诊肛肠科",
    "城南院区妇产科门诊": "城南院区妇产科门诊",
    "城南院区康复科门诊": "康复医学科",
    "营养科门诊": "肾内、内分泌科",
    "妇科专家门诊": "健康体检科",
    "儿童保健门诊": "儿童保健门诊",
    "急诊口腔科": "急诊科",
    "急诊妇产科": "急诊妇产科",
    "急诊耳鼻咽喉头颈外科": "急诊耳鼻咽喉头颈外科",
    "急诊眼科": "急诊眼科",
    "急诊产科": "急诊产科",
    "麻醉科门诊": "麻醉科",
    "新生儿科门诊": "新生儿科",
    "城南院区骨科门诊": "骨科",
    "城南院区麻醉科": "麻醉科",
    "骨科门诊": "骨科",
    "风湿免疫专科门诊": "肾内、内分泌科",
    "城南院区肿瘤门诊": "肿瘤科",
    "PICC门诊": "PICC门诊",
    "城南伤口造口门诊": "伤口造口门诊",
    "生长发育门诊": "儿童保健门诊",
    "血液透析门诊": "肾内、内分泌科",
    "综合内科(专家)门诊": "综合内科(专家)门诊",
    "精神科门诊": "精神科",
    "城南院区老年病科门诊": "老年病科",
    "城南院区感染性疾病科门诊": "感染性疾病科",
    "全科医学科门诊": "全科医学安宁疗护科",
    "睡眠障碍门诊": "精神科",
    "脱发门诊": "皮肤科门诊",
    "肿瘤血管介入门诊": "老年病科",
    "泌尿外科日间手术门诊": "泌尿外科",
    "普通外科门诊": "神经外科",
    "肺结节(MDT)门诊": "胸外科",
    "城南院区骨科日归手术门诊": "骨科",
    "静脉血栓栓塞症(VTE)防治门诊": "老年病科",
    "胃肠外科体重管理门诊": "胃肠外科",
    "胃肠外科便秘门诊": "胃肠外科",
    "胃肠外科减重与代谢外科门诊": "胃肠外科",
    "胃肠外科胃食管反流外科门诊": "神经外科",
    "糖尿病教育护理门诊": "肾内、内分泌科",
    "乳腺（MDT）门诊": "肝胆胰外科",
    "甲状腺（MDT）门诊": "肝胆胰外科"
    }


data.forEach((deptName, detailList) => {
    var registrationCount = detailList.stream()
        .reduce(0, (a, b) => {
            return a + b.get("收费合计挂号人次").asInt(0)
        })
    result.put(deptName, registrationCount)
})



return {

    result
}
{
  "properties" : { },
  "id" : "82bd1617af834e6e858ea3ce9171a4b0",
  "script" : null,
  "groupId" : "97a3e763eb36484ca09c4c1ac033c133",
  "name" : "变量转换",
  "createTime" : null,
  "updateTime" : 1736148749721,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "variableTransformation",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "requestBody" : "{\n    \"reportId\": \"2349\",\n    \"queryParams\": {\n        \"extractDataId\": \"&{extractDataId}\",\n        \"date\": \"&{date}\",\n        \"YYDM\": \"01\",\n        \"KSRQ\": \"&{KSRQ.firstDay}\",\n        \"JSRQ\": \"&{JSRQ.lastDay}\"\n    },\n    \"paramsPath\": \".queryParams\"\n}",
  "headers" : [ ],
  "paths" : [ ],
  "responseBody" : "{\n    \"code\": 200,\n    \"status\": 0,\n    \"message\": \"success\",\n    \"data\": {\n        \"&{KSRQ.firstDay}\": \"2024-11-01\",\n        \"&{JSRQ.lastDay}\": \"2024-11-30\",\n        \"&{KSRQ.firstDay.timestamp}\": \"2024-11-01 00:00:00\",\n        \"&{JSRQ.lastDay.timestamp}\": \"2024-11-30 23:59:59\"\n    }\n}",
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import cn.hutool.json.JSONArray
import cn.hutool.json.JSONBeanParser
import WinningReportExporter
import cn.hutool.json.JSONObject
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import cn.hutool.core.util.StrUtil;



let variablesMap = {}


// 获取当前日期
LocalDate today = LocalDate.now();
// 获取上个月的第一天
LocalDate firstDayOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
// 获取上个月的最后一天
LocalDate lastDayOfLastMonth = today.minusMonths(1).withDayOfMonth(today.minusMonths(1).lengthOfMonth());
DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
String firstDayFormatted = firstDayOfLastMonth.format(formatter);
String lastDayFormatted = lastDayOfLastMonth.format(formatter);
variablesMap.put("&{KSRQ.firstDay}", firstDayFormatted)
variablesMap.put("&{JSRQ.lastDay}", lastDayFormatted)



// 获取上个月第一天和最后一天的毫秒时间戳
DateTimeFormatter timeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
let firstDayTimestamp = firstDayOfLastMonth.atStartOfDay()
let lastDayTimestamp = lastDayOfLastMonth.atTime(23, 59, 59)
// 格式化日期时间

firstDayTimestamp = firstDayTimestamp.format(timeFormatter);
lastDayTimestamp = lastDayTimestamp.format(timeFormatter);

variablesMap.put("&{KSRQ.firstDay.timestamp}",  firstDayTimestamp);
variablesMap.put("&{JSRQ.lastDay.timestamp}", lastDayTimestamp);


return variablesMap
const bodyJSONObject = new JSONObject(body)

let needReplaceObj = bodyJSONObject


if (StrUtil.isNotBlank(body.get("paramsPath"))) {
    needReplaceObj = bodyJSONObject.getByPath(body.get("paramsPath"))
    bodyJSONObject.remove("paramsPath")
}

for (v in needReplaceObj.entrySet()) { //如果不需要key，也可以写成for(value in map)
    const key = v.getKey()
    const value = v.getValue()
    System.out.println("key:" + key + "value:" + value)
    if (variablesMap.get(value)) {
        System.out.println("替换参数：：" + key + ':' + value + ":" + variablesMap.get(value));
        needReplaceObj[key] = variablesMap.get(value)
    }

}

return bodyJSONObject
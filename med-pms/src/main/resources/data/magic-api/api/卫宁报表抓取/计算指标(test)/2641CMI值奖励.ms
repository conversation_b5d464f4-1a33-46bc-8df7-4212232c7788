{
  "properties" : { },
  "id" : "copy1735889234422d93217",
  "script" : null,
  "groupId" : "24f1c8948f1943819669e1f42cb3918b",
  "name" : "2641CMI值奖励",
  "createTime" : null,
  "updateTime" : 1747208904560,
  "lock" : null,
  "createBy" : null,
  "updateBy" : null,
  "path" : "dataTransformer_2641",
  "method" : "POST",
  "parameters" : [ ],
  "options" : [ ],
  "headers" : [ ],
  "paths" : [ ],
  "description" : null,
  "requestBodyDefinition" : null,
  "responseBodyDefinition" : null
}
================================
import java.time.temporal.ChronoUnit
import java.util.regex.Pattern
import java.time.LocalDate
import java.time.Duration
import java.time.LocalDateTime
import com.jp.med.pms.util.HolidayUtils
import java.time.LocalTime

import java.time.format.DateTimeFormatter

import java.math.BigDecimal

import java.util.stream.Collectors
import cn.hutool.core.util.StrUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSONUtil
import cn.hutool.json.JSON
import cn.hutool.json.JSONObject
import log; //org.slf4j.Logger
import '@post:/WinningReportFetch/extractData0' as extractData;
import java.util.ArrayList

/**
 * CMI值奖励
 * <p>
 *   该指标数据来源于卫宁报表运管部下“出区病人结算状态管理”表。
 *   该表数据每日更新，为保证指标数值的稳定性，
 *   采取每月14或15号导出数据作为该月指标的固定值。
 * </p>
 * <p>
 *   <b>差异性说明：</b>对于重症医学科，该指标的值取自“收治病人情况表”中
 *   已结算的病人总数，不包含未结算的病人。
 * </p>
 */

// TODO
let trigger = {
    "type": "magic-api",
    "magicApi": {
        "path": "/WinningReportFetch/extractData",
        "method": "POST",
        "body": {
            "reportId": "2641",
            "paramsPath": ".queryParams",
            "queryParams": {
                "WneportExcelConfigGroupColumnName": "出院科室",
                "KSRQ": "2024-11-01",
                "JSRQ": "2025-11-10",
                "KSDM": "-1",
                // "specifyOssFile": "wnReport/2025/01/03/wnbb_ygb_出区病人结算状态管理.cpt.xlsx",
                "bucket": "pms"
            }
        }
    }
}




body = {
    trigger
}
const resData = extractData()

let data = resData.jsonData

// return data
let a = []
let result = {}
let okStatusList = []
okStatusList.add("出院结算")
// okStatusList.add("在院结算") //在院结算的只有妇产科生产的才会出现这种情况。因为需要先结算婴儿再结算母亲的
data.forEach((dept, detailList) => {
    var count = detailList.stream().filter(detail => {
        return okStatusList.indexOf(detail.get("结算状态")) != -1
    }).count()
    result.put(dept, count)
})

return result
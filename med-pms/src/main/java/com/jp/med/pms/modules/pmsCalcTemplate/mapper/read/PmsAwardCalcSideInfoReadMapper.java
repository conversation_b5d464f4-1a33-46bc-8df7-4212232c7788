package com.jp.med.pms.modules.pmsCalcTemplate.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcSideInfoDto;
import com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcSideInfoVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 绩效计算侧边信息表，存储绩效计算指标的具体明细数据
 *
 * <AUTHOR>
 * @email -
 * @date 2025-03-31 11:56:50
 */
@Mapper
public interface PmsAwardCalcSideInfoReadMapper extends BaseMapper<PmsAwardCalcSideInfoDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<PmsAwardCalcSideInfoVo> queryList(PmsAwardCalcSideInfoDto dto);
}

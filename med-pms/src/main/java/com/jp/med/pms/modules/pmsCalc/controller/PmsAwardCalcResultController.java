package com.jp.med.pms.modules.pmsCalc.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.pms.modules.pmsCalc.dto.PmsAwardCalcResultDto;
import com.jp.med.pms.modules.pmsCalc.service.read.PmsAwardCalcResultReadService;
import com.jp.med.pms.modules.pmsCalc.service.write.PmsAwardCalcResultWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 绩效计算结果
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Api(value = "绩效计算结果", tags = "绩效计算结果")
@RestController
@RequestMapping("pmsAwardCalcResult")
public class PmsAwardCalcResultController {

    @Autowired
    private PmsAwardCalcResultReadService pmsAwardCalcResultReadService;

    @Autowired
    private PmsAwardCalcResultWriteService pmsAwardCalcResultWriteService;


    /**
     * 保存院级护士长分配绩效数据
     * @param dto
     * @return
     */
    @ApiOperation("保存院级护士长分配绩效数据")
    @PostMapping("/saveRankAwardByHospital")
    public CommonResult<?> saveRankAwardByHospital(PmsAwardCalcResultDto dto) {
        pmsAwardCalcResultWriteService.saveRankAwardByHospital(dto);
        return CommonResult.success();
    }

    /**
     * 查询院级护士长分配工资数据
     * @param dto
     * @return
     */
    @ApiOperation("查询院级护士长分配工资数据")
    @PostMapping("/selectRankAwardByHospital")
    public CommonResult<?> selectRankAwardByHospital(@RequestBody PmsAwardCalcResultDto dto) {
        return CommonResult.success(pmsAwardCalcResultReadService.selectRankAwardByHospital(dto));
    }


    /**
     * 计算院级护士长分配工资数据
     */
    @ApiOperation("计算院级护士长分配工资数据(前端处理数据)")
    @PostMapping("/getRankAwardByHospital2")
    public CommonResult<?> getRankAwardByHospital2(@RequestBody PmsAwardCalcResultDto dto) {
        return CommonResult.success(pmsAwardCalcResultReadService.getRankAwardByHospital2(dto));
    }


    /**
     * 计算院级护士长分配工资数据
     */
    @ApiOperation("计算院级护士长分配工资数据")
    @PostMapping("/getRankAwardByHospital")
    public CommonResult<?> getRankAwardByHospital(@RequestBody PmsAwardCalcResultDto dto) {
        return CommonResult.success(pmsAwardCalcResultReadService.getRankAwardByHospital(dto));
    }

    /**
     * 查询院级绩效工资汇总 底表 数据
     */
    @ApiOperation("查询院级绩效工资汇总 底表 数据")
    @PostMapping("/getAwardByHospital")
    public CommonResult<?> getAwardByHospital(@RequestBody PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(pmsAwardCalcResultReadService.getAwardByHospital(dto));
    }


    /**
     * 二次绩效分配查询 按量计算绩效合计
     */
    @ApiOperation("按量计算绩效合计")
    @PostMapping("/getAwardByQuantity")
    public CommonResult<?> getAwardByQuantity(@RequestBody PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(pmsAwardCalcResultReadService.getAwardByQuantity(dto));
    }

    /**
     * 发起一次绩效审核
     */
    @ApiOperation("发起一次绩效审核")
    @PostMapping("/createAwardAudit")
    public CommonResult<?> createAwardAudit(PmsAwardCalcResultDto dto) {
        pmsAwardCalcResultWriteService.createAwardAudit(dto);
        return CommonResult.success();
    }


    /**
     * 查询一次分配结果 一级指标汇总
     */
    @ApiOperation("查询一次分配结果 一级指标汇总")
    @PostMapping("/calcResultList")
    public CommonResult<?> calcResultList(@RequestBody PmsAwardCalcResultDto dto) {
        return CommonResult.success(pmsAwardCalcResultReadService.calcResultList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("分页查询绩效计算结果")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.paging(pmsAwardCalcResultReadService.queryPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询绩效计算结果")
    @PostMapping("/listMaxVersion")
    public CommonResult<?> list(@RequestBody PmsAwardCalcResultDto dto) throws Exception {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(pmsAwardCalcResultReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增绩效计算结果")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody PmsAwardCalcResultDto dto) {
        pmsAwardCalcResultWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改绩效计算结果")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody PmsAwardCalcResultDto dto) {
        pmsAwardCalcResultWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除绩效计算结果")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody PmsAwardCalcResultDto dto) {
        pmsAwardCalcResultWriteService.removeById(dto);
        return CommonResult.success();
    }
}

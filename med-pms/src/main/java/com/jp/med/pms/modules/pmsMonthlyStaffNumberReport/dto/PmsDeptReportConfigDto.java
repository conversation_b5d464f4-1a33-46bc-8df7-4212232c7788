package com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import java.util.Date;
import java.util.List;

/**
 * 科室人员上报权限配置DTO
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pms_dept_report_config")
public class PmsDeptReportConfigDto extends CommonQueryDto {

    /**
     * 主键ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 绩效系统科室名称
     */
    @TableField("pms_dept_name")
    @NotBlank(message = "绩效系统科室名称不能为空")
    private String pmsDeptName;

    /**
     * HRP系统机构ID
     */
    @TableField("hrp_org_id")
    @NotBlank(message = "HRP系统机构ID不能为空")
    private String hrpOrgId;

    /**
     * HRP系统机构名称
     */
    @TableField("hrp_org_name")
    @NotBlank(message = "HRP系统机构名称不能为空")
    private String hrpOrgName;

    /**
     * 可上报类型代码，多个用逗号分隔
     */
    @TableField("report_type_codes")
    private String reportTypeCodes;

    /**
     * 上报人员工号，多个用逗号分隔，为空则科室所有人可上报
     */
    @TableField("reporter_emp_codes")
    private String reporterEmpCodes;

    /**
     * 上报人姓名，多个用逗号分隔
     */
    @TableField("reporter_emp_names")
    private String reporterEmpNames;

    /**
     * 是否启用
     */
    @TableField("is_enabled")
    private Boolean isEnabled;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 医院ID
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 创建时间
     */
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 更新时间
     */
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private Date updatedAt;

    /**
     * 创建人
     */
    @TableField(value = "created_by", fill = FieldFill.INSERT)
    private String createdBy;

    /**
     * 更新人
     */
    @TableField(value = "updated_by", fill = FieldFill.INSERT_UPDATE)
    private String updatedBy;

    /**
     * SQL自动注入医院条件
     */
    @TableField(exist = false)
    private boolean sqlAutowiredHospitalCondition = true;

    // ========== 前端使用的辅助字段 ==========

    /**
     * 上报类型代码列表（前端用）
     */
    @TableField(exist = false)
    private List<String> reportTypeCodeList;

    /**
     * 上报人员工号列表（前端用）
     */
    @TableField(exist = false)
    private List<String> reporterEmpCodeList;

    /**
     * 上报人姓名列表（前端用）
     */
    @TableField(exist = false)
    private List<String> reporterEmpNameList;
} 
package com.jp.med.pms.modules.pmsCalc.PmsCalcImport.vo;

import lombok.Data;

import java.util.Date;

/**
 * 绩效采集结果
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-25 16:00:00
 */
@Data
public class PmsAwardCollectionResultVo {

    /**
     * id
     */
    private Long id;

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * HRP科室代码
     */
    private String hrpDeptCode;

    /**
     * 导入科室Code
     */
    private String importDeptCode;

    /**
     * 科室代码版本
     */
    private String deptCodeVersion;

    /**
     * meataData 快照json
     */
    private String metaDataSnapshot;

    /**
     * 绩效项目
     */
    private String itemName;

    /**
     * 绩效代码
     */
    private String itemCode;

    /**
     * 统计频次
     */
    private String statisticsFrequency;

    /**
     * 数量/分数
     */
    private String itemValue;

    /**
     * 采集时间
     */
    private Date collectionDate;

    /**
     * 医院id
     */
    private String hospitalId;

    /**
     * 批次号 元信息表id
     */
    private Long batchCode;

    /**
     * 创建人
     */
    private String creatdBy;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新人
     */
    private String updateBy;

    /**
     * 更新时间
     */
    private Date updateTime;


}
package com.jp.med.pms.modules.pmsCalcTemplate.controller;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcDeptTemplateDto;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcItemConfigDto;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.PmsAwardCalcDeptTemplateReadService;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.PmsAwardCalcItemConfigReadService;
import com.jp.med.pms.modules.pmsCalcTemplate.service.write.PmsAwardCalcItemConfigWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 绩效表计算项目配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-18 23:03:00
 */
@Api(value = "绩效表计算项目配置", tags = "绩效表计算项目配置")
@RestController
@RequestMapping("pmsAwardCalcItemConfig")
public class PmsAwardCalcItemConfigController {

    @Autowired
    private PmsAwardCalcItemConfigReadService pmsAwardCalcItemConfigReadService;

    @Autowired
    private PmsAwardCalcItemConfigWriteService pmsAwardCalcItemConfigWriteService;

    @Autowired
    private PmsAwardCalcDeptTemplateReadService pmsAwardCalcDeptTemplateReadService;


    /**
     * 列表
     */
    @ApiOperation("分页查询绩效表计算项目配置")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody PmsAwardCalcItemConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.paging(pmsAwardCalcItemConfigReadService.queryPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询绩效表计算项目配置")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody PmsAwardCalcItemConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);


        return CommonResult.success(pmsAwardCalcItemConfigReadService.queryList(dto));
    }

    @ApiOperation("新增绩效表计算项目配置")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody PmsAwardCalcItemConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);

        String itemName = dto.getItemName();
        if (itemName.isBlank()) {
            return CommonResult.failed("项目名称不能为空");
        }

        // 使用工具类生成唯一的项目代码
        String finalItemCode = com.jp.med.pms.util.CodeGenerationUtils.generateUniqueCode(
                itemName,
                code -> !pmsAwardCalcItemConfigWriteService.list(
                        Wrappers.lambdaQuery(PmsAwardCalcItemConfigDto.class)
                                .eq(PmsAwardCalcItemConfigDto::getItemCode, code)
                ).isEmpty()
        );

        dto.setItemCode(finalItemCode);
        pmsAwardCalcItemConfigWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改绩效表计算项目配置")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody PmsAwardCalcItemConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        dto.setItemCode(null);
        pmsAwardCalcItemConfigWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 修改绩效表计算项目配置
     */
    @ApiOperation("修改绩效表计算项目配置-上传文件")
    @PostMapping("/uploadExcelCustom")
    public CommonResult<?> uploadExcelCustom(PmsAwardCalcItemConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        String fileUrl = OSSUtil.uploadFile(OSSConst.BUCKET_PMS, "calcExcelTemplate/", dto.getFile());
        dto.setFileUrl(fileUrl);
        dto.setFileName((dto.getFile().getOriginalFilename()));
        PmsAwardCalcItemConfigDto pmsAwardCalcItemConfigDto = new PmsAwardCalcItemConfigDto();
        pmsAwardCalcItemConfigDto.setFileName(dto.getFileName());
        pmsAwardCalcItemConfigDto.setFileUrl(fileUrl);
        return CommonResult.success(pmsAwardCalcItemConfigDto);
    }

    /**
     * 删除
     */
    @ApiOperation("删除绩效表计算项目配置")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody PmsAwardCalcItemConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);

        pmsAwardCalcItemConfigWriteService.removeById(dto);
        return CommonResult.success();
    }

    /**
     * 查询科室人员待上报任务
     */
    @ApiOperation("查询科室人员待上报任务")
    @PostMapping("/queryDeptEmpTask")
    public CommonResult<?> queryDeptEmpTask(@RequestBody PmsAwardCalcItemConfigDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);

        return CommonResult.paging(pmsAwardCalcItemConfigReadService.queryDeptEmpTask(dto));
    }

    /**
     * 根据项目代码查询引用的科室模版
     */
    @ApiOperation("根据项目代码查询引用的科室模版")
    @PostMapping("/queryTemplatesByItemCode")
    public CommonResult<?> queryTemplatesByItemCode(@RequestBody PmsAwardCalcItemConfigDto dto) {
        if (dto.getItemCode() == null || dto.getItemCode().isBlank()) {
            return CommonResult.failed("项目代码不能为空");
        }
        List<PmsAwardCalcDeptTemplateDto> templates = pmsAwardCalcDeptTemplateReadService
                .queryTemplatesByItemCode(dto.getItemCode());
        return CommonResult.success(templates);
    }

}

package com.jp.med.pms.modules.pmsCalcTemplate.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcItemSourceConfigDto;
import com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcDeptTemplateVo;
import com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcItemSourceConfigVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 绩效计算项目子项配置
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-24 03:33:26
 */
@Mapper
public interface PmsAwardCalcItemSourceConfigReadMapper extends BaseMapper<PmsAwardCalcItemSourceConfigDto> {

    /**
     * 查询列表
     *
     * @param dto
     * @return
     */
    List<PmsAwardCalcItemSourceConfigVo> queryList(PmsAwardCalcItemSourceConfigDto dto);

    List<PmsAwardCalcDeptTemplateVo> queryUseItemSourceTemplate(PmsAwardCalcItemSourceConfigDto dto);
}

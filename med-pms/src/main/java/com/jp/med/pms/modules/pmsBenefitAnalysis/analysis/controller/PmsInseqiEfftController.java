package com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.dto.PmsInseqiEfftDto;
import com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.service.read.PmsInseqiEfftReadService;
import com.jp.med.pms.modules.pmsBenefitAnalysis.analysis.service.write.PmsInseqiEfftWriteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 设备效益
 * <AUTHOR>
 * @email -
 * @date 2024-02-04 11:46:24
 */
@Api(value = "设备效益", tags = "设备效益")
@RestController
@RequestMapping("pmsInseqiEfft")
public class PmsInseqiEfftController {

    @Autowired
    private PmsInseqiEfftReadService pmsInseqiEfftReadService;

    @Autowired
    private PmsInseqiEfftWriteService pmsInseqiEfftWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询设备效益")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody PmsInseqiEfftDto dto){
        return CommonResult.success(pmsInseqiEfftReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增设备效益")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody PmsInseqiEfftDto dto){
        pmsInseqiEfftWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改设备效益")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody PmsInseqiEfftDto dto){
        pmsInseqiEfftWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除设备效益")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody PmsInseqiEfftDto dto){
        pmsInseqiEfftWriteService.removeById(dto);
        return CommonResult.success();
    }

}

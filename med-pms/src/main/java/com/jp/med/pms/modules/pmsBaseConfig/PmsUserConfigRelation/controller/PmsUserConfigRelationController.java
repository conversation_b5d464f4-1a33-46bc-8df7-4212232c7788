package com.jp.med.pms.modules.pmsBaseConfig.PmsUserConfigRelation.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.pms.modules.pmsBaseConfig.PmsUserConfigRelation.dto.PmsUserConfigRelationDto;
import com.jp.med.pms.modules.pmsBaseConfig.PmsUserConfigRelation.service.PmsUserConfigRelationWriteService;
import com.jp.med.pms.modules.pmsBaseConfig.PmsUserConfigRelation.service.read.PmsUserConfigRelationReadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;


/**
 * PMS用户关联配置表
 *
 * <AUTHOR>
 * @email -
 * @date 2025-05-19 23:11:45
 */
@Api(value = "PMS用户关联配置表", tags = "PMS用户关联配置表")
@RestController
@RequestMapping("pmsUserConfigRelation")
public class PmsUserConfigRelationController {

    @Autowired
    private PmsUserConfigRelationReadService pmsUserConfigRelationReadService;

    @Autowired
    private PmsUserConfigRelationWriteService pmsUserConfigRelationWriteService;

    /**
     * 列表
     */
    @ApiOperation("分页查询PMS用户关联配置表")
    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody PmsUserConfigRelationDto dto) {
        return CommonResult.paging(pmsUserConfigRelationReadService.queryPageList(dto));
    }

    /**
     * 列表
     */
    @ApiOperation("查询PMS用户关联配置表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody PmsUserConfigRelationDto dto) {
        return CommonResult.success(pmsUserConfigRelationReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增PMS用户关联配置表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody PmsUserConfigRelationDto dto) {
        pmsUserConfigRelationWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改PMS用户关联配置表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody PmsUserConfigRelationDto dto) {
        pmsUserConfigRelationWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除PMS用户关联配置表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody PmsUserConfigRelationDto dto) {
        pmsUserConfigRelationWriteService.removeById(dto);
        return CommonResult.success();
    }

}

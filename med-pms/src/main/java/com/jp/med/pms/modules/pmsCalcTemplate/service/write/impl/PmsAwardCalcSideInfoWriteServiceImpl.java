package com.jp.med.pms.modules.pmsCalcTemplate.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcSideInfoDto;
import com.jp.med.pms.modules.pmsCalcTemplate.mapper.write.PmsAwardCalcSideInfoWriteMapper;
import com.jp.med.pms.modules.pmsCalcTemplate.service.write.PmsAwardCalcSideInfoWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 绩效计算侧边信息表，存储绩效计算指标的具体明细数据
 *
 * <AUTHOR>
 * @email -
 * @date 2025-03-31 11:56:50
 */
@Service
@Transactional(readOnly = false)
public class PmsAwardCalcSideInfoWriteServiceImpl extends ServiceImpl<PmsAwardCalcSideInfoWriteMapper, PmsAwardCalcSideInfoDto> implements PmsAwardCalcSideInfoWriteService {
}

package com.jp.med.pms.modules.pmsBenefitAnalysis.config.PmsItemChargeStandard.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.pms.modules.pmsBenefitAnalysis.config.PmsItemChargeStandard.dto.PmsItemChargeStandardDto;

/**
 * 项目收费标准
 * <AUTHOR>
 * @email -
 * @date 2023-12-28 16:24:26
 */
public interface PmsItemChargeStandardWriteService extends IService<PmsItemChargeStandardDto> {
    void deleteById(PmsItemChargeStandardDto dto);
}


package com.jp.med.pms.modules.pmsCalcTemplate.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 绩效计算侧边信息表，存储绩效计算指标的具体明细数据
 *
 * <AUTHOR>
 * @email -
 * @date 2025-03-31 11:56:50
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pms_award_calc_side_info")
public class PmsAwardCalcSideInfoDto extends CommonQueryDto {

    /**
     * 行记录唯一标识符 (主键, 自增)
     */
    @TableId("id")
    private Long id;

    /**
     * 关联的主计算记录ID (与主计算表关联, 且唯一)
     */
    @TableField("calculation_id")
    private Long calculationId;

    /**
     * 关联的绩效模板ID
     */
    @TableField("template_id")
    private Long templateId;

    /**
     * 医院ID
     */
    @TableField("hospital_id")
    private String hospitalId;

    /**
     * 计算月份 (格式: YYYY-MM)
     */
    @TableField("calc_month")
    private String calcMonth;

    /**
     * 存储指标明细的 JSONB 字段 (例如: [{"metric": "挂号人次", "entity": "范林芬", "value": 195}, ...])
     */
    @TableField("details")
    private String details;

    /**
     * 活动标志 (默认为 '1': 活动, '0': 非活动/逻辑删除)
     */
    @TableField("active_flag")
    private String activeFlag;

    /**
     * 记录创建时间 (带时区)
     */
    @TableField("created_at")
    private Date createdAt;

    /**
     * 记录创建人标识 (例如: 用户ID或用户名)
     */
    @TableField("created_by")
    private String createdBy;

    /**
     * 记录最后更新时间 (带时区)
     */
    @TableField("updated_at")
    private Date updatedAt;

    /**
     * 记录最后更新人标识 (例如: 用户ID或用户名)
     */
    @TableField("updated_by")
    private String updatedBy;

}

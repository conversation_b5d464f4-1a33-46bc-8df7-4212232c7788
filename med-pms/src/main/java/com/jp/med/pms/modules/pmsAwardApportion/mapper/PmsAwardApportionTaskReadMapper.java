package com.jp.med.pms.modules.pmsAwardApportion.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.pms.modules.pmsAwardApportion.dto.PmsAwardApportionTaskDto;
import com.jp.med.pms.modules.pmsAwardApportion.vo.PmsAwardApportionTaskVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 绩效二次分配任务表
 * <AUTHOR>
 * @email -
 * @date 2024-12-02 17:00:58
 */
@Mapper
public interface PmsAwardApportionTaskReadMapper extends BaseMapper<PmsAwardApportionTaskDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<PmsAwardApportionTaskVo> queryList(PmsAwardApportionTaskDto dto);


    /**
     * 查询绩效工资领取通知数据
     * @param dto
     * @return
     */
    PmsAwardApportionTaskVo getTaskByReceiver(PmsAwardApportionTaskDto dto);
}

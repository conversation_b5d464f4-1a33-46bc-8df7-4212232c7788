package com.jp.med.pms.modules.PmsIaeBalance.vo;

import lombok.Data;

/**
 * 项目与收费明细对照
 *
 * <AUTHOR>
 * @email -
 * @date 2024-10-31 15:33:20
 */
@Data
public class PmsProjCrspVo {

    /**
     * 医嘱项目代码
     */
    private String drordCode;

    /**
     * 收费项目代码
     */
    private String chrgitmCode;

    /**
     * 备注
     */
    private String memo;

    /**
     * 记录位置   1开始 >1中间 1000结束  0只有一条
     */
    private Integer loc;

    /**
     * 项目数量
     */
    private String itemNum;

    /**
     * 大类编码
     */
    private String majclsCode;

    /**
     * $column.comments
     */
    private String ybsl;

}

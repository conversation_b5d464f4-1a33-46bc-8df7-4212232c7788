package com.jp.med.pms.modules.pmsCalc.vo;

import com.jp.med.pms.modules.pmsCalc.service.calcEngine.PmsAwardCalcEngineService;
import com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcItemSourceConfigVo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 绩效计算中的变量项VO
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@ToString
@Slf4j
public class PmsCalcVariableItemVo {
    /**
     * 原始数据来源配置
     */
    private PmsAwardCalcItemSourceConfigVo rawData; // 假设PmsAwardCalcItemSourceConfigVo在同一包或可访问包中，或需要import
    /**
     * 变量的唯一标识值（例如："calculated.itemCode"，"const.constantName"，"deptCode.itemCode.default"）
     */
    private String value;
    /**
     * 变量的显示标签（如："项目名称.来源名称"，"常量名称"）
     */
    private String label;
    /**
     * 变量的描述信息
     */
    private String desc;
    /**
     * 变量的默认值或实际数据的字符串形式（如："1"，"100.50"）
     */
    private String data;
    /**
     * 该变量所属的二级指标项目名称（用于分组或显示）
     */
    private String itemName;

    public PmsCalcVariableItemVo(String value, String label, String desc, String data, String itemName,
                                 PmsAwardCalcItemSourceConfigVo rawData) {
        this.value = value;
        this.label = label;
        this.desc = desc;
        this.data = data;
        this.rawData = rawData;
        this.itemName = itemName;
    }

    // getResult方法依赖于CalculationResult。
    // 如果CalculationResult也被做成顶级类，这里的import会变化。
    // 目前假设CalculationResult可能还是内部类或也会被重构。

    /**
     * 按itemName分组
     *
     * @param variableItems 变量项列表
     * @return 按itemName分组后的Map
     */
    public static Map<String, List<PmsCalcVariableItemVo>> groupByItemName(List<PmsCalcVariableItemVo> variableItems) {
        return variableItems.stream()
                .collect(Collectors.groupingBy(PmsCalcVariableItemVo::getItemName));
    }

    /**
     * 获取指定itemName的所有VariableItem
     *
     * @param itemName      项目名称
     * @param variableItems 变量项列表
     * @return 指定itemName的VariableItem列表
     */
    public static List<PmsCalcVariableItemVo> getVariableItemsByItemName(String itemName,
                                                                         List<PmsCalcVariableItemVo> variableItems) {
        return variableItems.stream()
                .filter(item -> item.getItemName() != null && item.getItemName().equals(itemName))
                .collect(Collectors.toList());
    }

    /**
     * 获取所有唯一的itemName
     *
     * @param variableItems 变量项列表
     * @return 唯一的itemName集合
     */
    public static Set<String> getUniqueItemNames(List<PmsCalcVariableItemVo> variableItems) {
        return variableItems.stream()
                .map(PmsCalcVariableItemVo::getItemName)
                .collect(Collectors.toSet());
    }

    /**
     * 获取此变量项对应的计算结果值。
     * <p>
     * 主要用于获取"calculated.xxx"类型变量在完成计算后的实际值。
     * 对于非计算型变量，通常返回其自身的{@code data}字段解析后的Double值。
     * </p>
     *
     * @param calculationResult 包含所有项目最终计算结果的
     *                          {@link PmsAwardCalcEngineService.CalculationResult}
     *                          对象
     * @return 变量对应的计算后结果的Double值。如果变量非计算型、未找到或解析失败，则可能返回其默认数据转换的值或null。
     */
    public Double getResult(PmsAwardCalcEngineService.CalculationResult calculationResult) {
        try {
            if (this.value != null && this.value.startsWith("calculated.")) {
                // 确保calculationResult及其results map不为null
                if (calculationResult != null && calculationResult.getResults() != null) {
                    return calculationResult.getResults().getOrDefault(value.replace("calculated.", ""),
                            (this.data != null ? Double.parseDouble(this.data) : null));
                } else {
                    log.warn("在获取{}的结果时，CalculationResult或其results map为null", this.value);
                    // 如果calculationResult有问题，回退到data
                    return (this.data != null ? Double.parseDouble(this.data) : null);
                }
            } else if (this.data != null) {
                return Double.parseDouble(this.data);
            }
        } catch (NumberFormatException e) {
            log.error("解析数据'{}'为Double时发生NumberFormatException，变量'{}': {}", this.data, this.value, e.getMessage());
        } catch (NullPointerException e) {
            log.error("获取结果时发生NullPointerException，变量'{}': {}", this.value, e.getMessage());
        } catch (Exception e) {
            log.error("获取结果时发生未知错误，变量'{}': {}", this.value, e.getMessage());
        }
        // 如果无法获取有效结果，返回null
        return null;
    }
}
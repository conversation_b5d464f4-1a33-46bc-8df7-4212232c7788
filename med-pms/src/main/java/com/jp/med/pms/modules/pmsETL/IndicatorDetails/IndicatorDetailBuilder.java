package com.jp.med.pms.modules.pmsETL.IndicatorDetails;

import java.util.*;
import java.util.function.Function;

/**
 * 指标详情数据建造者类
 * 使用建造者模式构建指标详情数据，支持链式调用
 */
public class IndicatorDetailBuilder {
    
    private String dataSource;
    private List<TabData> tabs;
    private List<ColumnConfig> columns;
    private List<Map<String, Object>> data;
    private Map<String, Object> metadata;
    
    private IndicatorDetailBuilder() {
        this.tabs = new ArrayList<>();
        this.columns = new ArrayList<>();
        this.data = new ArrayList<>();
        this.metadata = new HashMap<>();
    }
    
    /**
     * 创建建造者实例
     */
    public static IndicatorDetailBuilder create() {
        return new IndicatorDetailBuilder();
    }
    
    /**
     * 设置数据来源
     */
    public IndicatorDetailBuilder dataSource(String dataSource) {
        this.dataSource = dataSource;
        return this;
    }
    
    /**
     * 添加元数据
     */
    public IndicatorDetailBuilder metadata(String key, Object value) {
        this.metadata.put(key, value);
        return this;
    }
    
    /**
     * 设置元数据
     */
    public IndicatorDetailBuilder metadata(Map<String, Object> metadata) {
        this.metadata = metadata != null ? metadata : new HashMap<>();
        return this;
    }
    
    /**
     * 添加标签页（多标签页模式）
     */
    public IndicatorDetailBuilder addTab(String key, String title, 
                                        List<ColumnConfig> columns, 
                                        List<Map<String, Object>> data) {
        this.tabs.add(TabData.builder()
                .key(key)
                .title(title)
                .columns(columns)
                .data(data)
                .build());
        return this;
    }
    
    /**
     * 添加标签页（多标签页模式，支持更多配置）
     */
    public IndicatorDetailBuilder addTab(String key, String title, 
                                        List<ColumnConfig> columns, 
                                        List<Map<String, Object>> data,
                                        boolean disabled,
                                        Map<String, Object> metadata) {
        this.tabs.add(TabData.builder()
                .key(key)
                .title(title)
                .columns(columns)
                .data(data)
                .disabled(disabled)
                .metadata(metadata)
                .build());
        return this;
    }
    
    /**
     * 使用TabBuilder添加标签页
     */
    public IndicatorDetailBuilder addTab(TabBuilder tabBuilder) {
        this.tabs.add(tabBuilder.build());
        return this;
    }
    
    /**
     * 设置单表格列配置（单表格模式）
     */
    public IndicatorDetailBuilder columns(List<ColumnConfig> columns) {
        this.columns = columns != null ? columns : new ArrayList<>();
        return this;
    }
    
    /**
     * 添加单个列配置（单表格模式）
     */
    public IndicatorDetailBuilder addColumn(String title, String key) {
        this.columns.add(ColumnConfig.builder()
                .title(title)
                .key(key)
                .build());
        return this;
    }
    
    /**
     * 添加单个列配置，指定宽度（单表格模式）
     */
    public IndicatorDetailBuilder addColumn(String title, String key, Integer width) {
        this.columns.add(ColumnConfig.builder()
                .title(title)
                .key(key)
                .width(width)
                .build());
        return this;
    }
    
    /**
     * 使用ColumnBuilder添加列配置
     */
    public IndicatorDetailBuilder addColumn(ColumnBuilder columnBuilder) {
        this.columns.add(columnBuilder.build());
        return this;
    }
    
    /**
     * 设置单表格数据（单表格模式）
     */
    public IndicatorDetailBuilder data(List<Map<String, Object>> data) {
        this.data = data != null ? data : new ArrayList<>();
        return this;
    }
    
    /**
     * 添加单行数据（单表格模式）
     */
    public IndicatorDetailBuilder addRow(Map<String, Object> row) {
        this.data.add(row);
        return this;
    }
    
    /**
     * 添加单行数据，使用键值对（单表格模式）
     */
    public IndicatorDetailBuilder addRow(String key, Object value) {
        Map<String, Object> row = new HashMap<>();
        row.put(key, value);
        this.data.add(row);
        return this;
    }
    
    /**
     * 构建指标详情数据
     */
    public IndicatorDetail build() {
        return new IndicatorDetail(dataSource, tabs, columns, data, metadata);
    }
    
    /**
     * 标签页建造者内部类
     */
    public static class TabBuilder {
        private String key;
        private String title;
        private List<ColumnConfig> columns = new ArrayList<>();
        private List<Map<String, Object>> data = new ArrayList<>();
        private boolean disabled = false;
        private Map<String, Object> metadata = new HashMap<>();
        
        public static TabBuilder create(String key, String title) {
            TabBuilder builder = new TabBuilder();
            builder.key = key;
            builder.title = title;
            return builder;
        }
        
        public TabBuilder disabled(boolean disabled) {
            this.disabled = disabled;
            return this;
        }
        
        public TabBuilder metadata(String key, Object value) {
            this.metadata.put(key, value);
            return this;
        }
        
        public TabBuilder metadata(Map<String, Object> metadata) {
            this.metadata = metadata != null ? metadata : new HashMap<>();
            return this;
        }
        
        public TabBuilder addColumn(String title, String key) {
            this.columns.add(ColumnConfig.builder()
                    .title(title)
                    .key(key)
                    .build());
            return this;
        }
        
        public TabBuilder addColumn(String title, String key, Integer width) {
            this.columns.add(ColumnConfig.builder()
                    .title(title)
                    .key(key)
                    .width(width)
                    .build());
            return this;
        }
        
        public TabBuilder addColumn(ColumnBuilder columnBuilder) {
            this.columns.add(columnBuilder.build());
            return this;
        }
        
        public TabBuilder columns(List<ColumnConfig> columns) {
            this.columns = columns != null ? columns : new ArrayList<>();
            return this;
        }
        
        public TabBuilder data(List<Map<String, Object>> data) {
            this.data = data != null ? data : new ArrayList<>();
            return this;
        }
        
        public TabBuilder addRow(Map<String, Object> row) {
            this.data.add(row);
            return this;
        }
        
        public TabData build() {
            return TabData.builder()
                    .key(key)
                    .title(title)
                    .columns(columns)
                    .data(data)
                    .disabled(disabled)
                    .metadata(metadata)
                    .build();
        }
    }
    
    /**
     * 列配置建造者内部类
     */
    public static class ColumnBuilder {
        private String title;
        private String key;
        private Integer width;
        private String align = "left";
        private boolean sortable = false;
        private String fixed;
        private boolean ellipsis = false;
        private ColumnRenderType renderType = ColumnRenderType.TEXT;
        private Map<String, Object> renderConfig = new HashMap<>();
        private Function<Object, Object> dataTransformer;
        private Map<String, Object> metadata = new HashMap<>();
        
        public static ColumnBuilder create(String title, String key) {
            ColumnBuilder builder = new ColumnBuilder();
            builder.title = title;
            builder.key = key;
            return builder;
        }
        
        public ColumnBuilder width(Integer width) {
            this.width = width;
            return this;
        }
        
        public ColumnBuilder align(String align) {
            this.align = align;
            return this;
        }
        
        public ColumnBuilder sortable(boolean sortable) {
            this.sortable = sortable;
            return this;
        }
        
        public ColumnBuilder fixed(String fixed) {
            this.fixed = fixed;
            return this;
        }
        
        public ColumnBuilder ellipsis(boolean ellipsis) {
            this.ellipsis = ellipsis;
            return this;
        }
        
        public ColumnBuilder renderType(ColumnRenderType renderType) {
            this.renderType = renderType;
            return this;
        }
        
        public ColumnBuilder renderConfig(String key, Object value) {
            this.renderConfig.put(key, value);
            return this;
        }
        
        public ColumnBuilder renderConfig(Map<String, Object> renderConfig) {
            this.renderConfig = renderConfig != null ? renderConfig : new HashMap<>();
            return this;
        }
        
        public ColumnBuilder dataTransformer(Function<Object, Object> dataTransformer) {
            this.dataTransformer = dataTransformer;
            return this;
        }
        
        public ColumnBuilder metadata(String key, Object value) {
            this.metadata.put(key, value);
            return this;
        }
        
        public ColumnBuilder metadata(Map<String, Object> metadata) {
            this.metadata = metadata != null ? metadata : new HashMap<>();
            return this;
        }
        
        public ColumnConfig build() {
            return ColumnConfig.builder()
                    .title(title)
                    .key(key)
                    .width(width)
                    .align(align)
                    .sortable(sortable)
                    .fixed(fixed)
                    .ellipsis(ellipsis)
                    .renderType(renderType)
                    .renderConfig(renderConfig)
                    .dataTransformer(dataTransformer)
                    .metadata(metadata)
                    .build();
        }
    }
} 
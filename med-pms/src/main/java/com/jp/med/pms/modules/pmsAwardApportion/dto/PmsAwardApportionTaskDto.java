package com.jp.med.pms.modules.pmsAwardApportion.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.web.multipart.MultipartFile;

import java.math.BigDecimal;
import java.util.List;

/**
 * 绩效二次分配任务表
 *
 * <AUTHOR>
 * @email -
 * @date 2024-12-02 17:00:58
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("pms_award_apportion_task")
public class PmsAwardApportionTaskDto extends CommonQueryDto {

    /**
     * 任务ID
     */
    @TableId("id")
    private Integer id;

    /**
     * 审核批次号
     */
    @TableField("bhcno")
    private String bhcno;

    /**
     * 流程实例编码
     */
    @TableField("process_instance_code")
    private String processInstanceCode;

    /**
     * 计算月份
     */
    @TableField("calc_month")
    private String calcMonth;

    /**
     * 绩效模板ID
     */
    @TableField("template_id")
    private String templateId;

    /**
     * 绩效分配总额，最多10位数字，4位小数
     */
    @TableField("amount")
    private BigDecimal amount;

    /**
     * 签名
     */
    @TableField("sign")
    private String sign;

    /**
     * 附件
     */
    @TableField("att")
    private String att;

    /**
     * 附件名称
     */
    @TableField("att_name")
    private String attName;

    /**
     * 任务状态 0待领取，1已领取,分配中，2已确认分配
     */
    @TableField("status")
    private String status;

    /**
     * 领用时间
     */
    @TableField("receive_time")
    private String receiveTime;

    /**
     * 领用人
     */
    @TableField("receiver")
    private String receiver;

    /**
     * 领用人签名
     */
    @TableField("receiver_sign")
    private String receiverSign;

    /**
     * 领取方式：ONLINE-线上领取，OFFLINE-线下领取
     */
    @TableField("receive_type")
    private String receiveType;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;

    /**
     * 创建人
     */
    @TableField("creator")
    private String creator;

    /**
     * 创建人签字
     */
    @TableField("creator_sign")
    private String creatorSign;

    /**
     * 创建时间，以字符串形式存储
     */
    @TableField("create_time")
    private String createTime;

    /**
     * 更新人
     */
    @TableField("updator")
    private String updator;

    /**
     * 更新时间，以字符串形式存储
     */
    @TableField("update_time")
    private String updateTime;

    /**
     * 活动标志，默认为'1'
     */
    @TableField("active_flag")
    private String activeFlag;

    /**
     * 医院ID
     */
    @TableField("hospital_id")
    private String hospitalId;

    @TableField(exist = false)
    private List<PmsAwardApportionDetailDto> details;

    @TableField(exist = false)
    private String dtoJson;

    /**
     * 附件
     */
    @TableField(exist = false)
    private List<MultipartFile> attFiles;

    /**
     * 签名文件
     */
    @TableField(exist = false)
    private MultipartFile signFile;

    @TableField(exist = false)
    private String orgCode;
}

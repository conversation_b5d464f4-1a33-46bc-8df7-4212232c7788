package com.jp.med.pms.modules.pmsETL.scheduler;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceConfigDto;
import com.jp.med.pms.modules.PmsIaeBalance.service.read.PmsIaeBalanceConfigReadService;
import com.jp.med.pms.modules.pmsBaseConfig.PmsDeptMapping.dto.PmsDeptMappingDto;
import com.jp.med.pms.modules.pmsBaseConfig.PmsDeptMapping.servie.PmsDeptMappingWriteServiceImpl;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.dto.PmsAwardCollectionResultDto;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.dto.PmsCalcImportMetadataDto;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.dto.PmsIaeBalanceCollectionResultDto;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.dto.PmsIaeBalanceImportMetadataDto;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.service.PmsCalcImportMetadataWriteService;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.service.PmsIaeBalanceCollectionResultWriteService;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcItemConfigDto;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.PmsAwardCalcItemConfigReadService;
import com.jp.med.pms.modules.pmsETL.config.dto.PmsCaptureConfigDto;
import com.jp.med.pms.modules.pmsETL.scheduler.status.TaskStatusEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 绩效数据加载器
 */
@Slf4j
@Component
public class PmsDataLoader {

    /**
     * 科室代码版本
     */
    private static final String PERFORMANCE_DEPT_VERSION = "HIS";
    /**
     * 医院ID
     */
    private static final String HOSPITAL_ID = "zjxrmyy";
    /**
     * 采集系统用户
     */
    private static final String SYSTEM_USER = "system";
    /**
     * 采集类型
     */
    private static final String BACKEND_COLLECTION = "backend";

    @Resource
    private PmsDeptMappingWriteServiceImpl pmsDeptMappingService;
    @Resource
    private PmsIaeBalanceCollectionResultWriteService pmsIaeBalanceCollectionResultWriteService;
    @Resource
    private PmsCalcImportMetadataWriteService pmsCalcImportMetadataWriteService;
    @Resource
    private PmsIaeBalanceConfigReadService pmsIaeBalanceConfigReadService;
    @Resource
    private PmsAwardCalcItemConfigReadService pmsAwardCalcItemConfigReadService;




    /**
     * 加载转换后的数据
     *
     * @param mappingType        数据映射类型
     * @param transformedDataMap 转换后的数据
     * @param reportOssPathMap   报告OSS路径映射
     * @param collectionDate     采集日期
     * @param updateStatus       状态更新函数
     * @param config             采集配置
     */
    public void loadTransformedData(String mappingType,
                                     HashMap<String, HashMap<String, String>> transformedDataMap,
                                     HashMap<String, String> reportOssPathMap,
                                     Date collectionDate,
                                     PmsCaptureScheduler.UpdateStatus updateStatus,
                                     PmsCaptureConfigDto config) {
        // 获取部门映射
        Map<String, PmsDeptMappingDto> deptMapping = getDeptMapping();

        // 获取当前时间
        Date nowDate = new Date();
        // 计数器
        int i = 0;
        // 开始加载数据
        for (Map.Entry<String, HashMap<String, String>> entry : transformedDataMap.entrySet()) {
            String dataItemCode = entry.getKey();
            HashMap<String, String> deptCodeValueMap = entry.getValue();
            updateStatus.accept(TaskStatusEnum.LOADING_ITEM_START, null, "开始加载第 " + (++i) + " 个数据项: " + dataItemCode);
            var formattedCollectionDate = DateUtil.format(collectionDate, "yyyy/MM/dd");
            switch (mappingType) {
                case "pmsIaeReportItem":
                    log.info(
                            "\n╔═══════════════════════════════════════════\n" +
                                    "║ 开始加载成本控制数据\n" +
                                    "║ 数据项编码: {}\n" +
                                    "║ 数据条数: {}\n" +
                                    "║ 批次编号: {}\n" +
                                    "║ 采集时间: {}\n" +
                                    "╚═══════════════════════════════════════════",
                            dataItemCode, deptCodeValueMap.size(), config.getScheduleduleBatchCode(),
                            formattedCollectionDate);
                    // 保存成本控制数据
                    saveIaeBalanceData(dataItemCode, deptCodeValueMap, reportOssPathMap,
                            collectionDate, nowDate, deptMapping, config.getScheduleduleBatchCode());
                    break;
                case "pmsAwardCalcItem":
                    log.info(
                            "\n╔═══════════════════════════════════════════\n" +
                                    "║ 开始加载绩效计算数据\n" +
                                    "║ 数据项编码: {}\n" +
                                    "║ 数据条数: {}\n" +
                                    "║ 批次编号: {}\n" +
                                    "║ 采集时间: {}\n" +
                                    "╚═══════════════════════════════════════════",
                            dataItemCode, deptCodeValueMap.size(), config.getScheduleduleBatchCode(),
                            formattedCollectionDate);
                    // 保存绩效计算数据
                    saveAwardCalcData(dataItemCode, deptCodeValueMap, reportOssPathMap,
                            collectionDate, nowDate, deptMapping, config.getScheduleduleBatchCode());
                    break;
                default:
                    // 不支持的数据映射类型
                    updateStatus.accept(TaskStatusEnum.LOADING_DATA_MAPPING_TYPE_ERROR, "不支持的数据映射类型: " + mappingType,
                            null);
                    throw new IllegalArgumentException("不支持的数据映射类型: " + mappingType);
            }
            updateStatus.accept(TaskStatusEnum.LOADING_ITEM_SUCCESS, null, "数据项 " + dataItemCode + " 加载完成");
        }
        updateStatus.accept(TaskStatusEnum.LOADING_DATA_SUCCESS, null, null); // 更新状态：数据加载成功
    }

    /**
     * 获取部门映射关系
     *
     * @return 返回部门映射Map, key可以是部门代码或部门名称, value是部门映射对象
     */
    private Map<String, PmsDeptMappingDto> getDeptMapping() {
        // 构建查询条件
        var queryWrapper = Wrappers.lambdaQuery(PmsDeptMappingDto.class)
                .eq(PmsDeptMappingDto::getSourceSystem, PERFORMANCE_DEPT_VERSION);

        // 获取所有部门映射数据
        var deptMappingList = pmsDeptMappingService.list(queryWrapper);

        // 按部门代码建立映射
        var codeMap = deptMappingList.stream()
                .collect(Collectors.toMap(
                        PmsDeptMappingDto::getSourceDeptCode,
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复的key,保留第一个
                ));

        // 按部门名称建立映射
        var nameMap = deptMappingList.stream()
                .collect(Collectors.toMap(
                        PmsDeptMappingDto::getSourceDeptName,
                        Function.identity(),
                        (existing, replacement) -> existing // 如果有重复的key,保留第一个
                ));

        // 合并两个Map,使得可以同时支持按代码和名称查找
        codeMap.putAll(nameMap);

        return codeMap;
    }

    /**
     * 保存
     *
     * @param dataItemCode     数据项代码
     * @param deptCodeValueMap 部门代码值映射
     * @param reportOssPathMap 报告OSS路径映射
     * @param collectionDate   采集日期
     * @param nowDate          当前日期
     * @param deptMapping      部门映射
     * @param batchCode        批次代码
     */
    private void saveIaeBalanceData(String dataItemCode,
                                    HashMap<String, String> deptCodeValueMap,
                                    HashMap<String, String> reportOssPathMap,
                                    Date collectionDate,
                                    Date nowDate,
                                    Map<String, PmsDeptMappingDto> deptMapping,
                                    String batchCode) {

        PmsIaeBalanceImportMetadataDto metadata = new PmsIaeBalanceImportMetadataDto();
        // 数据项代码
        metadata.setItemCode(dataItemCode);
        // 部门代码版本
        metadata.setDeptCodeVersion(PERFORMANCE_DEPT_VERSION);
        // 采集日期
        metadata.setCollectionDate(collectionDate);
        // 已填充列数
        metadata.setFilledColumns(deptCodeValueMap.size());
        // 总列数
        metadata.setTotalColumns(deptCodeValueMap.size());
        // 百分比
        metadata.setPercentage(100.0D);
        // 创建时间
        metadata.setCreatedAt(nowDate);
        // 采集方法
        metadata.setCollectionMethod(BACKEND_COLLECTION);
        // 文件名
        metadata.setFileName(String.join(",", reportOssPathMap.values()));
        // 批次代码
        metadata.setScheduleduleBatchCode(batchCode);
        // 文件URL
        metadata.setFileUrl(String.join(",", reportOssPathMap.values()));

        List<PmsIaeBalanceCollectionResultDto> resultList = deptCodeValueMap.entrySet().stream()
                .map(entry -> createIaeBalanceResult(entry.getKey(), entry.getValue(),
                        dataItemCode, nowDate, deptMapping))
                .collect(Collectors.toList());

        metadata.setIaeBalanceCollectionResultDtoList(new ArrayList<>(resultList));
        pmsIaeBalanceCollectionResultWriteService.saveMetaDataAndCollectionResult(metadata);
    }

    /**
     * 保存绩效计算数据
     *
     * @param dataItemCode     数据项代码
     * @param deptCodeValueMap 部门代码值映射
     * @param reportOssPathMap 报告OSS路径映射
     * @param collectionDate   采集日期
     * @param nowDate          当前日期
     * @param deptMapping      部门映射
     * @param batchCode        批次代码
     */
    private void saveAwardCalcData(String dataItemCode,
                                   HashMap<String, String> deptCodeValueMap,
                                   HashMap<String, String> reportOssPathMap,
                                   Date collectionDate,
                                   Date nowDate,
                                   Map<String, PmsDeptMappingDto> deptMapping,
                                   String batchCode) {

        PmsCalcImportMetadataDto metadata = new PmsCalcImportMetadataDto();
        // 数据项代码
        metadata.setItemCode(dataItemCode);
        // 生成时间
        metadata.setGenerateTime(nowDate);
        // 部门代码版本
        metadata.setDeptCodeVersion(PERFORMANCE_DEPT_VERSION);
        // 采集日期
        metadata.setCollectionDate(collectionDate);
        // 文件名
        metadata.setFileName(String.join(",", reportOssPathMap.values()));
        // 文件URL
        metadata.setFileUrl(String.join(",", reportOssPathMap.values()));
        // 已填充列数
        metadata.setFilledColumns(deptCodeValueMap.size());
        // 总列数
        metadata.setTotalColumns(deptCodeValueMap.size());
        // 百分比
        metadata.setPercentage(100.0D);
        // 创建时间
        metadata.setCreatedAt(nowDate);
        // 采集方法
        metadata.setCollectionMethod(BACKEND_COLLECTION);
        // 批次代码
        metadata.setScheduleduleBatchCode(batchCode);
        // MD5
        metadata.setMd5("");

        List<PmsAwardCollectionResultDto> resultList = deptCodeValueMap.entrySet()
                .stream()
                .map(entry -> createAwardCalcResult(entry.getKey(), entry.getValue(),
                        dataItemCode, nowDate, deptMapping))
                .collect(Collectors.toList());

        metadata.setAwardCollectionResultDtos(new ArrayList<>(resultList));
        pmsCalcImportMetadataWriteService.saveMetaDataAndResult(metadata);
    }

    /**
     * 创建成本控制导入结果
     *
     * @param deptCode     部门代码
     * @param value        值
     * @param dataItemCode 数据项代码
     * @param nowDate      当前日期
     * @param deptMapping  部门映射
     */
    private PmsIaeBalanceCollectionResultDto createIaeBalanceResult(
            String deptCode,
            String value,
            String dataItemCode,
            Date nowDate,
            Map<String, PmsDeptMappingDto> deptMapping) {

        PmsIaeBalanceCollectionResultDto resultDto = new PmsIaeBalanceCollectionResultDto();
        // 数据项代码
        resultDto.setItemCode(dataItemCode);
        // 数据项名称

        PmsIaeBalanceConfigDto config = pmsIaeBalanceConfigReadService.lambdaQuery()
                .eq(PmsIaeBalanceConfigDto::getItemCode, dataItemCode).select()
                .one();
        if (config != null) {
            resultDto.setItemName(config.getItemName());
        }

        // 数据项值
        resultDto.setItemValue(value);
        // 部门代码版本
        resultDto.setDeptCodeVersion(PERFORMANCE_DEPT_VERSION);
        // 采集日期
        resultDto.setCollectionDate(nowDate);
        // 导入部门代码
        // resultDto.setImportDeptCode(deptCode);
        // 创建人
        resultDto.setCreatdBy(SYSTEM_USER);
        // 医院ID
        resultDto.setHospitalId(HOSPITAL_ID);
        // 创建时间
        resultDto.setCreateTime(nowDate);

        // 使用正则表达式判断deptCode是否只包含数字
        boolean isNumeric = deptCode.matches("\\d+");
        try {
            if (isNumeric) {
                resultDto.setImportDeptCode(deptCode);
            } else {
                resultDto.setImportDeptCode(deptMapping.get(deptCode).getHrmOrgId());
            }
        } catch (Exception e) {
            log.error("部门映射为空: {}", deptCode);
        }

        if (deptMapping.get(deptCode) == null) {
            log.error("部门映射为空: {}", deptCode);
        }
        // 设置部门相关信息
        Optional.ofNullable(deptMapping.get(deptCode))
                .ifPresent(mapping -> {
                    resultDto.setDeptName(mapping.getSourceDeptName());
                    resultDto.setHrpDeptCode(mapping.getHrmOrgId());
                    resultDto.setImportDeptCode(mapping.getSourceDeptCode());
                });

        return resultDto;
    }

    /**
     * 创建成本控制导入结果
     *
     * @param deptCode     部门代码
     * @param value        值
     * @param dataItemCode 数据项代码
     * @param nowDate      当前日期
     * @param deptMapping  部门映射
     */
    private PmsAwardCollectionResultDto createAwardCalcResult(
            String deptCode,
            String value,
            String dataItemCode,
            Date nowDate,
            Map<String, PmsDeptMappingDto> deptMapping) {

        PmsAwardCollectionResultDto resultDto = new PmsAwardCollectionResultDto();
        // 数据项代码
        resultDto.setItemCode(dataItemCode);
        // 数据项名称
        resultDto.setItemName(dataItemCode);
        PmsAwardCalcItemConfigDto config = pmsAwardCalcItemConfigReadService.lambdaQuery()
                .eq(PmsAwardCalcItemConfigDto::getItemCode, dataItemCode).select()
                .one();
        if (config != null) {
            resultDto.setItemName(config.getItemName());
        } else {
            log.info("数据项名称为空: {}");
        }
        // 数据项值
        resultDto.setItemValue(value);
        // 部门代码版本
        resultDto.setDeptCodeVersion(PERFORMANCE_DEPT_VERSION);
        // 采集日期
        resultDto.setCollectionDate(nowDate);
        // 导入部门代码
        resultDto.setImportDeptCode(deptCode);
        // 创建人
        resultDto.setCreatdBy(SYSTEM_USER);
        // 医院ID
        resultDto.setHospitalId(HOSPITAL_ID);
        // 创建时间
        resultDto.setCreateTime(nowDate);

        // 设置部门相关信息
        Optional.ofNullable(deptMapping.get(deptCode))
                .ifPresent(mapping -> {
                    resultDto.setDeptName(mapping.getSourceDeptName());
                    resultDto.setHrpDeptCode(mapping.getHrmOrgId());
                });

        return resultDto;
    }


}
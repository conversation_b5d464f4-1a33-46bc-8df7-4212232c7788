package com.jp.med.pms.modules.pmsCalc.service.calcEngine;

import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.*;

/**
 * 绩效计算进度日志。
 * <p>
 * </p>
 */
@Service
@Slf4j

public class PmsCalcProgressService {

    // 使用ConcurrentHashMap存储每个计算任务的SseEmitter
    private final Map<String, SseEmitter> emitters = new ConcurrentHashMap<>();

    // 为每个计算任务创建一个消息队列
    private final Map<String, BlockingQueue<ProgressMessage>> messageQueues = new ConcurrentHashMap<>();

    // 消息处理线程池
    private final ExecutorService messageProcessor = Executors.newFixedThreadPool(2);

    // 标记服务是否正在运行
    private volatile boolean running = true;

    @PostConstruct
    public void init() {
        // 启动消息处理线程
        messageProcessor.submit(this::processMessages);
    }

    @PreDestroy
    public void destroy() {
        running = false;
        messageProcessor.shutdown();
        try {
            if (!messageProcessor.awaitTermination(60, TimeUnit.SECONDS)) {
                messageProcessor.shutdownNow();
            }
        } catch (InterruptedException e) {
            messageProcessor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    private void processMessages() {
        while (running) {
            try {
                // 处理所有队列中的消息
                for (Map.Entry<String, BlockingQueue<ProgressMessage>> entry : messageQueues.entrySet()) {
                    String calcMonth = entry.getKey();
                    BlockingQueue<ProgressMessage> queue = entry.getValue();

                    ProgressMessage message;
                    while ((message = queue.poll()) != null) {
                        try {
                            doSendProgress(calcMonth, message);
                        } catch (Exception e) {
                            log.error("处理消息失败: calcMonth={}, message={}", calcMonth, message, e);
                        }
                    }
                }

                // 短暂休眠避免CPU占用过高
                Thread.sleep(50);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                break;
            }
        }
    }

    /**
     * 创建SSE连接
     *
     * @param calcMonth 计算月份
     * @return SseEmitter
     */
    /**
     * 创建SSE连接
     *
     * @param calcMonth 计算月份
     * @return SseEmitter SSE发射器对象
     */
    public SseEmitter createConnection(String calcMonth) {
        // 创建SSE发射器,设置1小时超时
        SseEmitter emitter = new SseEmitter(3600000L);

        var now = System.currentTimeMillis();
        // 注册连接完成回调
        emitter.onCompletion(() -> {
            // 记录连接完成日志,包含计算月份和耗时
            log.info("SSE连接完成, calcMonth: {}, 耗时: {} ms",
                    calcMonth, System.currentTimeMillis() - now);
            // 从发射器映射中移除
            ProgressMessage.builder()
                    .type(ProgressMessage.MessageType.COMPLETE)
                    .message("连接完成")
                    .build();
            emitters.remove(calcMonth);
        });

        // 注册连接超时回调
        emitter.onTimeout(() -> {
            // 记录超时日志
            log.info("SSE连接超时, calcMonth: {}, 超时时间: {} ms",
                    calcMonth, emitter.getTimeout());
            // 完成连接并从映射中移除
            ProgressMessage timeoutMessage = ProgressMessage.builder()
                    .type(ProgressMessage.MessageType.ERROR)
                    .message("连接超时")
                    .build();
            sendProgress(calcMonth, timeoutMessage);
            emitter.complete();
            emitters.remove(calcMonth);
        });

        // 注册错误处理回调
        emitter.onError(ex -> {
            // 记录错误日志
            log.error("SSE连接错误, calcMonth: {}, 耗时: {} ms",
                    calcMonth, System.currentTimeMillis() - Optional.ofNullable(emitter.getTimeout()).orElse(0L), ex);
            // 完成连接并从映射中移除
            ProgressMessage errorMessage = ProgressMessage.builder()
                    .type(ProgressMessage.MessageType.ERROR)
                    .message("连接错误")
                    .build();
            sendProgress(calcMonth, errorMessage);
            emitters.remove(calcMonth);
        });

        // 将发射器添加到映射中
        emitters.put(calcMonth, emitter);
        return emitter;
    }

    /**
     * 发送进度消息
     */
    public void sendProgress(String calcMonth, ProgressMessage message) {
        // 确保该计算月份有对应的消息队列
        messageQueues.computeIfAbsent(calcMonth, k -> new LinkedBlockingQueue<>());

        // 将消息添加到队列
        try {
            messageQueues.get(calcMonth).offer(message, 1, TimeUnit.SECONDS);
        } catch (InterruptedException e) {
            log.error("添加消息到队列失败: calcMonth={}, message={}", calcMonth, message, e);
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 实际发送消息的方法
     */
    private void doSendProgress(String calcMonth, ProgressMessage message) {
        SseEmitter emitter = emitters.get(calcMonth);
        if (emitter != null) {
            try {
                Map<String, Object> progressInfo = new HashMap<>();
                progressInfo.put("calcMonth", calcMonth);
                String jsonStr = JSONUtil.toJsonStr(message);
                progressInfo.put("message", jsonStr);
                progressInfo.put("timestamp", new Date());

                emitter.send(SseEmitter.event()
                        .name("progress")
                        .data(progressInfo));

            } catch (IOException e) {
                log.error("发送进度消息失败", e);
                emitter.complete();
                emitters.remove(calcMonth);
                messageQueues.remove(calcMonth);
            }
        }
    }

    /**
     * 完成计算
     */
    public void complete(String calcMonth) {
        SseEmitter emitter = emitters.get(calcMonth);
        if (emitter != null) {
            emitter.complete();
            emitters.remove(calcMonth);
            messageQueues.remove(calcMonth);
        }
    }

}

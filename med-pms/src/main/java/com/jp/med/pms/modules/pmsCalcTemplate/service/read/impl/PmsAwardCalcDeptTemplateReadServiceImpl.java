package com.jp.med.pms.modules.pmsCalcTemplate.service.read.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.pms.modules.pmsCalcTemplate.dto.PmsAwardCalcDeptTemplateDto;
import com.jp.med.pms.modules.pmsCalcTemplate.mapper.read.PmsAwardCalcDeptTemplateReadMapper;
import com.jp.med.pms.modules.pmsCalcTemplate.service.read.PmsAwardCalcDeptTemplateReadService;
import com.jp.med.pms.modules.pmsCalcTemplate.vo.PmsAwardCalcDeptTemplateVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Transactional(readOnly = true)
@Service
public class PmsAwardCalcDeptTemplateReadServiceImpl
        extends ServiceImpl<PmsAwardCalcDeptTemplateReadMapper, PmsAwardCalcDeptTemplateDto>
        implements PmsAwardCalcDeptTemplateReadService {

    @Autowired
    private PmsAwardCalcDeptTemplateReadMapper pmsAwardCalcDeptTemplateReadMapper;

    @Override
    public List<PmsAwardCalcDeptTemplateVo> queryList(PmsAwardCalcDeptTemplateDto dto) {
        return pmsAwardCalcDeptTemplateReadMapper.queryList(dto);
    }

    @Override
    public List<PmsAwardCalcDeptTemplateVo> queryPageList(PmsAwardCalcDeptTemplateDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return pmsAwardCalcDeptTemplateReadMapper.queryList(dto);
    }

    @Override
    public List<PmsAwardCalcDeptTemplateDto> queryTemplatesByItemCode(String itemCode) {
        return pmsAwardCalcDeptTemplateReadMapper.queryTemplatesByItemCode(itemCode);
    }

}

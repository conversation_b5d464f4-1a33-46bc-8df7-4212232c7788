package com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.dto.PmsDeptReportConfigDto;

import java.util.List;

/**
 * 科室上报权限配置读取Service
 *
 * <AUTHOR>
 * @date 2024-11-25
 */
public interface PmsDeptReportConfigReadService {

    /**
     * 分页查询科室上报权限配置
     */
    IPage<PmsDeptReportConfigDto> queryPageList(PmsDeptReportConfigDto dto);

    /**
     * 查询科室上报权限配置列表
     */
    List<PmsDeptReportConfigDto> queryList(PmsDeptReportConfigDto dto);

    /**
     * 根据ID查询
     */
    PmsDeptReportConfigDto queryById(Integer id);

    /**
     * 根据绩效科室名称查询上报权限
     */
    PmsDeptReportConfigDto queryByPmsDeptName(String pmsDeptName);

    /**
     * 根据HRP机构ID查询上报权限
     */
    PmsDeptReportConfigDto queryByHrpOrgId(String hrpOrgId);

    /**
     * 检查用户是否有权限上报指定科室和类型的数据
     */
    boolean checkReportPermission(String pmsDeptName, String hrpOrgId, String reportTypeCode, String empCode);

    /**
     * 查询用户可上报的科室列表
     */
    List<PmsDeptReportConfigDto> queryUserReportableDepts(String empCode);
} 
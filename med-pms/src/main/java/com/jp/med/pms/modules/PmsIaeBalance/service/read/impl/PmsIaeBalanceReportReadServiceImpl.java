package com.jp.med.pms.modules.PmsIaeBalance.service.read.impl;

import cn.afterturn.easypoi.excel.annotation.Excel;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.constant.OSSConst;
import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.OSSUtil;
import com.jp.med.pms.common.annotation.ItemCode;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceConfigDto;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceReportDeptConfigDto;
import com.jp.med.pms.modules.PmsIaeBalance.dto.PmsIaeBalanceReportDto;
import com.jp.med.pms.modules.PmsIaeBalance.mapper.read.PmsIaeBalanceConfigReadMapper;
import com.jp.med.pms.modules.PmsIaeBalance.mapper.read.PmsIaeBalanceReportReadMapper;
import com.jp.med.pms.modules.PmsIaeBalance.service.read.PmsIaeBalanceReportReadService;
import com.jp.med.pms.modules.PmsIaeBalance.vo.PmsIaeBalanceReportVo;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.mapper.PmsIaeBalanceCollectionResultReadMapper;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.service.PmsIaeBalanceCollectionResultReadServiceImpl;
import com.jp.med.pms.modules.pmsCalc.PmsCalcImport.vo.PmsIaeBalanceCollectionResultVo;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

@Transactional(readOnly = true)
@Service
@Slf4j
public class PmsIaeBalanceReportReadServiceImpl extends
        ServiceImpl<PmsIaeBalanceReportReadMapper, PmsIaeBalanceReportDto> implements PmsIaeBalanceReportReadService {

    @Autowired
    private PmsIaeBalanceReportReadMapper pmsIaeBalanceReportReadMapper;
    @Autowired
    private PmsIaeBalanceCollectionResultReadMapper pmsIaeBalanceCollectionResultReadMapper;
    @Autowired
    private PmsIaeBalanceCollectionResultReadServiceImpl pmsIaeBalanceCollectionResultReadServiceImpl;
    @Autowired
    private PmsIaeBalanceConfigReadMapper pmsIaeBalanceConfigReadMapper;

    @Autowired
    private PmsIaeBalanceReportDeptConfigReadServiceImpl pmsIaeBalanceReportDeptConfigReadService;

    @Override
    public List<LinkedHashMap<String, Object>> queryList(PmsIaeBalanceReportDto dto) {
        // 动态读取生成报表数据
        // 1. 读取科室成本项目配置表
        var queryPmsIaeBalanceReportDeptConfigDto = new PmsIaeBalanceReportDeptConfigDto();
        queryPmsIaeBalanceReportDeptConfigDto.setActive("1");
        var iaeBalanceReportDeptConfigVos = pmsIaeBalanceReportDeptConfigReadService
                .queryList(queryPmsIaeBalanceReportDeptConfigDto);
        // 2. 读取成本指标配置表
        var queryPmsIaeBalanceConfigDto = new PmsIaeBalanceConfigDto();
        queryPmsIaeBalanceConfigDto.setStatus(1);
        queryPmsIaeBalanceConfigDto.setItemBigClass(dto.getItemBigClass());
        var iaeBalanceConfigVos = pmsIaeBalanceConfigReadMapper.queryList(queryPmsIaeBalanceConfigDto);
        // 2.1 对成本指标配置表按采集频率分类 (主要处理 每月 和 每年)

        /*
          key 指标代码 itemCode
          value map key 科室代码 deptCode
         */
        Map<String, HashMap<String, PmsIaeBalanceCollectionResultVo>> iaeBalanceResultMap = new ConcurrentHashMap<>();
        // 按"每月"或未填写采集频率分类
        var monthlyIaeBalanceConfigVos = iaeBalanceConfigVos.stream()
                .filter(item -> "每月".equals(item.getStatisticsFrequency())
                        || StrUtil.isBlank(item.getStatisticsFrequency()))
                .collect(Collectors.toList());
        // 按"每年"或未填写采集频率分类
        var annualIaeBalanceConfigVos = iaeBalanceConfigVos.stream()
                .filter(item -> "每年".equals(item.getStatisticsFrequency()))
                .collect(Collectors.toList());

        // 2.2 获取每月成本指标采集到的数据（并行流优化）
        monthlyIaeBalanceConfigVos.parallelStream().forEach(item -> {
            List<PmsIaeBalanceCollectionResultVo> monthlyIaeBalanceCollectionResultVos = pmsIaeBalanceCollectionResultReadServiceImpl
                    .getIaeBalanceCollectionResult(
                            dto.getStatMonth(),
                            item.getItemCode());
            HashMap<String, PmsIaeBalanceCollectionResultVo> deptResultMap = new HashMap<>();
            if (monthlyIaeBalanceCollectionResultVos != null) {
                // 遍历采集结果列表，按科室代码分组
                for (PmsIaeBalanceCollectionResultVo vo : monthlyIaeBalanceCollectionResultVos) {
                    // 以科室代码为key
                    deptResultMap.put(vo.getHrpDeptCode(), vo);
                }
            }
            iaeBalanceResultMap.put(item.getItemCode(), deptResultMap);
        });

        // 2.3 获取每年成本指标采集到的数据 todo (默认为年度指标为都为今年1月的数据)
        // 获取 dto.getStatMonth(), 的一月份 格式为 yyyyMM
        String firstMonth = dto.getStatMonth().substring(0, 7) + "01";
        annualIaeBalanceConfigVos.parallelStream().forEach(item -> {
            List<PmsIaeBalanceCollectionResultVo> annualIaeBalanceCollectionResultVos = pmsIaeBalanceCollectionResultReadServiceImpl
                    .getIaeBalanceCollectionResult(
                            firstMonth,
                            item.getItemCode());
            HashMap<String, PmsIaeBalanceCollectionResultVo> deptResultMap = new HashMap<>();
            if (annualIaeBalanceCollectionResultVos != null) {
                // 遍历采集结果列表，按科室代码分组
                for (PmsIaeBalanceCollectionResultVo vo : annualIaeBalanceCollectionResultVos) {
                    // 以科室代码为key
                    deptResultMap.put(vo.getHrpDeptCode(), vo);
                }
            }
            iaeBalanceResultMap.put(item.getItemCode(), deptResultMap);
        });

        // 3. 开始填充数据
        // 格式为 前2列为 科室名称 科室代码，接着的为kv 指标代码 指标值
        List<LinkedHashMap<String, Object>> resultList = iaeBalanceReportDeptConfigVos.stream().map(item -> {
            var resultMap = new LinkedHashMap<String, Object>();
            resultMap.put("config", item);
            if (StrUtil.isNotBlank(item.getDeptName())) {
                resultMap.put("deptName", item.getDeptName());
            } else {
                resultMap.put("deptName", item.getPmsDeptName());
            }
            if (StrUtil.isNotBlank(item.getDeptCode())) {
                resultMap.put("deptCode", item.getDeptCode());
            }
            // 循环指标

            // 科室启用的指标
            var enabledIndicatorsSet = new HashSet<String>();
            Optional.ofNullable(item.getEnabledIndicators()).ifPresent(enabledIndicators -> {
                if (StrUtil.isNotBlank(enabledIndicators)) {
                    var enabledIndicatorsList = StrUtil.split(enabledIndicators, ",");
                    enabledIndicatorsSet.addAll(enabledIndicatorsList);
                }
            });
            // 按指标配置排序填充数据
            iaeBalanceConfigVos.forEach(config -> {
                // 只处理已启用的指标
                if (!enabledIndicatorsSet.contains(config.getItemCode())) {
                    return;
                }
                // 获取该指标对应的科室采集结果
                var deptValueMap = iaeBalanceResultMap.get(config.getItemCode());
                if (deptValueMap == null) {
                    return;
                }
                var itemValue = deptValueMap.get(item.getDeptCode());
                if (itemValue == null) {
                    return;
                }
                String valueStr = itemValue.getItemValue();
                if (StrUtil.isBlank(valueStr)) {
                    return;
                }
                // 尝试将指标值转换为BigDecimal
                try {
                    // 保留2位小数
                    BigDecimal bigDecimal = new BigDecimal(valueStr);
                    bigDecimal = bigDecimal.setScale(2, BigDecimal.ROUND_HALF_UP);
                    resultMap.put(config.getItemCode(), bigDecimal);
                } catch (NumberFormatException e) {
                    // 转换失败时记录错误日志
                    log.error("{}:{}指标值:{}-转换为BigDecimal失败:", item.getDeptCode(), config.getItemCode(), valueStr);
                }
            });

            return resultMap;
        }).collect(Collectors.toList());
        return resultList;
    }

    @Override
    public List<PmsIaeBalanceReportVo> queryPageList(PmsIaeBalanceReportDto dto) {

        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        dto.setSqlAutowiredHospitalCondition(true);
        return pmsIaeBalanceReportReadMapper.queryList(dto);
        // List<PmsIaeBalanceCollectionResultVo> vo =
        // pmsIaeBalanceCollectionResultReadMapper.queryBalanceReportList(dto);
        // return PageUtil.getPageList(new
        // ArrayList<>(getBalanceReportData(vo).values()), dto.getPageNum(),
        // dto.getPageSize());
    }

    // ================
    @Override
    public String reportFileDownload(PmsIaeBalanceReportDto dto) {
        List<PmsIaeBalanceCollectionResultVo> vo = pmsIaeBalanceCollectionResultReadMapper.queryBalanceReportList(dto);
        Map<String, Map<String, Object>> balanceReportData = getBalanceReportData(vo);
        return generateReport(balanceReportData, dto);
    }

    // 收支结余报表封装
    private Map<String, Map<String, Object>> getBalanceReportData(List<PmsIaeBalanceCollectionResultVo> list) {

        // 使用 HashMap 按科室名称分组
        Map<String, Map<String, Object>> departmentMap = new HashMap<>();

        for (PmsIaeBalanceCollectionResultVo item : list) {
            Date collectionDate = item.getCollectionDate(); // 采集时间
            String deptName = item.getDeptName(); // 获取科室名称
            String hrpDeptCode = item.getHrpDeptCode();// 获取HRP科室代码
            String itemCode = item.getItemCode(); // 获取绩效代码
            String itemValue = item.getItemValue(); // 获取数量/分数

            // 如果该科室还未添加到 departmentMap 中，则初始化
            if (!departmentMap.containsKey(hrpDeptCode)) {
                Map<String, Object> map = new HashMap<>();
                map.put("collectionDate", collectionDate);
                map.put("deptCode", hrpDeptCode);
                map.put("deptName", deptName);
                departmentMap.put(hrpDeptCode, map);

            }

            // 获取科室对应的 Map
            Map<String, Object> itemMap = departmentMap.get(hrpDeptCode);

            // 将 itemCode 和 itemValue 添加到该科室的 Map 中
            itemMap.put(itemCode, itemValue);
        }

        return departmentMap;
    }

    private String generateReport(Map<String, Map<String, Object>> data, PmsIaeBalanceReportDto dto) {

        LambdaQueryWrapper<PmsIaeBalanceConfigDto> pmsIaeBalanceConfigDtoLambdaQueryWrapper = Wrappers
                .lambdaQuery(PmsIaeBalanceConfigDto.class);
        pmsIaeBalanceConfigDtoLambdaQueryWrapper.eq(PmsIaeBalanceConfigDto::getActive, 1);
        List<PmsIaeBalanceConfigDto> iaeBalanceConfigDtoList = pmsIaeBalanceConfigReadMapper
                .selectList(pmsIaeBalanceConfigDtoLambdaQueryWrapper);

        // 导出信息配置
        Map<String, List<PmsIaeBalanceConfigDto>> classificationMap = iaeBalanceConfigDtoList.stream()
                .collect(Collectors.groupingBy(PmsIaeBalanceConfigDto::getClassification)); // 按分类分组

        // 填充数据
        List<PmsIaeBalanceReportDto> excelData = generateData(data);
        try (Workbook workbook = new HSSFWorkbook()) {
            // 每个sheet的数据size,每页40条数据（动态调整）
            Integer sheetDataSize = dto.getPageSize();

            // 按incomeCostType分组
            Map<Integer, List<ExcelGroupEnum>> typeGroups = Arrays.stream(ExcelGroupEnum.values())
                    .collect(Collectors.groupingBy(ExcelGroupEnum::getIncomeCostType));

            Counter counter = new Counter();
            // 遍历每个incomeCostType类型
            for (Map.Entry<Integer, List<ExcelGroupEnum>> entry : typeGroups.entrySet()) {
                int incomeCostType = entry.getKey();
                List<ExcelGroupEnum> groupEnums = entry.getValue();

                // 获取当前类型的所有表头配置
                List<HeaderConfig> headers = getHeaderConfigsByType(groupEnums, classificationMap, counter);
                if (headers.isEmpty() || headers.size() < 2) {
                    continue;
                }

                // 计算需要的sheet数量
                int sheetCount = (int) Math.ceil((double) excelData.size() / sheetDataSize);

                for (int sheetIndex = 0; sheetIndex < sheetCount; sheetIndex++) {
                    // 根据incomeCostType获取sheet名称前缀
                    String prefix = getSheetPrefix(incomeCostType);
                    String sheetName = prefix + (sheetIndex + 1);
                    Sheet sheet = workbook.createSheet(sheetName);

                    int titleColumnSize = headers.stream()
                            .mapToInt(header -> header.subHeaders.size())
                            .sum();

                    // 创建标题行
                    String statMonth = dto.getStatMonth();
                    String year = statMonth.substring(0, 4);
                    String month = statMonth.substring(5);
                    String title = year + "年" + month + "月各科室" + prefix + "情况表";
                    createTitle(workbook, sheet, title, titleColumnSize - 1);

                    // 创建表头
                    createHeaders(workbook, sheet, headers);

                    // 计算当前sheet应该显示的数据范围
                    int startIndex = sheetIndex * sheetDataSize;
                    int endIndex = Math.min(startIndex + sheetDataSize, excelData.size());

                    // 合并两组数据并截取当前sheet需要的部分
                    List<PmsIaeBalanceReportDto> currentSheetData = excelData.subList(startIndex, endIndex);

                    // 填充数据
                    fillData(workbook, sheet, currentSheetData, headers);
                }
            }

            // 生成文件名
            String fileName = "科室收支报表_" + System.currentTimeMillis() + ".xls";
            File tempFile = new File(fileName);

            // 保存到临时文件
            try (FileOutputStream fos = new FileOutputStream(tempFile)) {
                workbook.write(fos);
            }

            // 上传到OSS并返回URL
            FileInputStream fileInputStream = new FileInputStream(tempFile);
            OSSUtil.uploadFile(OSSConst.BUCKET_PMS, "temp/" + fileName, fileInputStream);

            // 关闭文件流
            fileInputStream.close();
            // 删除临时文件
            if (!tempFile.delete()) {
                log.warn("临时文件删除失败: " + tempFile.getAbsolutePath());
            }

            return "temp/" + fileName;

        } catch (IOException e) {
            log.error("收支结余报表生成失败", e);
            throw new AppException("收支结余报表生成失败");
        }
    }

    /**
     * 封装excel数据
     */
    private List<PmsIaeBalanceReportDto> generateData(Map<String, Map<String, Object>> data) {
        ArrayList<PmsIaeBalanceReportDto> resultList = new ArrayList<>();
        Class<PmsIaeBalanceReportDto> pmsIaeBalanceReportDtoClass = PmsIaeBalanceReportDto.class;
        Field[] fields = pmsIaeBalanceReportDtoClass.getDeclaredFields();
        Map<String, String> fieldAnnotationMap = new HashMap<>();
        for (Field field : fields) {
            ItemCode annotation = field.getAnnotation(ItemCode.class);
            if (annotation != null) {
                fieldAnnotationMap.put(annotation.value(), field.getName());
            }
        }
        for (Map.Entry<String, Map<String, Object>> entry : data.entrySet()) {
            String statMonth = entry.getValue().get("collectionDate").toString();
            String deptCode = entry.getValue().get("deptCode").toString();
            String deptName = entry.getValue().get("deptName").toString();
            PmsIaeBalanceReportDto dto = new PmsIaeBalanceReportDto();
            dto.setStatMonth(statMonth);
            dto.setDeptCode(deptCode);
            dto.setDeptName(deptName);
            for (Map.Entry<String, Object> temp : entry.getValue().entrySet()) {
                String key = temp.getKey(); // MEDICAL_SERVICE_BED
                Object value = temp.getValue();
                try {
                    String fieldName = fieldAnnotationMap.get(key);
                    if (fieldName != null) {
                        Field field = pmsIaeBalanceReportDtoClass.getDeclaredField(fieldName);
                        field.setAccessible(true);
                        if (field.getType() == BigDecimal.class) {
                            field.set(dto, new BigDecimal(value.toString()));
                        } else if (field.getType() == Integer.class) {
                            field.set(dto, Integer.parseInt(value.toString()));
                        } else {
                            field.set(dto, value);
                        }
                    }
                } catch (Exception e) {
                    log.error("设置字段值失败: " + key, e);
                }
            }
            resultList.add(dto);
        }
        return resultList;
    }

    /**
     * 创建标题
     *
     * @param workbook
     * @param sheet
     * @param titleColumnSize 标题合并列数
     */
    private void createTitle(Workbook workbook, Sheet sheet, String sheetName, int titleColumnSize) {
        Row titleRow = sheet.createRow(0);
        // 设置行高为34
        titleRow.setHeight((short) (34 * 20));

        // 创建标题样式
        CellStyle titleStyle = workbook.createCellStyle();
        // 创建标题字体
        Font titleFont = workbook.createFont();
        titleFont.setBold(true);
        titleFont.setFontHeightInPoints((short) 28); // 设置字体大小为28
        titleFont.setFontName("华文新魏"); // 设置字体为华文新魏
        titleStyle.setFont(titleFont);
        // 设置水平居中
        titleStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        titleStyle.setVerticalAlignment(VerticalAlignment.CENTER);

        // 设置边框样式
        titleStyle.setBorderTop(BorderStyle.THIN);
        titleStyle.setBorderBottom(BorderStyle.THIN);
        titleStyle.setBorderLeft(BorderStyle.THIN);
        titleStyle.setBorderRight(BorderStyle.THIN);

        // 创建标题单元格
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue(sheetName);
        titleCell.setCellStyle(titleStyle);

        // 合并单元格
        sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, titleColumnSize + 2));
    }

    /**
     * 创建表头
     */
    private void createHeaders(Workbook workbook, Sheet sheet, List<HeaderConfig> headers) {
        // 创建分组行
        Row groupRow = sheet.createRow(1);
        groupRow.setHeight((short) (30 * 20));

        // 创建分组样式
        CellStyle groupStyle = workbook.createCellStyle();
        groupStyle.setAlignment(HorizontalAlignment.CENTER); // 水平居中
        groupStyle.setVerticalAlignment(VerticalAlignment.CENTER); // 垂直居中
        // 设置四边框
        groupStyle.setBorderTop(BorderStyle.THIN);
        groupStyle.setBorderBottom(BorderStyle.THIN);
        groupStyle.setBorderLeft(BorderStyle.THIN);
        groupStyle.setBorderRight(BorderStyle.THIN);

        // 设置字体为仿宋_GB2312，12号，加粗
        Font groupFont = workbook.createFont();
        groupFont.setFontName("仿宋_GB2312");
        groupFont.setFontHeightInPoints((short) 12);
        groupFont.setBold(true);
        groupStyle.setFont(groupFont);

        // 创建子表头行
        Row subHeaderRow = sheet.createRow(2);
        subHeaderRow.setHeight((short) (25 * 20));

        // 创建子表头样式
        CellStyle subHeaderStyle = workbook.createCellStyle();
        subHeaderStyle.setAlignment(HorizontalAlignment.CENTER);
        subHeaderStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        subHeaderStyle.setWrapText(true);
        // 设置四边框
        subHeaderStyle.setBorderTop(BorderStyle.THIN);
        subHeaderStyle.setBorderBottom(BorderStyle.THIN);
        subHeaderStyle.setBorderLeft(BorderStyle.THIN);
        subHeaderStyle.setBorderRight(BorderStyle.THIN);

        // 设置子表头字体
        Font subHeaderFont = workbook.createFont();
        subHeaderFont.setFontName("宋体");
        subHeaderFont.setFontHeightInPoints((short) 10);
        subHeaderStyle.setFont(subHeaderFont);

        for (HeaderConfig header : headers) {
            // 创建分组单元格
            Cell groupCell = groupRow.createCell(header.startCol);

            // 检查是否包含反斜杠,需要特殊处理
            if (header.group.contains("\\")) {
                // 分割字符串,将文本分为上下两部分
                String[] parts = header.group.split("\\\\");

                // 获取绘图对象,用于绘制对角线
                HSSFPatriarch patriarch = ((HSSFSheet) sheet).createDrawingPatriarch();
                HSSFClientAnchor anchor = new HSSFClientAnchor();

                // 设置对角线的起始和结束坐标
                // 从左上角(1行,startCol列)到右下角(3行,startCol+2列)
                anchor.setCol1(header.startCol); // 起始列
                anchor.setRow1(1); // 起始行(第2行)
                anchor.setCol2(header.startCol + 2); // 结束列(+2表示跨2列)
                anchor.setRow2(3); // 结束行(第3行)

                // 创建并设置对角线
                HSSFSimpleShape line = patriarch.createSimpleShape(anchor);
                line.setShapeType(HSSFSimpleShape.OBJECT_TYPE_LINE); // 设置为直线
                line.setLineStyle(HSSFSimpleShape.LINESTYLE_SOLID); // 实线样式
                line.setLineWidth(HSSFSimpleShape.LINEWIDTH_DEFAULT);

                // 创建第一行单元格样式(右对齐)
                CellStyle topStyle = workbook.createCellStyle();
                topStyle.cloneStyleFrom(groupStyle);
                topStyle.setAlignment(HorizontalAlignment.RIGHT); // 右对齐
                topStyle.setBorderBottom(BorderStyle.NONE); // 去除下边框

                // 创建第二行单元格样式(左对齐)
                CellStyle bottomStyle = workbook.createCellStyle();
                bottomStyle.cloneStyleFrom(groupStyle);
                bottomStyle.setAlignment(HorizontalAlignment.LEFT); // 左对齐
                bottomStyle.setBorderTop(BorderStyle.NONE); // 去除上边框

                // 创建并合并第一行单元格(类别)
                Cell topCell = groupRow.createCell(header.startCol);
                topCell.setCellValue(parts[0]); // 设置为"类别"
                topCell.setCellStyle(topStyle);
                sheet.addMergedRegion(new CellRangeAddress(
                        1, 1, header.startCol, header.startCol + 1));

                // 创建并合并第二行单元格(科室信息)
                Cell bottomCell = subHeaderRow.createCell(header.startCol);
                bottomCell.setCellValue(parts[1]); // 设置为"科室信息"
                bottomCell.setCellStyle(bottomStyle);
                sheet.addMergedRegion(new CellRangeAddress(
                        2, 2, header.startCol, header.startCol + 1));

                // 为合并区域内的其他单元格设置边框样式
                for (int j = header.startCol + 1; j <= header.startCol + 1; j++) {
                    Cell topBorderCell = groupRow.createCell(j);
                    Cell bottomBorderCell = subHeaderRow.createCell(j);
                    topBorderCell.setCellStyle(topStyle);
                    bottomBorderCell.setCellStyle(bottomStyle);
                }
            } else {
                // 普通表头处理
                groupCell.setCellValue(header.group);
                groupCell.setCellStyle(groupStyle);

                // 合并分组单元格
                sheet.addMergedRegion(new CellRangeAddress(
                        1, 1, header.startCol, header.endCol));
            }

            // 为合并区域内的所有单元格设置样式和边框
            for (int j = header.startCol; j <= header.endCol; j++) {
                Cell cell = groupRow.getCell(j);
                if (cell == null) {
                    cell = groupRow.createCell(j);
                }
                // 复制groupStyle的所有属性
                CellStyle cellStyle = workbook.createCellStyle();
                cellStyle.cloneStyleFrom(groupStyle);
                // 确保边框样式被正确复制
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                cell.setCellStyle(cellStyle);
            }

            // 创建子表头并设置边框
            int col = header.startCol;
            for (String subHeader : header.subHeaders) {
                Cell subHeaderCell = subHeaderRow.createCell(col);
                subHeaderCell.setCellValue(subHeader);
                // 为每个子表头单元格创建独立的样式
                CellStyle cellStyle = workbook.createCellStyle();
                cellStyle.cloneStyleFrom(subHeaderStyle);
                // 确保边框样式被正确复制
                cellStyle.setBorderTop(BorderStyle.THIN);
                cellStyle.setBorderBottom(BorderStyle.THIN);
                cellStyle.setBorderLeft(BorderStyle.THIN);
                cellStyle.setBorderRight(BorderStyle.THIN);
                subHeaderCell.setCellStyle(cellStyle);
                sheet.setColumnWidth(col, 12 * 256);
                col++;
            }
        }
    }

    /**
     * 填充数据
     */
    private void fillData(Workbook workbook, Sheet sheet,
            List<PmsIaeBalanceReportDto> data, List<HeaderConfig> headers) {
        // 创建数据单元格样式
        CellStyle dataStyle = workbook.createCellStyle();
        // 设置水平居中
        dataStyle.setAlignment(HorizontalAlignment.CENTER);
        // 设置垂直居中
        dataStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        // 设置字体
        Font dataFont = workbook.createFont();
        dataFont.setFontHeightInPoints((short) 10);
        dataFont.setFontName("宋体");
        dataStyle.setFont(dataFont);

        // 设置四边框
        dataStyle.setBorderTop(BorderStyle.THIN);
        dataStyle.setBorderBottom(BorderStyle.THIN);
        dataStyle.setBorderLeft(BorderStyle.THIN);
        dataStyle.setBorderRight(BorderStyle.THIN);

        sheet.setColumnWidth(0, 600);

        // 创建科室特殊样式
        CellStyle deptSpecialStyle = workbook.createCellStyle();
        Font deptSpecialFont = workbook.createFont();
        deptSpecialFont.setFontName("仿宋_GB2312");
        deptSpecialFont.setFontHeightInPoints((short) 12);
        deptSpecialFont.setBold(true);
        deptSpecialStyle.setFont(deptSpecialFont);
        // 设置四边框
        deptSpecialStyle.setBorderTop(BorderStyle.THIN);
        deptSpecialStyle.setBorderBottom(BorderStyle.THIN);
        deptSpecialStyle.setBorderLeft(BorderStyle.THIN);
        deptSpecialStyle.setBorderRight(BorderStyle.THIN);

        // 设置数据单元格左对齐
        dataStyle.setAlignment(HorizontalAlignment.LEFT);

        int rowNum = 3;

        // 创建序号行
        Row headerRow = sheet.createRow(rowNum++);
        headerRow.setHeight((short) (25 * 20)); // 设置行高
        int col = 0;

        // 添加"科室"和"科室代码"列
        Cell deptCell = headerRow.createCell(col++);
        deptCell.setCellValue("科室名称");
        deptCell.setCellStyle(dataStyle);

        sheet.setColumnWidth(col - 1, 30 * 256);

        Cell deptCodeCell = headerRow.createCell(col++);
        deptCodeCell.setCellValue("科室代码");
        deptCodeCell.setCellStyle(dataStyle);

        // 计算并添加序号列[1]-[n]
        int totalColumns = totalColumns = headers.stream()
                .mapToInt(h -> h.subHeaders.size())
                .sum();

        // 创建序号列样式
        CellStyle indexStyle = workbook.createCellStyle();
        indexStyle.cloneStyleFrom(dataStyle);
        indexStyle.setAlignment(HorizontalAlignment.CENTER);

        for (int i = 1; i <= totalColumns; i++) {
            Cell cell = headerRow.createCell(col++);
            cell.setCellValue("[" + i + "]");
            cell.setCellStyle(indexStyle);
            // 设置序号列宽度
            sheet.setColumnWidth(col - 1, 10 * 256);
        }
        // 获取PmsIaeBalanceReportDto的所有字段
        Field[] fields = PmsIaeBalanceReportDto.class.getDeclaredFields();

        // 填充数据行
        for (PmsIaeBalanceReportDto dto : data) {
            Row row = sheet.createRow(rowNum++);
            row.setHeight((short) (25 * 20)); // 设置数据行高
            col = 0;

            // 科室信息
            row.createCell(col++).setCellValue(dto.getDeptName());
            row.createCell(col++).setCellValue(dto.getDeptCode());

            // 分组数据
            for (HeaderConfig header : headers) {
                for (String subHeader : header.subHeaders) {
                    Cell cell = row.createCell(col++);
                    Field field = Arrays.stream(fields)
                            .filter(f -> {
                                Excel annotation = f.getAnnotation(Excel.class);
                                return annotation != null && annotation.name().equals(subHeader);
                            })
                            .findFirst()
                            .orElse(null);
                    if (field != null) {
                        field.setAccessible(true);
                        try {
                            Object value = field.get(dto);
                            if (value instanceof BigDecimal) {
                                cell.setCellValue(((BigDecimal) value).doubleValue());
                            } else if (value instanceof Double) {
                                cell.setCellValue((Double) value);
                            } else if (value instanceof Integer) {
                                cell.setCellValue((Integer) value);
                            } else if (value instanceof String) {
                                cell.setCellValue((String) value);
                            } else if (value instanceof Date) {
                                cell.setCellValue((Date) value);
                            } else if (value == null) {
                                cell.setCellValue("");
                            } else {
                                cell.setCellValue(value.toString());
                            }
                        } catch (IllegalAccessException e) {
                            e.printStackTrace();
                            cell.setCellValue("");
                        }
                    }
                }
            }

            // 设置样式
            for (int i = 0; i < col; i++) {
                Cell cell = row.getCell(i);
                if (i == 0 || i == 1) {
                    cell.setCellStyle(deptSpecialStyle);
                } else {
                    cell.setCellStyle(dataStyle);
                }
            }
        }
    }

    // 新增方法：根据类型获取对应的组配置
    private List<HeaderConfig> getHeaderConfigsByType(List<ExcelGroupEnum> groupEnums,
            Map<String, List<PmsIaeBalanceConfigDto>> classificationMap,
            Counter counter) {
        List<HeaderConfig> configs = new ArrayList<>();
        int col = 0;

        // 基本信息
        configs.add(new HeaderConfig("类别\\科室信息", Arrays.asList(), col));
        col += 2;

        for (ExcelGroupEnum groupEnum : groupEnums) {
            List<String> headers = classificationMap
                    .getOrDefault(groupEnum.getGroupName(), new ArrayList<>())
                    .stream()
                    .map(PmsIaeBalanceConfigDto::getItemName)
                    .collect(Collectors.toList());

            if (headers.isEmpty()) {
                continue;
            }
            String groupName = getGroupNameWithPrefix(groupEnum, counter);

            configs.add(new HeaderConfig(groupName, headers, col));
            col += headers.size();
        }

        return configs;
    }

    // 新增方法：获取带序号前缀的分组名称
    private String getGroupNameWithPrefix(ExcelGroupEnum groupEnum, Counter counter) {
        // 如果不需要添加序号前缀，直接返回分组名称
        if (!groupEnum.isHasOrderPrefix()) {
            return groupEnum.getGroupName();
        }

        // 定义中文数字数组
        String[] chineseNumbers = { "一", "二", "三", "四", "五", "六", "七", "八", "九", "十" };

        // 使用计数器获取序号并递增
        int currentOrder = counter.getAndIncrement();

        // 如果序号超出数组范围，返回原始名称
        if (currentOrder >= chineseNumbers.length) {
            return groupEnum.getGroupName();
        }

        // 返回带序号的分组名称
        return chineseNumbers[currentOrder] + "、" + groupEnum.getGroupName();
    }

    // 新增方法：获取sheet名称前缀
    private String getSheetPrefix(int incomeCostType) {
        switch (incomeCostType) {
            case 1:
                return "收入";
            case 2:
                return "成本";
            case 3:
                return "费用";
            default:
                return "其他";
        }
    }

    // 计数器类
    private static class Counter {
        private int value;

        public int getAndIncrement() {
            return value++;
        }
    }

    /**
     * 表头配置类
     */
    private static class HeaderConfig {
        String group;
        List<String> subHeaders;
        int startCol;
        int endCol;

        HeaderConfig(String group, List<String> subHeaders, int startCol) {
            this.group = group;
            this.subHeaders = subHeaders;
            this.startCol = startCol;
            this.endCol = startCol + subHeaders.size() - 1;
        }
    }

    @Getter
    @AllArgsConstructor
    private enum ExcelGroupEnum {
        // 医疗服务收入分组
        MEDICAL_SERVICE(true, "医疗服务收入", 1),
        // 药品收入分组
        DRUG_INCOME(true, "药品收入", 1),
        // 收入总计
        INCOME_TOTAL(false, "总计", 1),
        // 科室费用分组
        DEPT_VARIABLE_COST(true, "变动成本/科室费用", 2),
        // 总务材料分组
        GENERAL_MATERIAL_COST(true, "变动成本/总务材料", 2),
        // 医用材料费分组
        MEDICAL_MATERIAL_COST(true, "变动成本/医用材料费", 3),
        // 固定资产折旧分组
        FIXED_ASSETS_COST(true, "固定资产折旧/设备维修费", 3),
        // 支出总计
        EXPENSE_TOTAL(false, "业务支出总计", 3),
        // 基本数字分析分组
        BASIC_NUMERICAL_ANALYSIS(true, "基本数字分析", 3),
        // 其他分组
        OTHER(true, "其他", 5);

        // 是否添加排序前缀
        private final boolean hasOrderPrefix;
        // 分组名称
        private final String groupName;
        // 收入成本类型
        private final int incomeCostType;

        /**
         * 根据分类获取分组枚举
         *
         * @param classification 分类名称
         * @return 对应的分组枚举
         */
        public static ExcelGroupEnum getByClassification(String classification) {
            switch (classification) {
                case "医疗服务收入":
                    return MEDICAL_SERVICE;
                case "药品收入":
                    return DRUG_INCOME;
                case "总计":
                case "变动成本/科室费用":
                    return DEPT_VARIABLE_COST;
                case "变动成本/总务材料":
                    return GENERAL_MATERIAL_COST;
                case "变动成本/医用材料费":
                    return MEDICAL_MATERIAL_COST;
                case "固定资产折旧/设备维修费":
                    return FIXED_ASSETS_COST;
                case "业务支出总计":
                    return EXPENSE_TOTAL;
                case "基本数字分析":
                    return BASIC_NUMERICAL_ANALYSIS;
                default:
                    return OTHER;
            }
        }
    }

}

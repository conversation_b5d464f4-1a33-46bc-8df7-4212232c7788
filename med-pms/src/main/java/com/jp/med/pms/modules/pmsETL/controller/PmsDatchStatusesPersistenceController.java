package com.jp.med.pms.modules.pmsETL.controller;

import java.util.List;

import javax.annotation.Resource;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.github.pagehelper.PageHelper;
import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.pms.modules.pmsCalc.dto.PmsDatchStatusesPersistence;
import com.jp.med.pms.modules.pmsCalc.mapper.PmsDatchStatusesPersistenceMapper;
import com.jp.med.pms.modules.pmsCalc.service.calcEngine.PmsDatchStatusesPersistenceService;

import lombok.extern.slf4j.Slf4j;

@RestController
@RequestMapping("batchStatuses")
@Slf4j
public class PmsDatchStatusesPersistenceController {

    @Autowired
    private PmsDatchStatusesPersistenceMapper pmsDatchStatusesPersistenceMapper;

    @Resource
    private PmsDatchStatusesPersistenceService pmsDatchStatusesPersistenceService;

    @PostMapping
    public CommonResult<?> create(@RequestBody PmsDatchStatusesPersistence entity) {
        pmsDatchStatusesPersistenceMapper.insert(entity);
        return CommonResult.success();
    }

    @PutMapping
    public CommonResult<?> update(@RequestBody PmsDatchStatusesPersistence entity) {
        pmsDatchStatusesPersistenceMapper.updateById(entity);
        return CommonResult.success();
    }

    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody PmsDatchStatusesPersistence entity) {
        return CommonResult.success(pmsDatchStatusesPersistenceMapper.selectList(new QueryWrapper<>()));
    }

    @PostMapping("/pageList")
    public CommonResult<?> pageList(@RequestBody PmsDatchStatusesPersistence entity) {
        PageHelper.startPage(entity.getPage(), entity.getPageSize());
        QueryWrapper<PmsDatchStatusesPersistence> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(entity.getBatchCode() != null, PmsDatchStatusesPersistence::getBatchCode, entity.getBatchCode())
                .eq(entity.getBatchStatus() != null, PmsDatchStatusesPersistence::getBatchStatus,
                        entity.getBatchStatus())
                .eq(entity.getStatisticsFrequency() != null, PmsDatchStatusesPersistence::getStatisticsFrequency,
                        entity.getStatisticsFrequency())
                .eq(entity.getDataStartTime() != null, PmsDatchStatusesPersistence::getDataStartTime,
                        entity.getDataStartTime())
                .eq(entity.getDataEndTime() != null, PmsDatchStatusesPersistence::getDataEndTime,
                        entity.getDataEndTime())
                .eq(entity.getStatus() != null, PmsDatchStatusesPersistence::getStatus, entity.getStatus())
                .orderByDesc(PmsDatchStatusesPersistence::getId);
        List<PmsDatchStatusesPersistence> list = pmsDatchStatusesPersistenceService.list(queryWrapper);

        return CommonResult.paging(list);
    }

    @DeleteMapping("/{id}")
    public CommonResult<?> delete(@PathVariable String id) {
        pmsDatchStatusesPersistenceMapper.deleteById(id);
        return CommonResult.success();
    }

    @GetMapping("/{id}")
    public CommonResult<?> getById(@PathVariable String id) {
        return CommonResult.success(pmsDatchStatusesPersistenceMapper.selectById(id));
    }

    @GetMapping("/{batchCode}")
    public CommonResult<?> getByBatchCode(@PathVariable String batchCode) {
        QueryWrapper<PmsDatchStatusesPersistence> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("batch_code", batchCode);
        return CommonResult.success(pmsDatchStatusesPersistenceMapper.selectList(queryWrapper));
    }
}

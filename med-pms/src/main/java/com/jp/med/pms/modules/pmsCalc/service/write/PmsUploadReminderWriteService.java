package com.jp.med.pms.modules.pmsCalc.service.write;

import com.jp.med.pms.modules.pmsCalc.dto.PmsUploadReminder;
import com.jp.med.pms.modules.pmsCalc.requestBody.PmsUploadReminderRB;

import java.util.List;

/**
 * 绩效指标上报催促记录写入服务接口
 */
public interface PmsUploadReminderWriteService {

    /**
     * 新增
     *
     * @param entity 实体
     * @return 是否成功
     */
    boolean save(PmsUploadReminder entity);


    /**
     * 更新
     *
     * @param entity 实体
     * @return 是否成功
     */
    boolean update(PmsUploadReminder entity);

    /**
     * 删除
     *
     * @param id ID
     * @return 是否成功
     */
    boolean removeById(Long id);

    /**
     * 批量删除
     *
     * @param ids ID列表
     * @return 是否成功
     */
    boolean removeByIds(List<Long> ids);

    /**
     * 处理催促上报请求
     *
     * @param rb 请求体
     * @return 处理结果
     */
    boolean handleRemindRequest(PmsUploadReminderRB rb);

    /**
     * 批量更新催促消息状态
     *
     * @param ids    ID列表
     * @param status 状态值，例如: READ, COMPLETED
     * @return 是否成功
     */
    boolean batchUpdateStatus(List<Long> ids, String status);
}
package com.jp.med.pms.modules.pmsCalc.controller;

import com.jp.med.common.entity.common.CommonResult;
import com.jp.med.pms.modules.pmsCalc.dto.PmsAwardCalcResultDto;
import com.jp.med.pms.modules.pmsCalc.dto.PmsCalcContextDto;
import com.jp.med.pms.modules.pmsCalc.service.read.PmsCalcContextReadService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 绩效工作台
 * @Description
 * <AUTHOR>
 * @Date 2024/11/27 17:14
 */
@Api(value = "绩效计算上下文", tags = "绩效计算上下文")
@RestController
@RequestMapping("pmsHome")
public class PmsHomeController {

    @Autowired
    private PmsCalcContextReadService pmsCalcContextReadService;


    @ApiOperation("获取全院绩效总额（医生、护士、医技、行政后勤）")
    @PostMapping("/getTotalAward")
    public CommonResult<?> getTotalAward(@RequestBody PmsCalcContextDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(pmsCalcContextReadService.getTotalAward(dto));
    }

    @ApiOperation("获取全院绩效总额（医生、护士、医技、行政后勤）")
    @PostMapping("/getTotalAward2")
    public CommonResult<?> getTotalAward(@RequestBody PmsAwardCalcResultDto dto) {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(pmsCalcContextReadService.getTotalAward2(dto));
    }

    @ApiOperation("统计全院人力支出成本（人力实发工资or应发工资）占全院总收入比例，区分单月和累计")
    @PostMapping("/getHumanCostAnalysis")
    public CommonResult<?> getHumanCostAnalysis(@RequestBody PmsCalcContextDto dto) throws Exception {
        dto.setSqlAutowiredHospitalCondition(true);
        return CommonResult.success(pmsCalcContextReadService.getHumanCostAnalysis(dto));
    }

}

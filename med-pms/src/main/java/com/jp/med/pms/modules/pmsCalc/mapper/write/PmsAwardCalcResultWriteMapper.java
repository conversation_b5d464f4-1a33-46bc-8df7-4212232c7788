package com.jp.med.pms.modules.pmsCalc.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.pms.modules.pmsCalc.dto.PmsAwardCalcResultDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 绩效计算结果
 *
 * <AUTHOR>
 * @date 2024-07-26
 */
@Mapper
public interface PmsAwardCalcResultWriteMapper extends BaseMapper<PmsAwardCalcResultDto> {

    /**
     * 插入管理员绩效分配
     *
     * @param dto 管理员绩效分配DTO
     */
    void insertMannagerAwardApportion(PmsAwardCalcResultDto dto);

    /**
     * 删除管理员绩效分配
     *
     * @param dto 管理员绩效分配DTO
     */
    void deleteMannagerAwardApportion(PmsAwardCalcResultDto dto);
}

package com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.service;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.dto.PmsStaffTypeConfigDto;
import com.jp.med.pms.modules.pmsMonthlyStaffNumberReport.mapper.PmsStaffTypeConfigMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 人员类型配置读取Service实现
 *
 * <AUTHOR>

 */
@Service
public class PmsStaffTypeConfigReadServiceImpl implements PmsStaffTypeConfigReadService {

    @Autowired
    private PmsStaffTypeConfigMapper staffTypeConfigMapper;

    @Override
    public IPage<PmsStaffTypeConfigDto> queryPageList(PmsStaffTypeConfigDto dto) {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = buildQueryWrapper(dto);

        Page<PmsStaffTypeConfigDto> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        return staffTypeConfigMapper.selectPage(page, wrapper);
    }

    @Override
    public List<PmsStaffTypeConfigDto> queryList(PmsStaffTypeConfigDto dto) {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = buildQueryWrapper(dto);
        return staffTypeConfigMapper.selectList(wrapper);
    }

    @Override
    public PmsStaffTypeConfigDto queryById(Integer id) {
        return staffTypeConfigMapper.selectById(id);
    }

    @Override
    public PmsStaffTypeConfigDto queryByTypeCode(String typeCode) {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmsStaffTypeConfigDto::getTypeCode, typeCode);
        return staffTypeConfigMapper.selectOne(wrapper);
    }

    @Override
    public List<PmsStaffTypeConfigDto> queryActiveTypes() {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmsStaffTypeConfigDto::getIsActive, true)
                .orderByAsc(PmsStaffTypeConfigDto::getSortOrder);
        return staffTypeConfigMapper.selectList(wrapper);
    }

    @Override
    public boolean checkTypeCodeExists(String typeCode, Integer excludeId) {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmsStaffTypeConfigDto::getTypeCode, typeCode);

        if (excludeId != null) {
            wrapper.ne(PmsStaffTypeConfigDto::getId, excludeId);
        }

        return staffTypeConfigMapper.selectCount(wrapper) > 0;
    }

    @Override
    public List<PmsStaffTypeConfigDto> queryByCategory(String category) {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmsStaffTypeConfigDto::getCategory, category)
                .eq(PmsStaffTypeConfigDto::getIsActive, true)
                .orderByAsc(PmsStaffTypeConfigDto::getSortOrder);
        return staffTypeConfigMapper.selectList(wrapper);
    }

    @Override
    public List<PmsStaffTypeConfigDto> queryByStaffGroup(String staffGroup) {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(PmsStaffTypeConfigDto::getStaffGroup, staffGroup)
                .eq(PmsStaffTypeConfigDto::getIsActive, true)
                .orderByAsc(PmsStaffTypeConfigDto::getSortOrder);
        return staffTypeConfigMapper.selectList(wrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<PmsStaffTypeConfigDto> buildQueryWrapper(PmsStaffTypeConfigDto dto) {
        LambdaQueryWrapper<PmsStaffTypeConfigDto> wrapper = new LambdaQueryWrapper<>();

        if (StringUtils.hasText(dto.getTypeCode())) {
            wrapper.like(PmsStaffTypeConfigDto::getTypeCode, dto.getTypeCode());
        }

        if (StringUtils.hasText(dto.getTypeName())) {
            wrapper.like(PmsStaffTypeConfigDto::getTypeName, dto.getTypeName());
        }

        if (StringUtils.hasText(dto.getStaffGroup())) {
            wrapper.eq(PmsStaffTypeConfigDto::getStaffGroup, dto.getStaffGroup());
        }

        if (StringUtils.hasText(dto.getCategory())) {
            wrapper.eq(PmsStaffTypeConfigDto::getCategory, dto.getCategory());
        }

        if (StringUtils.hasText(dto.getCalculationMethod())) {
            wrapper.eq(PmsStaffTypeConfigDto::getCalculationMethod, dto.getCalculationMethod());
        }

        if (dto.getIsActive() != null) {
            wrapper.eq(PmsStaffTypeConfigDto::getIsActive, dto.getIsActive());
        }

        wrapper.orderByAsc(PmsStaffTypeConfigDto::getSortOrder);

        return wrapper;
    }
} 
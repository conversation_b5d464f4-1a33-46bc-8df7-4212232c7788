package com.jp.med.pms.modules.pmsCalcTemplate.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.jp.med.common.dto.common.CommonQueryDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 绩效计算科室模版
 *
 * <AUTHOR>
 * @email -
 * @date 2024-07-22 05:05:40
 */
@Data
@EqualsAndHashCode(callSuper = true)

@TableName("pms_award_calc_dept_template")
public class PmsAwardCalcDeptTemplateDto extends CommonQueryDto {

    /**
     * id
     */
    @TableId("id")
    private Long id;

    /**
     * 模版名称
     */
    @TableField("template_name")
    private String templateName;

    /**
     * 绩效科室名称
     */
    @TableField("pms_dept_name")
    private String pmsDeptName;

    /**
     * 模版科室 HRP 科室 id
     */
    @TableField("template_dept")
    private String templateDept;

    /**
     * 模版科室名称 HRP 科室名称
     */
    @TableField(exist = false)
    private String templateDeptName;

    /**
     * 模板编码，用于全局缓存唯一标识
     */
    @TableField(exist = false)
    private String templateCode;
    /**
     * 已采集的hrp代码
     */
    @TableField(exist = false)
    private String hrpDeptCode;

    /**
     * 模版类型 0 护 1 医
     */
    @TableField("template_type")
    private String templateType;

    /**
     * 绩效领用人工号
     */
    @TableField("award_apportion_emp_code")
    private String awardApportionEmpCode;

    /**
     * 创建人emp_code
     */
    @TableField("create_user")
    private String createUser;

    /**
     * 创建时间
     */
    @TableField("create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @TableField("update_time")
    private Date updateTime;

    /**
     * 是否启用 1启用0关闭
     */
    @TableField("active")
    private String active;

    /**
     * 版本号
     */
    @TableField("version")
    private Integer version;

    /*
     * 排序
     */
    private Integer orderInteger;
}

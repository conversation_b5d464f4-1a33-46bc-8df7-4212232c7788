<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.supplier.mapper.read.PurmsUserInfoReadMapper">

	<!-- 可根据自己的需求，是否要使用 -->
    <resultMap type="com.jp.med.purms.modules.supplier.vo.PurmsUserInfoVo" id="userInfoMap">
        <result property="id" column="id"/>
        <result property="supplierId" column="supplier_id"/>
        <result property="userName" column="user_name"/>
        <result property="contactName" column="contact_name"/>
        <result property="phone" column="phone"/>
        <result property="email" column="email"/>
        <result property="sex" column="sex"/>
        <result property="contactAddr" column="contact_addr"/>
        <result property="countryCode" column="country_code"/>
        <result property="regDate" column="reg_date"/>
        <result property="updter" column="updter"/>
        <result property="updateTime" column="update_time"/>
        <result property="delter" column="delter"/>
        <result property="deleteTime" column="delete_time"/>
        <result property="isDeleted" column="is_deleted"/>
        <result property="hospitalId" column="hospital_id"/>
    </resultMap>
    <select id="queryList" resultType="com.jp.med.purms.modules.supplier.vo.PurmsUserInfoVo">
        select
            id as id,
            supplier_id as supplierId,
            user_name as userName,
            contact_name as contactName,
            phone as phone,
            email as email,
            sex as sex,
            contact_addr as contactAddr,
            country_code as countryCode,
            reg_date as regDate,
            updter as updter,
            update_time as updateTime,
            delter as delter,
            delete_time as deleteTime,
            is_deleted as isDeleted,
            hospital_id as hospitalId
        from purms_user_info
        <where>
            (is_deleted IS NULL OR is_deleted != 1)
            <if test="id != null or id != ''">
                and id = #{id,jdbcType=INTEGER}
            </if>
            <if test="supplierId != null or supplierId != ''">
                and supplier_id = #{supplierId,jdbcType=INTEGER}
            </if>
            <if test="userName != null or userName != ''">
                and user_name = #{userName,jdbcType=VARCHAR}
            </if>
            <if test="contactName != null or contactName != ''">
                and contact_name = #{contactName,jdbcType=VARCHAR}
            </if>
            <if test="phone != null or phone != ''">
                and phone = #{phone,jdbcType=VARCHAR}
            </if>
            <if test="sex != null or sex != ''">
                and sex = #{sex,jdbcType=VARCHAR}
            </if>
            <if test="contactAddr != null or contactAddr != ''">
                and contact_addr = #{contactAddr,jdbcType=VARCHAR}
            </if>
            <if test="countryCode != null or countryCode != ''">
                and country_code = #{countryCode,jdbcType=VARCHAR}
            </if>
            <if test="regDate != null or regDate != ''">
                and reg_date = #{regDate,jdbcType=VARCHAR}
            </if>
            <if test="updter != null or updter != ''">
                and updter = #{updter,jdbcType=VARCHAR}
            </if>
            <if test="updateTime != null or updateTime != ''">
                and update_time = #{updateTime,jdbcType=VARCHAR}
            </if>

            <if test="deleteTime != null or deleteTime != ''">
                and delete_time = #{deleteTime,jdbcType=VARCHAR}
            </if>

        </where>
    </select>

    <select id="queryListBySupplierId" resultType="com.jp.med.purms.modules.supplier.vo.PurmsUserInfoVo">
        select
            id as id,
            supplier_id as supplierId,
            user_name as userName,
            contact_name as contactName,
            phone as phone,
            email as email,
            sex as sex,
            contact_addr as contactAddr,
            country_code as countryCode,
            reg_date as regDate,
            updter as updter,
            update_time as updateTime,
            delter as delter,
            delete_time as deleteTime,
            is_deleted as isDeleted,
            hospital_id as hospitalId
        from purms_user_info
        <where>
            <if test="supplierId != null or supplierId != ''">
                supplier_id = #{supplierId,jdbcType=INTEGER}
            </if>
        </where>
    </select>




</mapper>

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.purms.modules.applyMgt.mapper.read.PurmsPurcExeReadMapper">

    <select id="queryList" resultType="com.jp.med.purms.modules.applyMgt.vo.PurmsPurcExeVo">
        SELECT A.id                                                   AS id,
               'exe' || A.id                                          AS key,
               A.purc_type                                            AS purcType,
               A.status                                               AS status,
               A.sign                                                 AS sign,
               A.att                                                  AS att,
               A.att_name                                             AS attName,
               A.exe_remark                                           AS exeRemark,
               A.item_name                                            AS itemName,
               A.executor                                             AS executor,
               emp1.emp_name                                          as executorName,
               A.sumamt                                               AS sumamt,
               A.really_sumamt                                        AS reallySumamt,
               A<PERSON>crter                                                AS crter,
               to_date(A.crte_time, 'yyyy-MM-dd HH24:mi:ss')::VARCHAR AS exeDate,
               A.active_flag                                          AS activeFlag,
               A.hospital_id                                          AS hospitalId
        FROM purms_purc_exe A
                 LEFT JOIN hrm_employee_info emp1 on A.executor = emp1.emp_code
        <where>
            <if test="type != null and type != ''">
                and A.purc_type = #{type}
            </if>
            <if test="crteTime != null and crteTime != ''">
                and A.crte_time::date &lt;= to_date(#{crteTime}, 'YYYY-MM-DD')
            </if>
            <if test="id != null">
                and A.id = #{id}
            </if>
            and A.active_flag = '1'
        </where>
        order by a.crte_time desc
    </select>

    <select id="selectTaskToReimApply" resultType="com.jp.med.common.dto.ecs.EcsReimPurcTask">
        select A.id            AS id,
               A.item_name     AS itemName,
               A.executor      as exeEmpCode,
               A.exe_org       as reimOrgCode,
               A.crte_time     as exeDate,
               A.exe_remark    as reimDesc,
               A.really_sumamt as sumamt,
               A.att,
               A.att_name      as attName
        from purms_purc_exe A
        where A.active_flag = '1'
          and id in
        <foreach collection="exeIds" open="(" separator="," close=")" item="exeId">
            #{exeId}
        </foreach>
    </select>

    <select id="selectByDetailIds" resultType="com.jp.med.purms.modules.applyMgt.vo.PurmsPurcExeVo">
        select *
        from purms_purc_exe
        where id in(
        select distinct exe_id
        from purms_purc_req_detail
        where id in
        <foreach collection="detailIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        )
    </select>

</mapper>

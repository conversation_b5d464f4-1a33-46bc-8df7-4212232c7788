package com.jp.med.purms.modules.integration.service.read;

import com.jp.med.purms.modules.integration.dto.PurchaseCategoryDTO;
import com.jp.med.purms.modules.integration.dto.PurchaseProjectDTO;
import com.jp.med.purms.modules.integration.vo.PurchaseExecutionStatusVO;
import com.jp.med.purms.modules.integration.vo.PurchaseRequestStatusVO;
import com.jp.med.purms.modules.integration.vo.IntegrationPurReqDetailVO;

import java.util.List;
import java.util.Map;

/**
 * 采购系统集成读服务
 */
public interface PurmsIntegrationReadService {

	/**
	 * 获取所有采购分类
	 *
	 * @param hospitalId 医院ID
	 * @return 采购分类列表
	 */
	List<PurchaseCategoryDTO> getAllPurchaseCategories(String hospitalId);

	/**
	 * 获取所有采购项目
	 *
	 * @param hospitalId 医院ID
	 * @return 采购项目列表
	 */
	List<PurchaseProjectDTO> getAllPurchaseProjects(String hospitalId);

	/**
	 * 获取采购申请状态
	 *
	 * @param requestId  申请ID
	 * @param hospitalId 医院ID
	 * @return 采购申请状态
	 */
	PurchaseRequestStatusVO getPurchaseRequestStatus(String requestId, String hospitalId);

	/**
	 * 获取采购执行状态
	 *
	 * @param requestId  申请ID
	 * @param hospitalId 医院ID
	 * @return 采购执行状态
	 */
	PurchaseExecutionStatusVO getPurchaseExecutionStatus(String requestId, String hospitalId);

	/**
	 * 获取二级库物资相关的采购单明细 (对外接口 PUR1001)
	 * 
	 * @param hospitalId 医院ID
	 * @param params     查询参数 (例如，可以包含日期范围, 采购单状态等)
	 * @return 二级库采购单明细列表
	 */
	List<IntegrationPurReqDetailVO> getSecondaryWarehousePurchaseDetails(String hospitalId, Map<String, Object> params);
}
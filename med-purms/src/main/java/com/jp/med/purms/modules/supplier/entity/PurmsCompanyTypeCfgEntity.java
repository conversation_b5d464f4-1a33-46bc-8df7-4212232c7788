package com.jp.med.purms.modules.supplier.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import lombok.Data;

/**
 * 企业性质配置
 * <AUTHOR>
 * @email -
 * @date 2024-01-15 17:08:48
 */
@Data
@TableName("purms_company_type_cfg")
public class PurmsCompanyTypeCfgEntity {

	/** id */
	@TableId("id")
	private Integer id;

	/** 企业性质名称 */
	@TableField("type_name")
	private String typeName;

	/** 企业类型编码 */
	@TableField("type_code")
	private String typeCode;

	/** 状态 */
	/** 状态(逻辑删除) */
	@TableField("is_deleted")
	private Integer isDeleted;
	/** 医院组织id */
	@TableField("hospital_id")
	private String hospitalId;

}

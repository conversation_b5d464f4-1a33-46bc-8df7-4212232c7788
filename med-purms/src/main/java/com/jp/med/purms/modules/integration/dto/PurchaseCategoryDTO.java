package com.jp.med.purms.modules.integration.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 采购分类DTO
 */
@Data
@ApiModel("采购分类DTO")
public class PurchaseCategoryDTO {

	@ApiModelProperty("分类ID")
	private String categoryId;

	@ApiModelProperty("分类编码")
	private String code;

	@ApiModelProperty("分类名称")
	private String name;

	@ApiModelProperty("父级ID")
	private String parentId;

	@ApiModelProperty("是否为叶子节点 (1: 是, 0: 否)")
	private Integer isLeaf;

	@ApiModelProperty("删除标志 (1: 已删除, 0: 未删除)")
	private Integer deleteFlag;
}
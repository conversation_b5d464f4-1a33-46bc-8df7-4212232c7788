package com.jp.med.purms.modules.supplier.service.read.impl;

import com.github.pagehelper.PageHelper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;


import com.jp.med.purms.modules.supplier.mapper.read.PurmsEmpnumCfgReadMapper;
import com.jp.med.purms.modules.supplier.dto.PurmsEmpnumCfgDto;
import com.jp.med.purms.modules.supplier.vo.PurmsEmpnumCfgVo;
import com.jp.med.purms.modules.supplier.service.read.PurmsEmpnumCfgReadService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

@Transactional(readOnly = true)
@Service
public class PurmsEmpnumCfgReadServiceImpl extends ServiceImpl<PurmsEmpnumCfgReadMapper, PurmsEmpnumCfgDto> implements PurmsEmpnumCfgReadService {

    @Autowired
    private PurmsEmpnumCfgReadMapper purmsEmpnumCfgReadMapper;

    @Override
    public List<PurmsEmpnumCfgVo> queryList(PurmsEmpnumCfgDto dto) {
        PageHelper.startPage(dto.getPageNum(), dto.getPageSize());
        return purmsEmpnumCfgReadMapper.queryList(dto);
    }

    @Override
    public List<PurmsEmpnumCfgVo> queryCfgList(PurmsEmpnumCfgDto dto) {
        return purmsEmpnumCfgReadMapper.queryCfgList(dto);
    }
}

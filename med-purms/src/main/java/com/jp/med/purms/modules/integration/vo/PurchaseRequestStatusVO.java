package com.jp.med.purms.modules.integration.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 采购申请状态VO
 */
@Data
@ApiModel("采购申请状态VO")
public class PurchaseRequestStatusVO {

	@ApiModelProperty("申请ID")
	private String requestId;

	@ApiModelProperty("申请名称")
	private String requestName;

	@ApiModelProperty("申请时间")
	private String requestTime;

	@ApiModelProperty("申请状态 (0: 待审核, 1: 审核通过, 2: 审核拒绝, 3: 已取消)")
	private String status;

	@ApiModelProperty("状态描述")
	private String statusDescription;

	@ApiModelProperty("申请部门")
	private String department;

	@ApiModelProperty("申请人")
	private String applicant;

	@ApiModelProperty("总金额")
	private BigDecimal totalAmount;

	@ApiModelProperty("审核批次号")
	private String auditBatchNo;

	@ApiModelProperty("审核时间")
	private String auditTime;

	@ApiModelProperty("审核人")
	private String auditor;

	@ApiModelProperty("审核意见")
	private String auditOpinion;
}
package com.jp.med.erp.modules.vcrGen.vo;

import lombok.Data;

/**
 * 凭证预览映射表
 * <AUTHOR>
 * @email -
 * @date 2025-03-11 17:00:33
 */
@Data
public class ErpVcrPreviewVo {

	/** id */
	private Integer id;

	/** 支付类型(先确定大类supType) */
	private String payType;

	/** 类型id */
	private Integer moduleId;

	/** 大类型 1：费用报销 2：药品 3：工资 4：折旧 (根据需求新增) */
	private String supType;

	/** 创建人 */
	private String crter;

	/** 创建时间 */
	private String createTime;

	/** 更新者 */
	private String updter;

	/** 更新时间 */
	private String updateTime;

	/** 是否推送 **/
	private String pushFlag;

	/** hrp凭证号 **/
	private String vpzh;

}

<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.jp.med.bms.modules.dispose.mapper.read.BmsDisposeBudgetTypeReadMapper">

    <!--查询所有数据-->
    <select id="queryList" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTypeVo">
        select	DISTINCT
            a.budget_type_id as budgetTypeId, <!--预算编制项类别ID-->
            a.budget_type_code as budgetTypeCode, <!--预算编制项类别编码-->
            a.budget_type_name as budgetTypeName, <!--预算编制项类别名称-->
            a.budget_type_parent_id as budgetTypeParentId, <!--上级编码-->
            b.budget_type_name as budgetTypeParentName, <!--上级编制项名称-->
            a.hospital_id as hospitalId, <!--医疗机构编码-->
            a.flag as flag
        from
            <choose>
                <when test="budgetTypeName != '' and budgetTypeName != null">
                    (select
                    x.*
                    from
                    (with RECURSIVE temp AS (
                    select * from bms_budget_type r
                    <where>
                        <if test="budgetTypeName != '' and budgetTypeName != null">
                            budget_type_name like CONCAT('%',#{budgetTypeName,jdbcType=VARCHAR},'%')
                        </if>
                    </where>
                    UNION ALL
                    SELECT b.* from bms_budget_type b, temp t where b.budget_type_parent_id = t.budget_type_code
                    )
                    select * from temp) x
                    union all
                    select
                    y.* from
                    (with RECURSIVE temp AS (
                    select * from bms_budget_type r
                    <where>
                        <if test="budgetTypeName != '' and budgetTypeName != null">
                            budget_type_name like CONCAT('%',#{budgetTypeName,jdbcType=VARCHAR},'%')
                        </if>
                    </where>
                    UNION ALL
                    SELECT b.* from bms_budget_type b, temp t where b.budget_type_code = t.budget_type_parent_id
                    )
                    select * from temp) y) a
                </when>
                <otherwise>
                    bms_budget_type a
                </otherwise>
            </choose>
        left join bms_budget_type b on a.budget_type_parent_id = b.budget_type_code
    </select>

    <!--查询编码是否已经存在-->
    <select id="queryByCode" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTypeVo">
        select
            a.budget_type_id as budgetTypeId, <!--预算编制项类别ID-->
            a.budget_type_code as budgetTypeCode, <!--预算编制项类别编码-->
            a.budget_type_name as budgetTypeName, <!--预算编制项类别名称-->
            a.budget_type_parent_id as budgetTypeParentId, <!--上级编码-->
            a.hospital_id as hospitalId, <!--医疗编码-->
            a.flag as flag
        from bms_budget_type a
        <where>
            <if test="budgetTypeCode != '' and budgetTypeCode != null">
                and a.budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
            </if>
        </where>
    </select>

    <select id="querySelectTree" resultType="com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTypeVo">
        select
            a.budget_type_id as budgetTypeId, <!--预算编制项类别ID-->
            a.budget_type_code as budgetTypeCode, <!--预算编制项类别编码-->
            a.budget_type_name as budgetTypeName, <!--预算编制项类别名称-->
            a.budget_type_parent_id as budgetTypeParentId, <!--上级编码-->
            a.hospital_id as hospitalId, <!--医疗机构编码-->
            a.flag as flag
            <if test="budgetTypeCode != '' and budgetTypeCode != null">
                ,case when b.budget_type_id is not null then true else false end as disabled <!--是否禁用-->
            </if>
            from bms_budget_type a
            <if test="budgetTypeCode != '' and budgetTypeCode != null">
                left join
                (select
                x.*
                from
                (with RECURSIVE temp AS (
                select * from bms_budget_type r
                <where>
                    <if test="budgetTypeCode != '' and budgetTypeCode != null">
                        budget_type_code = #{budgetTypeCode,jdbcType=VARCHAR}
                    </if>
                </where>
                UNION ALL
                SELECT b.* from bms_budget_type b, temp t where b.budget_type_parent_id = t.budget_type_code
                )
                select * from temp) x) b
                on a.budget_type_id = b.budget_type_id
            </if>
    </select>

</mapper>
package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetWorkloadDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetResultsDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetResultsVo;
import com.jp.med.bms.modules.dispose.vo.TitleVo;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 预算编制数据
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
@Mapper
public interface BmsExecuteBudgetResultsReadMapper extends BaseMapper<BmsExecuteBudgetResultsDto> {

    /**
     * 查询编制结果
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetResultsVo> queryList(BmsExecuteBudgetResultsDto dto);

    /**
     * 查询科室预算
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetResultsVo> queryDeptBudget(BmsExecuteBudgetResultsDto dto);

    /**
     * 查询科室预算(汇总的)
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetResultsVo> queryDeptBudget2(BmsExecuteBudgetResultsDto dto);

    /**
     * 查询预算报表推进进度
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetResultsVo> queryBudgetReport(BmsExecuteBudgetDataDto dto);


    /**
     * 查询预算报表表头
     * @param dto
     * @return
     */
    List<TitleVo> queryTitle(BmsExecuteBudgetDataDto dto);

    /**
     * 查询预算项目汇总
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetResultsVo> queryBudgetSummary(BmsBudgetWorkloadDto dto);

    List<BmsExecuteBudgetResultsVo> queryListNew(BmsExecuteBudgetResultsDto dto);

    /**
     * 查询预算结果表
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetResultsVo> queryBudgetResList(BmsExecuteBudgetResultsDto dto);
}

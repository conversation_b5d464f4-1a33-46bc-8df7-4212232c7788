package com.jp.med.bms.modules.dispose.controller;

import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetApportionDto;
import com.jp.med.bms.modules.dispose.service.read.BmsDisposeBudgetApportionReadService;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetApportionWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 预算编制项分配表
 * <AUTHOR>
 * @email -
 * @date 2023-04-21 18:02:22
 */
@Api(value = "预算编制项分配表", tags = "预算编制项分配表")
@RestController
@RequestMapping("dispose/budgetApportion")
public class BmsDisposeBudgetApportionController {

    @Autowired
    private BmsDisposeBudgetApportionReadService bmsDisposeBudgetApportionReadService;

    @Autowired
    private BmsDisposeBudgetApportionWriteService bmsDisposeBudgetApportionWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算编制项分配表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsDisposeBudgetApportionDto dto){
        return CommonResult.paging(bmsDisposeBudgetApportionReadService.queryList(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算编制项分配表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsDisposeBudgetApportionDto dto){
        bmsDisposeBudgetApportionWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算编制项分配表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsDisposeBudgetApportionDto dto){
        bmsDisposeBudgetApportionWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算编制项分配表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsDisposeBudgetApportionDto dto){
        bmsDisposeBudgetApportionWriteService.removeById(dto);
        return CommonResult.success();
    }

}

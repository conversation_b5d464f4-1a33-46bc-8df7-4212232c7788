package com.jp.med.bms.modules.dispose.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTypeDto;
import com.jp.med.bms.modules.dispose.vo.BmsDisposeBudgetTypeVo;

import java.util.List;

/**
 * 预算编制项类别
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-10 15:11:57
 */
public interface BmsDisposeBudgetTypeReadService extends IService<BmsDisposeBudgetTypeDto> {
    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsDisposeBudgetTypeVo> queryList(BmsDisposeBudgetTypeDto dto);

    /**
     *查询上级组织下拉树
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetTypeVo> querySelectTree(BmsDisposeBudgetTypeDto dto);

    /**
     * 查询编码是否以及存在
     * @param dto
     * @return
     */
    List<BmsDisposeBudgetTypeVo> queryByCode(BmsDisposeBudgetTypeDto dto);
}


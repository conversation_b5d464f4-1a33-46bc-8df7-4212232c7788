package com.jp.med.bms.modules.bmsConfig.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 信息化系统预算配置
 * <AUTHOR>
 * @email -
 * @date 2023-11-15 10:39:28
 */
@Data
@TableName("bms_engine_cfg" )
public class BmsEngineCfgDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 信息设备编码 */
    @TableField(value = "engine_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String engineCode;

    /** 信息设备名称 */
    @TableField(value = "engine_name",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String engineName;

    /** 医疗机构编码 */
    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

    /** 有效标志 */
    @TableField(value = "active_flag",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String activeFlag;

    /** 类型 */
    @TableField(value = "type",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String type;

}

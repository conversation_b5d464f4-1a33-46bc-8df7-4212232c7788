package com.jp.med.bms.modules.bmsExecute.mapper.read;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetHouseDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsBudgetHouseVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 工程类政府采购预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 20:34:43
 */
@Mapper
public interface BmsBudgetHouseReadMapper extends BaseMapper<BmsBudgetHouseDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsBudgetHouseVo> queryList(BmsBudgetHouseDto dto);
}

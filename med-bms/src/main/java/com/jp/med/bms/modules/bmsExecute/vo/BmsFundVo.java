package com.jp.med.bms.modules.bmsExecute.vo;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 科研教学经费预算
 * <AUTHOR>
 * @email -
 * @date 2023-10-30 19:57:30
 */
@Data
public class BmsFundVo {

	/** ID */
	private Integer id;

	/** 科室 */
	private String dept;

	/** 科室名称 */
	private String orgName;

	/** 教学经费 */
	private BigDecimal teachFund;

	/** 科研经费 */
	private BigDecimal studyFund;

	/** 预算数 */
	private BigDecimal budgetAmount;

	/** 科室预算总和 */
	private BigDecimal budgetAmountSum;

	/** 备注 */
	private String memo;

	/** 预算任务 */
	private String taskCode;

	/** 预算名称 */
	private String taskName;

	/** 审核状态(0:未审核 1:已审核) */
	private String chk;

}

package com.jp.med.bms.modules.dispose.service.write.impl;

import com.jp.med.common.exception.AppException;
import com.jp.med.common.util.BatchUtil;
import com.jp.med.common.util.EasyPoiUtil;
import com.jp.med.common.util.TreeUtil;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.factory.annotation.Autowired;


import com.jp.med.bms.modules.dispose.mapper.write.BmsDisposeBudgetProjWriteMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetProjDto;
import com.jp.med.bms.modules.dispose.service.write.BmsDisposeBudgetProjWriteService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.lang.reflect.InvocationTargetException;
import java.util.List;

@Transactional(readOnly = false)
@Service
public class BmsDisposeBudgetProjWriteServiceImpl extends ServiceImpl<BmsDisposeBudgetProjWriteMapper, BmsDisposeBudgetProjDto> implements BmsDisposeBudgetProjWriteService {
    @Autowired
    private BmsDisposeBudgetProjWriteMapper bmsDisposeBudgetProjWriteMapper;

    @Override
    public void updateBudgetProj(BmsDisposeBudgetProjDto dto) {
        bmsDisposeBudgetProjWriteMapper.updateBudgetProj(dto);
    }

    @Override
    public void saveBudgetProj(BmsDisposeBudgetProjDto dto) {
        bmsDisposeBudgetProjWriteMapper.saveBudgetProj(dto);
    }

    @Override
    public void deleteBudgetProj(BmsDisposeBudgetProjDto dto) {
        bmsDisposeBudgetProjWriteMapper.deleteBudgetProj(dto);
    }

    @Override
    public void upload(BmsDisposeBudgetProjDto dto, MultipartFile[] files) {
        try {
            List<BmsDisposeBudgetProjDto> dtos = EasyPoiUtil.importExcel(files[0], BmsDisposeBudgetProjDto.class);
            TreeUtil.checkRing(dtos,"budgetCode", "budgetParentId");
            BatchUtil.batch("saveBudgetProj", dtos, BmsDisposeBudgetProjWriteMapper.class);
        } catch (IOException e) {
            throw new AppException("文件解析失败");
        } catch (InvocationTargetException | NoSuchMethodException | IllegalAccessException e) {
            throw new RuntimeException(e);
        }
    }
}
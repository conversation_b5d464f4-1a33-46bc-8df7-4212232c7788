package com.jp.med.bms.modules.bmsOrg.dto;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import com.jp.med.common.dto.common.CommonQueryDto;

import lombok.Data;

/**
 * 用户科室映射
 * <AUTHOR>
 * @email -
 * @date 2023-11-03 09:27:54
 */
@Data
@TableName("bms_emp_mapping" )
public class BmsEmpMappingDto extends CommonQueryDto {

    /** ID */
    @TableId("id")
    private Integer id;

    /** 员工编号 */
    @TableField(value = "emp_code",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String empCode;

    /** 人力资源科室ID */
    @TableField(value = "hrm_org_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hrmOrgId;

    /** 预算管理科室ID */
    @TableField(value = "bms_org_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String bmsOrgId;

    /** 医疗机构编码 */
    @TableField(value = "hospital_id",insertStrategy = FieldStrategy.NOT_EMPTY)
    private String hospitalId;

}

package com.jp.med.bms.modules.dispose.mapper.write;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetTableProjDto;
import org.apache.ibatis.annotations.Mapper;

/**
 * 预算对应编制项目
 * <AUTHOR>
 * @email -
 * @date 2023-05-31 11:35:42
 */
@Mapper
public interface BmsDisposeBudgetTableProjWriteMapper extends BaseMapper<BmsDisposeBudgetTableProjDto> {

    /**
     * 根据查询写入
     * @param dto
     */
    void insertTableProjBySelect(BmsDisposeBudgetTableProjDto dto);

    /**
     * 修改项目
     * @param dto
     */
    void updateTableProj(BmsDisposeBudgetTableProjDto dto);
}

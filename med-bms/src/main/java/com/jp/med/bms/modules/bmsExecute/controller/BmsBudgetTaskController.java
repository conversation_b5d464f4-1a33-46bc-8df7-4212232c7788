package com.jp.med.bms.modules.bmsExecute.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsBudgetTaskDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsBudgetTaskReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsBudgetTaskWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 预算任务表
 * <AUTHOR>
 * @email -
 * @date 2023-10-19 17:18:28
 */
@Api(value = "预算任务表", tags = "预算任务表")
@RestController
@RequestMapping("bmsBudgetTask")
public class BmsBudgetTaskController {

    @Autowired
    private BmsBudgetTaskReadService bmsBudgetTaskReadService;

    @Autowired
    private BmsBudgetTaskWriteService bmsBudgetTaskWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算任务表")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsBudgetTaskDto dto){
        return CommonResult.paging(bmsBudgetTaskReadService.queryList(dto));
    }

    @ApiOperation("查询任务执行详情")
    @PostMapping("/queryTaskFlowDetail")
    public CommonResult<?> queryTaskFlowDetail(@RequestBody BmsBudgetTaskDto dto){
        return CommonResult.success(bmsBudgetTaskReadService.queryTaskFlowDetail(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算任务表")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsBudgetTaskDto dto){
        bmsBudgetTaskWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算任务表")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsBudgetTaskDto dto){
        bmsBudgetTaskWriteService.updateById(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算任务表")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsBudgetTaskDto dto){
        bmsBudgetTaskWriteService.removeById(dto);
        return CommonResult.success();
    }

}

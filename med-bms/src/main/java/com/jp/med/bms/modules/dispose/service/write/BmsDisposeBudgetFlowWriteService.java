package com.jp.med.bms.modules.dispose.service.write;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.dispose.dto.BmsDisposeBudgetFlowDto;

/**
 * 预算编制流程
 *
 * <AUTHOR>
 * @email -
 * @date 2023-04-19 17:21:14
 */
public interface BmsDisposeBudgetFlowWriteService extends IService<BmsDisposeBudgetFlowDto> {
    /**
     * 修改
     * @param dto
     */
    void updateBudgetFlow(BmsDisposeBudgetFlowDto dto);

    /**
     * 新增
     * @param dto
     */
    void saveBudgetFlow(BmsDisposeBudgetFlowDto dto);

    /**
     * 删除
     * @param dto
     */
    void deleteBudgetFlow(BmsDisposeBudgetFlowDto dto);

    /**
     * 流程启动
     * @param dto
     */
    void initiateProcess(BmsDisposeBudgetFlowDto dto);
}


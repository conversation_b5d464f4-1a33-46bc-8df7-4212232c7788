package com.jp.med.bms.modules.bmsExecute.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetDataDto;
import com.jp.med.bms.modules.bmsExecute.service.read.BmsExecuteBudgetDataReadService;
import com.jp.med.bms.modules.bmsExecute.service.write.BmsExecuteBudgetDataWriteService;
import com.jp.med.common.entity.common.CommonResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;



/**
 * 预算编制数据
 * <AUTHOR>
 * @email -
 * @date 2023-04-26 15:40:51
 */
@Api(value = "预算编制数据", tags = "预算编制数据")
@RestController
@RequestMapping("bmsExecute/budgetData")
public class BmsExecuteBudgetDataController {

    @Autowired
    private BmsExecuteBudgetDataReadService bmsExecuteBudgetDataReadService;

    @Autowired
    private BmsExecuteBudgetDataWriteService bmsExecuteBudgetDataWriteService;

    /**
     * 列表
     */
    @ApiOperation("查询预算编制数据")
    @PostMapping("/list")
    public CommonResult<?> list(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.queryList(dto));
    }

    @ApiOperation("查询预算数据明细")
    @PostMapping("/queryDataDetail")
    public CommonResult<?> queryDataDetail(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.queryDataDetail(dto));
    }

    @ApiOperation("页面初始化")
    @PostMapping("/queryPageInit")
    public CommonResult<?> queryPageInit(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.queryPageInit(dto));
    }

    @ApiOperation("查询编制项目排序规则")
    @PostMapping("/queryBudgetOrder")
    public CommonResult<?> queryBudgetOrder(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.queryBudgetOrder(dto));
    }

    /**
     * 保存
     */
    @ApiOperation("新增预算编制数据")
    @PostMapping("/save")
    public CommonResult<?> save(@RequestBody BmsExecuteBudgetDataDto dto){
        bmsExecuteBudgetDataWriteService.save(dto);
        return CommonResult.success();
    }

    /**
     * 修改
     */
    @ApiOperation("修改预算编制数据")
    @PutMapping("/update")
    public CommonResult<?> update(@RequestBody BmsExecuteBudgetDataDto dto){
        bmsExecuteBudgetDataWriteService.updateData(dto);
        return CommonResult.success();
    }

    /**
     * 重置为可重新填报
     */
    @ApiOperation("重置为可重新填报")
    @PutMapping("/resetStatus")
    public CommonResult<?> resetStatus(@RequestBody BmsExecuteBudgetDataDto dto){
        bmsExecuteBudgetDataWriteService.resetStatus(dto);
        return CommonResult.success();
    }

    /**
     * 删除
     */
    @ApiOperation("删除预算编制数据")
    @DeleteMapping("/delete")
    public CommonResult<?> delete(@RequestBody BmsExecuteBudgetDataDto dto){
        bmsExecuteBudgetDataWriteService.removeById(dto);
        return CommonResult.success();
    }


    @ApiOperation("查询编制结果")
    @PostMapping("/queryResult")
    public CommonResult<?> queryResult(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.queryData(dto));
    }

    @ApiOperation("查询预算汇总数据")
    @PostMapping("/queryBudgetSummary")
    public CommonResult<?> queryBudgetSummary(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.queryBudgetSummary(dto));
    }

    @ApiOperation("查询预算汇总报表数据")
    @PostMapping("/queryBudgetReportSummary")
    public CommonResult<?> queryBudgetReportSummary(@RequestBody BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.queryBudgetReportSummary(dto));
    }

    @ApiOperation("上传文件")
    @PostMapping("/upload")
    public CommonResult<?> upload(BmsExecuteBudgetDataDto dto){
        return CommonResult.success(bmsExecuteBudgetDataReadService.upload(dto));
    }
}

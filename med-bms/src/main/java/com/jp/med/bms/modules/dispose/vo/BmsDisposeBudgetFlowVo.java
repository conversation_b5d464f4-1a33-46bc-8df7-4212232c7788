package com.jp.med.bms.modules.dispose.vo;

import lombok.Data;

/**
 * 预算编制流程
 * 
 * <AUTHOR>
 * @email -
 * @date 2023-04-19 17:21:14
 */
@Data
public class BmsDisposeBudgetFlowVo {

	/** 流程ID */
	private Integer budgetFlowId;

	/** 预算编制流程编码 */
	private String budgetFlowCode;

	/** 预算编制流程名称 */
	private String budgetFlowName;

	/** 医疗机构编码 */
	private String hospitalId;

	/** 备注 */
	private String remarks;

	/** 状态 */
	private String flag;

	/**流程图*/
	private String flowData;

	/** 创建时间 */
	private String createTime;

	/** 预算编制表 */
	private Integer budgetTableId;

	private String budgetTaskCode;
	private Integer budgetTaskId;
	private String budgetTaskName;

}

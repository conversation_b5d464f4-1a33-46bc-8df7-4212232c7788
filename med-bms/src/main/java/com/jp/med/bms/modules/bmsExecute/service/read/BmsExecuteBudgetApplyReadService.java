package com.jp.med.bms.modules.bmsExecute.service.read;

import com.baomidou.mybatisplus.extension.service.IService;
import com.jp.med.bms.modules.bmsExecute.dto.BmsExecuteBudgetApplyDto;
import com.jp.med.bms.modules.bmsExecute.vo.BmsExecuteBudgetApplyVo;

import java.util.List;
import java.util.Map;

/**
 * 预算调整申请表
 * <AUTHOR>
 * @email -
 * @date 2023-06-01 14:47:42
 */
public interface BmsExecuteBudgetApplyReadService extends IService<BmsExecuteBudgetApplyDto> {

    /**
     * 查询列表
     * @param dto
     * @return
    */
    List<BmsExecuteBudgetApplyVo> queryList(BmsExecuteBudgetApplyDto dto);

    /**
     * 预算调整申请页面初始化
     * @param dto
     * @return
     */
    Map<String,Object> queryModalInit(BmsExecuteBudgetApplyDto dto);

    /**
     * 查询预算审核
     * @param dto
     * @return
     */
    List<BmsExecuteBudgetApplyVo> queryAdjustAudit(BmsExecuteBudgetApplyDto dto);
}


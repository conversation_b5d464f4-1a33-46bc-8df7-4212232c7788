package com.jp.med.bms.modules.analysis.service.read;

import com.jp.med.bms.modules.analysis.dto.BmsAnalysisDto;
import com.jp.med.bms.modules.analysis.vo.BmsAnalysisVo;

import java.util.List;

public interface BmsAnalysisReadService {

    /**
     * 查询服务质量
     * @param dto
     * @return
     */
    List<BmsAnalysisVo> queryQosAnalysis(BmsAnalysisDto dto);

    /**
     * 查询服务质量(门诊)
     * @param dto
     * @return
     */
    List<BmsAnalysisVo> queryMZQosAnalysis(BmsAnalysisDto dto);

    /**
     * 查询收入预算分析
     * @param dto
     * @return
     */
    List<BmsAnalysisVo> queryIncomeAnalysis(BmsAnalysisDto dto);
}

package com.jp.med.bms.modules.config.service.write.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.jp.med.bms.modules.config.dto.BmsBudgetDeptMappingDto;
import com.jp.med.bms.modules.config.mapper.write.BmsBudgetDeptMappingWriteMapper;
import com.jp.med.bms.modules.config.service.write.BmsBudgetDeptMappingWriteService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 预算科室映射
 * <AUTHOR>
 * @email -
 * @date 2025-03-25 11:00:18
 */
@Service
@Transactional(readOnly = false)
public class BmsBudgetDeptMappingWriteServiceImpl extends ServiceImpl<BmsBudgetDeptMappingWriteMapper, BmsBudgetDeptMappingDto> implements BmsBudgetDeptMappingWriteService {
}
